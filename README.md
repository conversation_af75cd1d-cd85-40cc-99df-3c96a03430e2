# Vita 前端应用

基于 Turborepo 的多端应用，支持小程序、H5 和原生应用。


## 技术栈

- **框架**:
    - React 18
    - React Native
    - TypeScript 5
- **构建工具**:
    - Turborepo - 单体仓库管理
    - Webpack - 应用构建
- **代码质量**:
    - eslint - JavaScript/TypeScript linter
    - TypeScript - 类型检查
    - stylelint - CSS linter
- **状态管理**:
    - Jotai - 原子化状态管理

## 项目结构

```
├── apps // 应用
│   ├── docs // 文档
│   ├── mini-app // 小程序 & H5
│   └── native-app // 原生应用
├── package.json
├── packages
│   ├── events-dispatch // 事件总成  （class 实现，各场景实例化使用）
│   ├── pages-common // 通用页面
│   ├── pages-wenzhen // 问诊业务页面
│   ├── ui-cards-common // 通用消息卡片
│   ├── ui-cards-wenzhen // 问诊消息卡片
│   ├── ui-components-shared // 通用组件
│   └── utils-shared // 通用工具
│       ├── logger  // 日志工具
│       ├── monitor // 监控
│       ├── navigate // 跳转 （class 实现，各场景实例化使用）
│       └── request  // 请求 （class 实现，各场景实例化使用）
├── pnpm-lock.yaml
├── pnpm-workspace.yaml
└── turbo.json
```

## 快速开始

### 环境要求

- Node.js >= 18
- pnpm >= 8.0
- iOS/Android 开发环境（如需运行原生应用）

### 安装依赖

```bash
pnpm install
```

### 开发命令

```bash
# 开发 H5
pnpm dev:h5

# 开发小程序
pnpm dev:mini

# 开发原生应用
pnpm dev:native

# 构建所有应用
pnpm build

# 代码检查
pnpm lint
```

## 开发规范

### 包命名规范

所有 packages 使用 `@baidu/vita/*` 作为前缀

### 代码规范

- 使用 TypeScript 进行开发
- 使用 oxlint 进行代码质量检查
- 遵循 React 最佳实践

### Git 提交规范

本项目使用 [Conventional Commits](https://www.conventionalcommits.org/)
规范，提交信息需要符合以下格式：

```bash
<type>(<scope>): <subject>

<body>

<footer>
```

### Type 类型

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式（不影响代码运行的变动）
- `refactor`: 重构（既不是新增功能，也不是修改 bug 的代码变动）
- `perf`: 性能优化
- `test`: 增加测试
- `chore`: 构建过程或辅助工具的变动
- `revert`: 回退
- `ci`: CI 相关变动
- `build`: 影响项目构建或依赖项修改

### Scope 范围

- 影响范围，例如：`feat(auth)`、`fix(api)`、`style(ui)` 等
- 如果影响多个范围，可以使用 `*` 表示，例如：`feat(*)`

### Subject 主题

- 简短描述，不超过 50 个字符
- 以动词开头，使用第一人称现在时
- 第一个字母小写
- 结尾不加句号

### Body 正文

- 详细描述，可以分多行
- 说明代码变动的动机，以及与以前行为的对比

### Footer 页脚

- 不兼容变动：以 `BREAKING CHANGE:` 开头
- 关闭 Issue：`Closes #123, #456`

### 示例

```bash
# 新功能
feat(auth): add login with google

# 修复 bug
fix(api): handle null response from server

# 文档更新
docs(readme): update installation guide

# 代码格式
style(ui): format button component

# 重构
refactor(store): optimize state management

# 性能优化
perf(render): reduce unnecessary re-renders

# 测试
test(utils): add unit tests for helpers

# 构建
chore(deps): update dependencies

# 回退
revert: feat(auth): remove google login

# CI
ci(github): add automated tests

# 构建
build(webpack): update configuration
```

### 提交检查

项目使用 `@commitlint/cli` 和 `@commitlint/config-conventional`
进行提交信息检查，不符合规范的提交将被拒绝。

### 自动修复

如果您使用 VS Code，建议安装
[Commit Message Editor](https://marketplace.visualstudio.com/items?itemName=adamvoss.vscode-languagetool)
插件，它可以帮助您自动生成符合规范的提交信息。

## 项目特性

- 📱 多端统一：Web、小程序、原生应用
- 🚀 Turborepo 提供的高效构建和依赖管理
- 💡 TypeScript 保证代码质量
- ⚡️ oxlint 提供快速的代码检查
- 🎯 Jotai 实现高效的状态管理
- 📦 模块化的包结构

## 开发指南

### 新增功能

1. 确定功能所属的包
2. 在相应的包中开发
3. 使用 TypeScript 编写代码
4. 添加必要的测试
5. 提交代码前运行 lint 检查

### 调试

- Web/H5：使用浏览器开发工具
- 小程序：使用小程序开发者工具
- 原生应用：使用 React Native 调试工具

## 构建与部署

### 构建流程

```bash
# 构建所有应用
pnpm build

# 构建特定应用
pnpm build --filter=@baidu/vita-mini-app
pnpm build --filter=@baidu/vita-native-app
```

### 部署流程

根据不同平台选择相应的发布流程：

- H5：部署到 Web 服务器
- 小程序：提交小程序平台审核
- 原生应用：提交应用商店审核

## 常见问题

### 开发环境配置

1. 确保已安装所需的全局依赖：

```bash
npm install -g pnpm
pnpm install -g turbo
```

2. 配置环境变量（如需要）

### 调试技巧

- 使用 React DevTools 进行组件调试
- 使用 Jotai DevTools 调试状态
- 开启 TypeScript 严格模式进行类型检查

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交变更
4. 推送到分支
5. 创建 Pull Request

## 相关资源

- [React 文档](https://react.dev)
- [React Native 文档](https://reactnative.dev)
- [TypeScript 文档](https://www.typescriptlang.org)
- [Jotai 文档](https://jotai.org)
- [Turborepo 文档](https://turbo.build)

## 许可证

MIT License
