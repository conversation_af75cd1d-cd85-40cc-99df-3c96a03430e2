{"name": "@baidu/webpack-alias-resolver", "version": "1.0.0", "description": "Webpack plugin for resolving aliases in monorepo packages", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "prepublishOnly": "npm run build"}, "keywords": ["webpack", "plugin", "alias", "monorepo"], "dependencies": {"enhanced-resolve": "^5.15.0"}, "peerDependencies": {"@tarojs/service": "^3.6.0", "webpack": "^5.0.0", "webpack-chain": "^6.5.1"}, "devDependencies": {"@tarojs/service": "^3.6.0", "@types/node": "^18.0.0", "@types/webpack": "^5.0.0", "@types/webpack-chain": "5.2.0", "typescript": "^5.0.0", "webpack": "^5.0.0", "webpack-chain": "5.2.0"}}