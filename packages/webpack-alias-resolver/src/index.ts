import type { IPluginContext } from '@tarojs/service';
import type { WebpackChain } from 'webpack-chain';
import { Resolver } from 'enhanced-resolve';
import { resolve as pathResolve } from 'path';
import { existsSync, readFileSync } from 'fs';

interface MonorepoAliasResolverOptions {
  workspaceRoot?: string;
}

const monorepoAliasResolver = (ctx: IPluginContext) => {
  const workspaceRoot = process.cwd();
  const packageAliases = new Map<string, Record<string, string>>();

  // 初始化包别名
  const initializePackageAliases = () => {
    const workspaceConfigPath = pathResolve(workspaceRoot, 'pnpm-workspace.yaml');
    if (!existsSync(workspaceConfigPath)) {
      return;
    }

    const packagesDir = pathResolve(workspaceRoot, 'packages');
    if (existsSync(packagesDir)) {
      const packages = require('fs').readdirSync(packagesDir);
      
      for (const pkg of packages) {
        const packageJsonPath = pathResolve(packagesDir, pkg, 'package.json');
        if (!existsSync(packageJsonPath)) continue;

        try {
          const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
          const tsConfigPath = pathResolve(packagesDir, pkg, 'tsconfig.json');
          
          if (existsSync(tsConfigPath)) {
            const tsConfig = JSON.parse(readFileSync(tsConfigPath, 'utf-8'));
            const paths = tsConfig.compilerOptions?.paths || {};
            const baseUrl = tsConfig.compilerOptions?.baseUrl || '.';
            
            const resolvedAliases: Record<string, string> = {};
            
            Object.entries(paths).forEach(([key, value]) => {
              const aliasKey = key.replace(/\/\*$/, '');
              const aliasValue = pathResolve(
                packagesDir,
                pkg,
                baseUrl,
                (value as string[])[0].replace(/\/\*$/, '')
              );
              resolvedAliases[aliasKey] = aliasValue;
            });

            if (Object.keys(resolvedAliases).length > 0) {
              packageAliases.set(packageJson.name, resolvedAliases);
            }
          }
        } catch (error) {
          console.error(`Error processing package ${pkg}:`, error);
        }
      }
    }
  };

  ctx.modifyWebpackChain(({ chain }: { chain: WebpackChain }) => {
    initializePackageAliases();

    chain.resolve.plugin('monorepo-alias-resolver')
      .use({
        apply(resolver: Resolver) {
          const target = resolver.ensureHook('resolve');
          
          resolver.getHook('described-resolve').tapAsync(
            'MonorepoAliasResolver',
            (request: any, resolveContext: any, callback: any) => {
              if (!request.request || !request.context) {
                return callback();
              }

              const moduleMatch = request.context.match(/node_modules[/\\](@[^/\\]+[/\\][^/\\]+|[^/\\]+)/);
              if (!moduleMatch) {
                return callback();
              }

              const packageName = moduleMatch[1];
              const aliases = packageAliases.get(packageName);
              
              if (!aliases) {
                return callback();
              }

              const matchedAlias = Object.entries(aliases).find(([key]) => 
                request.request.startsWith(key)
              );

              if (matchedAlias) {
                const [aliasKey, aliasValue] = matchedAlias;
                const newRequest = request.request.replace(aliasKey, aliasValue);
                
                return resolver.doResolve(
                  target,
                  {
                    ...request,
                    request: newRequest
                  },
                  `Resolved alias ${aliasKey} to ${aliasValue}`,
                  resolveContext,
                  callback
                );
              }

              return callback();
            }
          );
        }
      });
  });
};

export default {
  name: '@baidu/webpack-alias-resolver',
  fn: monorepoAliasResolver
}; 