# Monorepo Alias Resolver Webpack Plugin

这个 Webpack 插件用于解决 monorepo 项目中不同包之间的别名路径解析问题。它可以自动识别并解析每个包中定义的 TypeScript 路径别名。

## 安装

```bash
npm install @vita/webpack-alias-resolver --save-dev
# 或
pnpm add -D @vita/webpack-alias-resolver
```

## 使用方法

在你的 Webpack 配置文件中：

```javascript
const MonorepoAliasResolver = require('@vita/webpack-alias-resolver');

module.exports = {
  // ...其他配置
  resolve: {
    plugins: [
      new MonorepoAliasResolver({
        // 可选：指定工作区根目录，默认为 process.cwd()
        workspaceRoot: process.cwd()
      })
    ]
  }
};
```

## 工作原理

1. 插件会自动扫描 workspace 中的所有包
2. 读取每个包的 `tsconfig.json` 中定义的 `paths` 配置
3. 将这些路径别名转换为对应包的实际路径
4. 在 Webpack 解析模块时，自动处理这些别名路径

## 示例

假设你的 monorepo 结构如下：

```
my-monorepo/
  ├── packages/
  │   ├── package-a/
  │   │   ├── tsconfig.json  // 包含 "@components/*" -> "./src/components/*" 的配置
  │   │   └── src/
  │   └── package-b/
  │       └── src/
  └── apps/
      └── main-app/
```

在 package-a 的代码中使用了别名：

```typescript
// package-a/src/index.ts
import { Button } from '@components/Button';
```

当 main-app 引用 package-a 时，插件会自动解析 `@components` 到正确的路径。

## 注意事项

1. 确保每个包都有正确配置的 `tsconfig.json`
2. 插件会自动读取 `pnpm-workspace.yaml` 来确定包的位置
3. 建议在开发环境中使用 `pnpm link` 或 workspace 功能进行本地开发 