import {View} from '@tarojs/components';
import {HTags, HTabs} from '@baidu/health-ui';

import styles from './index.module.less';

const list = [
    {
        text: '标签1',
        value: '1'
    },
    {
        text: '标签2',
        value: '2'
    },
    {
        text: '标签3',
        value: '3'
    }
];
const ImDemo = () => {
    return (
        <>
            <View className={styles.imText}>么么哒</View>
            {/* 集成health-ui 分开引入 */}
            <HTabs selectedIndex={0} activeLineMode='auto' activatedType='line'>
                <HTabs.TabPane title='标题一' />
                <HTabs.TabPane title='标题二' />
                <HTabs.TabPane title='标题三' />
            </HTabs>
            <HTags list={list} />
        </>
    );
};

export default ImDemo;
