import type {Fields} from '../index.d';

/**
 * 验证字段是否符合规则
 *
 * @param value 要验证的值
 * @param rules 验证规则，为null或undefined时，不进行任何验证，直接返回true
 * @returns 验证结果，包括isValid（布尔值，表示是否验证通过）和message（字符串，验证失败时的错误信息）
 */
function validateField(value, rules) {
    if (!rules) {
        return {isValid: true, message: ''};
    }

    for (const rule of rules) {
        if (rule.type === 'required' && !value && value !== 0) {
            return {
                isValid: false,
                message: rule.message
            };
        }
        if (rule.type === 'min' && value !== '' && Number(value) < Number(rule.value)) {
            return {
                isValid: false,
                message: rule.message
            };
        }
        if (rule.type === 'max' && value !== '' && Number(value) > Number(rule.value)) {
            return {
                isValid: false,
                message: rule.message
            };
        }
    }

    return {isValid: true, message: ''};
}

/**
 * 验证表单
 *
 * @param fields 表单字段数组
 * @param formValues 表单值对象
 * @returns 验证结果对象，包含isValid和firstError属性
 */
export function validateForm(
    fields: Fields[],
    formValues: Record<string, any>
): {
    isValid: boolean;
    firstError: string;
} {
    for (const item of fields) {
        const value = formValues[item.key];
        const validation = validateField(value, item.rules);

        if (!validation.isValid) {
            return {isValid: false, firstError: validation.message};
        }
    }

    return {isValid: true, firstError: ''};
}
