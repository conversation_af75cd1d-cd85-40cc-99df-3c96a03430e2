import type {ReactNode} from 'react';

export interface ImFormCardProps {
    data: {
        content: ImFormCardData;
    };
    msgId: string;
    cardId?: string;
    cardName?: string;
    ext?: {
        [key: string]: string | number;
    };
}

export interface ImFormCardData {
    intentDes?: string;
    userSubmitMsg?: string;
    disabled: boolean;
    title: string;
    subtitle: string;
    fields: Fields[];
    footer: Footer;
}

interface Buttons {
    type: string;
    value: string;
    interaction: string;
    interactionInfo: InteractionInfo;
    disabled: boolean;
}

interface Config {
    type: string;
    minDate?: string;
    maxDate?: string;
}

interface Description {
    icon: string;
    text: string;
}

interface Fields {
    key: string;
    label: string;
    element: 'picker' | 'input';
    popupTitle: string;
    value: string;
    disabled: boolean;
    placeholder: string;
    description: string;
    config: Config;
    rules: Rules[];
    unit?: {
        type: string;
        value: string;
    };
}

interface Footer {
    buttons: Buttons[];
    description: Description;
}

interface InteractionInfo {
    url: string;
    method: string;
}

interface Rules {
    type: string;
    message: string;
    value?: number;
}

interface PickerProps {
    placeholder?: string;
    popupTitle?: string;
    minDate?: string;
    maxDate?: string;
    value?: string;
    onChange?: (val: string) => void;
    disabled?: boolean;
    show?: boolean;
    onClose?: () => void;
    onOpen?: () => void;
}

interface ImFormItemProps {
    label: string;
    unit?: {
        type: string;
        value: string;
    };
    disabled?: boolean;
    children?: ReactNode;
    onContentClick?: () => void;
}

interface InputProps {
    value?: string;
    onChange?: (val: string) => void;
    disabled?: boolean;
    placeholder?: string;
}
