import React from 'react';
import cx from 'classnames';
import {HIcon} from '@baidu/health-ui';
import {View} from '@tarojs/components';

import type {ImFormItemProps} from '../../index.d';
import styles from './index.module.less';

const ImFormItem: React.FC<ImFormItemProps> = props => {
    const {label, unit, children, disabled, onContentClick} = props;

    return (
        <View className={cx(styles.inputWrap, 'wz-flex wz-col-center wz-mb-36 wz-pl-54 wz-fs-45')}>
            <View className={cx({[styles.disabled]: disabled}, 'wz-fs-45 wz-fw-500')}>{label}</View>
            <View
                className={cx(
                    styles.content,
                    {[styles.disabled]: disabled},
                    'wz-flex wz-row-between wz-pr-54'
                )}
                onClick={onContentClick}
            >
                <View className='wz-flex-1 wz-pr-39'>{children}</View>
                {unit?.type === 'string' && <View className={cx(styles.unit)}>{unit.value}</View>}
                {unit?.type === 'icon' && <HIcon value={unit.value} color='#000311' />}
            </View>
        </View>
    );
};

export default ImFormItem;
