import {memo, useCallback, type FC} from 'react';
import {pxTransform} from '@tarojs/taro';
import {View} from '@tarojs/components';
import {HInput} from '@baidu/health-ui';
import cx from 'classnames';

import type {InputProps} from '../../index.d';

import styles from './index.module.less';

const Input: FC<InputProps> = ({value = '', onChange, disabled, placeholder}) => {
    const handleInputChange = useCallback(
        e => {
            onChange?.(e.detail?.value);
        },
        [onChange]
    );

    return (
        <View className={cx(styles.dateInput)}>
            {value === '' && (
                <View className={cx(styles.customPlaceholder, 'wz-fs-45 wz-text-left')}>
                    {placeholder}
                </View>
            )}
            <HInput
                className={styles.input}
                type='number'
                value={value}
                onChange={handleInputChange}
                readonly={disabled}
                rootStyle={{
                    '--input-background': '#f7f8fa',
                    '--input-color-border': '#f7f8fa',
                    '--input-font-size': `${pxTransform(45)}`,
                    '--input-color-text': '#000311',
                    padding: '0',
                    textAlign: 'right',
                    fontWeight: 600,
                    fontFamily: 'PingFangSC'
                }}
            />
        </View>
    );
};

export default memo(Input);
