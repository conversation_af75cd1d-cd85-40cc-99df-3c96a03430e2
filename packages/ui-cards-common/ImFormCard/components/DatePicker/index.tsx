import {memo, useCallback, useMemo, useRef, type FC} from 'react';
import {<PERSON><PERSON>utton, HPicker} from '@baidu/health-ui';
import {View} from '@tarojs/components';
import cx from 'classnames';
import dayjs from 'dayjs';

import type {HPickerInstance} from '@baidu/health-ui/types/HPicker';
import type {PickerProps} from '../../index.d';

import {isIos} from '../../../../pages-im/src/utils/jumpWx';
import Portal from '../../../Portal';
import styles from './index.module.less';

const DatePicker: FC<PickerProps> = props => {
    const {
        placeholder,
        minDate = '2025-01-01',
        maxDate = dayjs().format('YYYY-MM-DD'),
        popupTitle = '请选择',
        value = '',
        onChange,
        disabled = false,
        show = false,
        onOpen,
        onClose
    } = props;
    const childRef = useRef<HPickerInstance>(null);

    // 受控 value => picker 需要数组
    const valueArr = value ? value.split('-').map(v => Number(v)) : [];

    const initDate = useCallback(
        birthOptions => {
            const myOptions = birthOptions({
                minDate,
                maxDate
            });

            // 递归处理每一级，分别加上年/月/日
            function addUnit(options, level = 0) {
                const units = ['年', '月', '日'];
                return options.map(item => {
                    const newItem = {
                        ...item,
                        text: item.value + units[level]
                    };
                    if (item.children) {
                        newItem.children = addUnit(item.children, level + 1);
                    }
                    return newItem;
                });
            }

            const optionsWithUnit = [addUnit(myOptions[0], 0)];
            return optionsWithUnit;
        },
        [maxDate, minDate]
    );

    const defaultValue = useMemo(() => {
        const d = dayjs();
        return [d.year(), d.month() + 1, d.date()];
    }, []);

    const renderPopupFooter = useMemo(
        () => (
            <View className={cx(styles.popupFooter, 'wz-ptb-54')}>
                <View className={cx(styles.line)} />
                <HButton
                    className={styles.confirmButton}
                    text='确定'
                    height={135}
                    onClick={e => {
                        e.stopPropagation();
                        childRef?.current?.onConfirmPicker();
                    }}
                />
            </View>
        ),
        []
    );

    const handlePopupConfirm = useCallback(
        text => {
            let formattedDate = text;
            if (Array.isArray(text)) {
                const year = text[0] || '2025';
                const month = text[1] || '1';
                const day = text[2] || '1';
                formattedDate = dayjs(`${year}-${month}-${day}`).format('YYYY-MM-DD');
            }

            onChange && onChange?.(formattedDate);
            onClose && onClose?.();
        },
        [onClose, onChange]
    );

    const isIosH5 = useMemo(() => {
        return isIos() && process.env.TARO_ENV === 'h5';
    }, []);

    return (
        <View className={cx(styles.datePicker, 'wz-flex-1 wz-flex', {[styles.disabled]: disabled})}>
            <View
                className={cx('wz-flex-1, wz-flex', {
                    'wz-row-right': valueArr.length > 0
                })}
                onClick={onOpen}
            >
                {valueArr && valueArr.length > 0 ? (
                    <View className={styles.dateText}>{valueArr.join('-')}</View>
                ) : (
                    <View className={styles.placeholder}>{placeholder}</View>
                )}
            </View>

            <Portal>
                <HPicker
                    defaultValue={defaultValue}
                    ref={childRef}
                    popupTitle={popupTitle}
                    isCascader
                    show={show}
                    value={valueArr}
                    mode='date'
                    onClose={onClose}
                    onConfirm={handlePopupConfirm}
                    showConfirm={false}
                    showCancel={false}
                    onChangeData={initDate}
                    className={cx({
                        [styles.overscroll]: isIosH5
                    })}
                    propsIndicatorStyle={{
                        color: '#000311'
                    }}
                    propsPickerViewStyle={{
                        height: 240
                    }}
                    popupProps={{
                        renderFooter: renderPopupFooter,
                        showClose: true
                    }}
                />
            </Portal>
        </View>
    );
};

DatePicker.displayName = 'DatePicker';
export default memo(DatePicker);
