.confirmButton {
    width: 100%;
    box-sizing: border-box;
    color: #00c8c8;
    position: relative;
}

.popupFooter {
    background-color: #fff;
    position: relative;

    .line {
        position: absolute;
        top: 0;
        left: -48px;
        right: -48px;
        border-top: #dcdde0 solid 1.5px;
        margin-bottom: 54px;
    }
}

.dateText {
    font-family: PingFang SC;
    font-weight: 600;
    align-self: self-end;
}

.placeholder {
    color: #848691;
}

.overscroll {
    /* stylelint-disable-next-line */
    :global {
        .taro-picker-view-column-container {
            overscroll-behavior-y: none !important;
        }
    }
}
