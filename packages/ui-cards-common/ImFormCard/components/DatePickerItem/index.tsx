import React, {useState, useCallback} from 'react';

import DatePicker from '../DatePicker';
import ImFormItem from '../ImFormItem';

import type {Fields} from '../../index.d';

interface DatePickerItemProps {
    item: Fields;
    value: string;
    disabled?: boolean;
    onChange: (value: string) => void;
}

const DatePickerItem: React.FC<DatePickerItemProps> = ({item, value, disabled, onChange}) => {
    const {label, unit, placeholder, popupTitle, config} = item;
    const [show, setShow] = useState(false);

    const handleClosePopup = useCallback(() => {
        if (!disabled) {
            setShow(false);
        }
    }, [disabled]);

    const handleOpenPopup = useCallback(() => {
        if (!disabled) {
            setShow(true);
        }
    }, [disabled]);

    const handleChange = useCallback(
        (newValue: string) => {
            onChange(newValue);
            handleClosePopup();
        },
        [onChange, handleClosePopup]
    );

    return (
        <ImFormItem label={label} disabled={disabled} unit={unit} onContentClick={handleOpenPopup}>
            <DatePicker
                placeholder={placeholder}
                popupTitle={popupTitle}
                minDate={config?.minDate}
                maxDate={config?.maxDate}
                value={value}
                onChange={handleChange}
                disabled={disabled}
                show={show}
                onOpen={handleOpenPopup}
                onClose={handleClosePopup}
            />
        </ImFormItem>
    );
};

export default DatePickerItem;
