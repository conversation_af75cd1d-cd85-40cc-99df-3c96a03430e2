import {memo, useMemo, useState, type FC, useCallback, useEffect} from 'react';
import {HButton, HIcon} from '@baidu/health-ui';
import {hooks} from '@baidu/vita-pages-im';
import {View} from '@tarojs/components';
import {showToast} from '@baidu/vita-pages-im/src/utils/customShowToast';
import {createTriageStreamConversation} from '@baidu/vita-pages-im/src/models/apis/vtui';
import cx from 'classnames';

import {ubcCommonViewSend, ubcCommonClkSend} from '../../pages-im/src/utils/generalFunction/ubc';

import DatePickerItem from './components/DatePickerItem';
import ImFormItem from './components/ImFormItem';
import Input from './components/Input';

import {validateForm} from './utils/validation';
import type {ImFormCardProps} from './index.d';
import styles from './index.module.less';

const ImFormCard: FC<ImFormCardProps> = (msgData: ImFormCardProps) => {
    const {data, msgId = '', cardId = '', cardName = '', ext = {}} = msgData || {};
    const {
        title,
        fields = [],
        footer,
        disabled: formDisabled = false,
        userSubmitMsg = ''
    } = data?.content || {};
    const {useConversationDataController} = hooks;
    const {createConversation} = useConversationDataController();
    const [fetchLoading, setFetchLoading] = useState(false);

    useEffect(() => {
        ubcCommonViewSend({
            value: 'ImAiForm',
            ext: {
                value_type: cardName,
                value_id: cardId,
                product_info: {
                    msgId: msgId,
                    ...ext
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const initialValues = useMemo(() => {
        const obj: Record<string, any> = {};
        fields.forEach(f => {
            obj[f.key] = f.value ?? '';
        });
        return obj;
    }, [fields]);
    const [formValues, setFormValues] = useState(initialValues);

    const renderContent = useMemo(() => {
        return fields.map(item => {
            const value = formValues[item.key];
            const handleChange = val => {
                setFormValues(v => ({...v, [item.key]: val}));
            };
            const disabled = formDisabled || !!item.disabled || fetchLoading;

            switch (item.element) {
                case 'picker':
                    if (item.config.type === 'date') {
                        return (
                            <DatePickerItem
                                disabled={disabled}
                                key={item.key}
                                item={item}
                                value={value}
                                onChange={handleChange}
                            />
                        );
                    } else {
                        return null;
                    }
                case 'input':
                    if (item?.config?.type === 'number') {
                        return (
                            <ImFormItem
                                label={item.label}
                                key={item.key}
                                disabled={disabled}
                                unit={item.unit}
                            >
                                <Input
                                    placeholder={item.placeholder}
                                    value={value}
                                    onChange={handleChange}
                                    disabled={disabled}
                                />
                            </ImFormItem>
                        );
                    }
                    return null;
                default:
                    return null;
            }
        });
    }, [fetchLoading, fields, formDisabled, formValues]);

    const handleSubmit = useCallback(
        async (e, item) => {
            e?.stopPropagation && e.stopPropagation();

            // 校验表单
            const {isValid, firstError} = validateForm(fields, formValues);
            if (!isValid) {
                showToast({
                    title: firstError,
                    icon: 'none',
                    duration: 1000
                });
                return;
            }

            // 提交表单
            if (item.interaction === 'request') {
                const fields = Object.entries(formValues).map(([key, value]) => ({key, value}));
                if (item?.interactionInfo?.url === createTriageStreamConversation) {
                    // 创建会话特殊处理
                    setFetchLoading(true);
                    createConversation({
                        msg: {
                            type: 'text',
                            content: userSubmitMsg,
                            sceneType: 'submitAiForm'
                        },
                        formSubmitData: {
                            msgId: msgId,
                            fields: fields
                        },
                        withOutMsg: !userSubmitMsg
                    })
                        .catch(() => {
                            setFetchLoading(false);
                        })
                        .finally(() => {
                            ubcCommonClkSend({
                                value: 'ImAiForm',
                                ext: {
                                    value_type: cardName,
                                    value_id: cardId,
                                    product_info: {
                                        msgId: msgId,
                                        ...ext
                                    }
                                }
                            });
                        });
                }
            }
        },
        [fields, formValues, createConversation, msgId, cardName, cardId, ext, userSubmitMsg]
    );

    const renderFooter = useMemo(() => {
        return (
            <View>
                {footer?.buttons && footer?.buttons?.length > 0 && (
                    <View className={cx(styles.footer, 'wz-flex')}>
                        {footer.buttons.map(item => (
                            <HButton
                                key={item.value}
                                className={cx(styles.submitButton, {
                                    [styles.submitButtonDisabled]:
                                        formDisabled || item.disabled || fetchLoading
                                })}
                                size={45}
                                text={item.value}
                                height={135}
                                disabled={formDisabled || item.disabled || fetchLoading}
                                onClick={e => handleSubmit(e, item)}
                            />
                        ))}
                    </View>
                )}

                {footer.description && (
                    <View className='wz-flex wz-col-top wz-mt-51'>
                        {footer.description?.icon && (
                            <HIcon
                                className={styles.descriptionIcon}
                                size={39}
                                value={footer.description?.icon}
                            />
                        )}
                        {footer.description?.text && (
                            <View className={cx(styles.descriptionText, 'wz-fs-39 wz-ml-18')}>
                                {footer.description.text}
                            </View>
                        )}
                    </View>
                )}
            </View>
        );
    }, [fetchLoading, footer.buttons, footer.description, formDisabled, handleSubmit]);

    return (
        <View className={styles.imFormCard}>
            {title && <View className={cx(styles.title, 'wz-fs-57')}>{title}</View>}
            {renderContent}
            {renderFooter}
        </View>
    );
};

ImFormCard.displayName = 'ImFormCard';
export default memo(ImFormCard);
