import {showToast} from '@tarojs/taro';
import {View} from '@tarojs/components';
import React, {memo, FC, forwardRef, useImperativeHandle, useEffect, useCallback} from 'react';

import {SmsProps} from './index.d';

let observer: MutationObserver | null = null;
const Sms: FC<SmsProps> = forwardRef((props: SmsProps, ref) => {
    const {phone, onLoginSuccess, disableSuccessToast} = props;
    const id = `sms-${Math.ceil(Math.random() * 1000)}-${Date.now()}`;

    // 初始化验证码登录
    const initSmsLogin = useCallback(
        i => {
            const staticPage = `${window.location.origin}/med/web/static/html/v3Jump.html`;
            const login = new i.passport.smsLogin({
                product: 'askdoctor',
                u: window.location.href,
                smsRegTip: 0,
                extrajson: {
                    src: 'se_020003'
                },
                staticPage
            });
            login.on('loginSuccess', e => {
                // eslint-disable-next-line no-undef
                const $wrap = $(`#${id}`);
                onLoginSuccess &&
                    onLoginSuccess({
                        phone: $wrap.find('input[name="mobilenum"]').val(),
                        sms: $wrap.find('input[name="password"]').val()
                    });
                !disableSuccessToast &&
                    showToast({
                        icon: 'none',
                        title: '登录成功'
                    });
                e.returnValue = false;
            });
            login.on('loginError', e => {
                showToast({
                    icon: 'none',
                    title: e?.rsp?.errInfo?.msg
                });
            });

            login.on('render', e => {
                e.returnValue = false;
                // eslint-disable-next-line no-undef
                const $wrap = $(`#${id}`);
                // 填入手机号
                $wrap.find('input[name="mobilenum"]').val(phone);
                // 点击[发送动态验证码]
                $wrap.find('.pass-button-send').click();
                // 错误信息
                const $err = $wrap.find('.pass-msg-generalError');
                observer = new MutationObserver(() => {
                    const errorText = $err.text();
                    console.info('errerrerr ===> ', errorText);
                    if (errorText) {
                        showToast({
                            title: errorText.trim(),
                            icon: 'none'
                        });
                    }
                });
                observer?.observe($err.get(0), {
                    attributes: true
                });
                $wrap.css({
                    opacity: 0,
                    position: 'absolute',
                    'z-index': -1
                });
            });
            login.render(id);
        },
        [id, onLoginSuccess, phone, disableSuccessToast]
    );
    useEffect(() => {
        if (phone) {
            // eslint-disable-next-line no-undef
            const $wrap = $(`#${id}`);
            $wrap.find('input[name="mobilenum"]').val(phone);
            console.info('name="mobilenum', $wrap.find('input[name="mobilenum"]').val());
        }
        console.info('phone ====> ', phone);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [phone]);

    useEffect(() => {
        // 初始化验证码登录组件
        // 参考文档：http://dev.passport.baidu.com/docs/agg/viewv2?path=tech.text&doc=wap/passport/sms.text
        const src = `https://wappass.baidu.com/static/touch/js/api/wrapper.js?cdnversion=${new Date().getTime()}`;
        getScript(src, () => {
            /* eslint-disable no-undef */
            passport.use(
                'smsLogin',
                {
                    library: true
                },
                i => {
                    return initSmsLogin(i);
                }
            );
        });

        return () => {
            observer && observer.disconnect();
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 动态添加js
    const getScript = (src, cb) => {
        const script = document.createElement('script');
        script.async = true;
        script.src = src;
        if (typeof cb === 'function') {
            script.onload = cb;
        }
        document.getElementsByTagName('head')[0].appendChild(script);
    };

    // 手动发送验证码
    const sendSms = () => {
        const $wrap = $(`#${id}`);
        // 点击[发送动态验证码]
        $wrap.find('.pass-button-send').click();
    };
    // 手动登录
    const login = (smsCode?: string | number) => {
        const $wrap = $(`#${id}`);
        smsCode && $wrap.find('input[name="password"]').val(smsCode);
        $wrap.find('.pass-button-submit').removeClass('pass-button-submit-disabled').click();
    };

    useImperativeHandle(ref, () => ({
        sendSms,
        login
    }));

    return <View id={id} />;
});

Sms.displayName = 'Sms';
export default memo(Sms);
