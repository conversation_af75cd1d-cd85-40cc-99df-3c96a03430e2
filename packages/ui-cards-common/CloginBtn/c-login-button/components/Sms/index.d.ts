import type {CSSProperties, ReactNode} from 'react';

export interface SmsProps {
    phone: string | number;
    sms: string | number;
    onLoginSuccess: (...args) => void;
    className?: string;
    customStyle?: string | CSSProperties;
    children?: ReactNode | ReactNode[];
    disableSuccessToast?: boolean;
}

export declare const passport: {
    use: (name: string, options: {library: boolean}, callback: (...args: any[]) => void) => void;
};
export declare const smsLogin: {
    on: (event: string, callback: (...args: any[]) => void) => void;
};

export declare const $: any;
