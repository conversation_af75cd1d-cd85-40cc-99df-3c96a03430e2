export interface UbcParams {
    type: 'clk' | 'view';
    value: string;
    ext?: {[key: string]: string | number};
}

export interface IProps {
    open?: boolean;
    unShowNewUserTag?: boolean;
    isH5Triage?: boolean;
    setOpen?: (args: boolean) => void;
    loginSuccess: (args: {phone?: string | number; sms?: string | number}) => void;
    ubcCallback?: (params: UbcParams) => void;
    disableSuccessToast?: boolean;
}

export interface IPopupInfo {
    params?: {
        title?: string;
    };
    type?: string;
}

interface IOption {
    appKey?: string;
    swanUrl?: string;
}

interface ICtnIobjItem {
    fullStr?: string;
    shortStr?: string;
    showFlag?: boolean;
}

export interface IAbstractItem {
    title?: string;
    url?: string;
    icon?: string;
    contentType?: number;
    content?: Array<string>;
    option?: IOption;
    ctnObj: ICtnIobjItem[];
}

interface IDoctorInfo {
    department?: string;
    docImg?: string;
    docTeam?: string;
    docTeamUrl?: string;
    doctorText?: string;
    edit?: string;
    hospital?: string;
    name?: string;
    portrait?: string;
    title?: string;
    url?: string;
}

export interface IYidian {
    title?: string;
    abstract?: IAbstractItem[];
    option?: IOption;
    alias?: string;
    doctor?: IDoctorInfo;
    headerTitle?: string;
    sign?: string;
    subTitle?: string;
    tag?: string;
    type?: string;
    url?: string;
}
