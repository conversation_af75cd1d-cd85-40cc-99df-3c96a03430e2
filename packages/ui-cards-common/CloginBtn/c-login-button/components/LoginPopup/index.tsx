import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import {WiseCloseSolid} from '@baidu/wz-taro-tools-icons';
import {showToast, nextTick, useRouter} from '@tarojs/taro';
import {CountdownInstance} from '@baidu/wz-taro-tools-core/countdown';
import React, {memo, useCallback, useState, useRef, useMemo, FC} from 'react';
import {Popup, Input, Button, Countdown} from '@baidu/wz-taro-tools-core';

import SmsCom from '../Sms';
import {prefixClassname} from '../../utils/prefixClassname';
import {navigateFnOfSwan} from '../../utils/navigateFn';

import type {IProps} from './index.d';

const loginDesc = [
    [
        {
            text: '继续操作代表您已阅读并同意',
            type: 'text'
        },
        {
            text: '《百度用户协议》',
            type: 'link',
            url: `/wenzhen/pages/common/webpage/index?url=${encodeURIComponent('https://wappass.baidu.com/passport/agreement?adapter=3&lang=zh-cn')}&unNeedLogin=1&title=百度用户协议`
        }
    ],
    [
        {
            text: '《百度隐私政策》',
            type: 'link',
            url: `/wenzhen/pages/common/webpage/index?url=${encodeURIComponent('https://wappass.baidu.com/passport/agreement?adapter=3&lang=zh-cn&personal=1')}&unNeedLogin=1&title=百度隐私政策`
        },
        {
            text: '和',
            type: 'text'
        },
        {
            text: '《百度健康用户协议》',
            type: 'link',
            url: '/wenzhen/pages/common/agreementList/index'
        }
    ]
];

const phoneNumberReg = /^1[3456789]\d{9}$/;

const LoginPopup: FC<IProps> = props => {
    const options = {
        placement: 'bottom',
        closeable: true
    };
    const {isDirected} = useRouter()?.params || {};

    const {open, setOpen, loginSuccess, unShowNewUserTag, ubcCallback, disableSuccessToast} = props;

    const smsRef = useRef();
    const countRef = useRef<CountdownInstance>(null);

    const [phone, setPhone] = useState('');
    const [code, setCode] = useState('');
    const [smsCode, setSmsCode] = useState('');
    const [smsPhone, setSmsPhone] = useState('');
    // 展示倒计时
    const [showCountDown, setShowCountDown] = useState(false);
    // 获取验证码高亮
    const [isActiveSendCode, setIsActiveSendCode] = useState(true);
    // 获取验证码文案
    const [codeText, setCodeText] = useState('获取验证码');

    // 控制是否展示优惠提示
    const showCoupon = useMemo(() => {
        return !unShowNewUserTag && isDirected?.toString() !== '1';
    }, [isDirected, unShowNewUserTag]);

    // 关闭弹窗
    const _setOpen = useCallback(() => {
        setOpen?.(false);
        setCode('');

        ubcCallback?.({
            type: 'clk',
            value: 'c_login_h5_popup_close'
        });
    }, [setOpen, ubcCallback]);

    const updatePhone = useCallback(e => {
        setPhone(e.detail.value);
    }, []);

    const updateCode = useCallback(e => {
        setCode(e.detail.value);
    }, []);

    const sendCodeFun = useCallback(() => {
        if (!isActiveSendCode && showCountDown) {
            return;
        }
        if (!phone || !phoneNumberReg.test(phone)) {
            showToast({
                title: '手机号格式填写错误',
                icon: 'none'
            });

            return;
        }
        setShowCountDown(true);
        setIsActiveSendCode(false);
        setSmsPhone(phone);
        nextTick(() => {
            if (smsRef && smsRef.current) {
                const {sendSms} = smsRef.current as unknown as {sendSms: () => void};
                sendSms && sendSms();
            }
        });
        setCodeText('再次获取');
    }, [isActiveSendCode, showCountDown, phone]);

    const handleLoginSuccess = useCallback(
        info => {
            loginSuccess && loginSuccess(info);
        },
        [loginSuccess]
    );

    const handleClear = useCallback(() => {
        setShowCountDown(false);
        setIsActiveSendCode(true);
        countRef.current?.reset();
        setCodeText('获取验证码');
    }, []);

    const handleClickRule = useCallback(item => {
        if (item.type === 'link') {
            navigateFnOfSwan({url: item.url, openType: 'navigate'});
        }
    }, []);

    const toLogin = useCallback(() => {
        if (code && code.length >= 6) {
            setSmsCode(code);
            // 调用pass 方法
            if (smsRef && smsRef.current) {
                const {login} = smsRef.current as unknown as {login: (...args) => void};
                login && login(code);
                setCode('');

                ubcCallback?.({
                    type: 'clk',
                    value: 'c_login_to_login'
                });
            }
        }
    }, [code, ubcCallback]);

    // 生成登录元素
    const genLoginDom = useMemo(() => {
        return (
            <View>
                <View className={cx(prefixClassname('c-login-popup'))} catchMove>
                    <View className={cx(prefixClassname('c-login-popup_title'))}>
                        <View className={cx(prefixClassname('c-login-popup_title-newRea'))}>
                            登录百度账号
                            {/* 非定向展示优惠信息 */}
                            {showCoupon && (
                                <View
                                    className={cx(
                                        prefixClassname('c-login-popup_title-newRea-newText')
                                    )}
                                >
                                    新人登录领券
                                </View>
                            )}
                        </View>
                    </View>
                    <View className={cx(prefixClassname('c-login-popup_con'))}>
                        <View className={cx(prefixClassname('c-login-popup_con-input'))}>
                            <View style={{flex: 1}}>
                                <Input
                                    placeholder='请输入手机号'
                                    clearable
                                    value={phone}
                                    type='number'
                                    nativeProps={{pattern: '[0-9]*'}}
                                    maxlength={11}
                                    clearIcon={<WiseCloseSolid />}
                                    confirmType='send'
                                    onInput={updatePhone}
                                    onConfirm={sendCodeFun}
                                    onClear={handleClear}
                                />
                            </View>
                            <View
                                className={cx(
                                    prefixClassname(
                                        isActiveSendCode && phone && phone.length === 11
                                            ? 'c-login-popup_con-input-activeCodeText'
                                            : 'c-login-popup_con-input-codeText'
                                    )
                                )}
                                onClick={sendCodeFun}
                            >
                                {codeText}
                                {showCountDown && (
                                    <>
                                        <Text>(</Text>
                                        <Countdown
                                            ref={countRef}
                                            style={{display: 'inline-block', color: '#ccc'}}
                                            value={60 * 1000}
                                            format='ss'
                                            onComplete={() => {
                                                setShowCountDown(false);
                                                setIsActiveSendCode(true);
                                            }}
                                        />
                                        <Text>)</Text>
                                    </>
                                )}
                            </View>
                        </View>
                        <View className={cx(prefixClassname('c-login-popup_con-code-input'))}>
                            <View style={{flex: 1}}>
                                <Input
                                    placeholder='请输入验证码'
                                    value={code}
                                    type='text'
                                    nativeProps={{
                                        pattern: '[0-9]*',
                                        inputmode: 'numeric',
                                        autocomplete: 'one-time-code'
                                    }}
                                    maxlength={6}
                                    confirmType='send'
                                    onInput={updateCode}
                                />
                            </View>
                        </View>
                        <View className={cx(prefixClassname('c-login-popup_con-btn'))}>
                            <Button
                                className={cx(
                                    prefixClassname(
                                        !(phone && phone.length === 11 && code && code.length >= 6)
                                            ? 'c-login-popup_con-btn-disableitem'
                                            : 'c-login-popup_con-btn-item'
                                    ),
                                    prefixClassname('c-login-popup_con-btn-phoneLoginBtn')
                                )}
                                size='large'
                                shape='round'
                                onClick={toLogin}
                            >
                                登录
                            </Button>
                        </View>
                    </View>
                    <View className={cx(prefixClassname('c-login-popup_desc'))}>
                        {loginDesc.map((item, index) => {
                            return (
                                <View
                                    key={index}
                                    className={cx(
                                        prefixClassname('c-login-popup_desc-con'),
                                        index === 1
                                            ? prefixClassname('c-login-popup_desc-con-item1')
                                            : ''
                                    )}
                                >
                                    {item.map((ol, olIndex) => {
                                        return (
                                            <Text
                                                key={olIndex}
                                                className={cx(
                                                    ol.type === 'link'
                                                        ? prefixClassname(
                                                            'c-login-popup_desc-con-active'
                                                        )
                                                        : ''
                                                )}
                                                onClick={() => {
                                                    handleClickRule(ol);
                                                }}
                                            >
                                                {ol.text}
                                            </Text>
                                        );
                                    })}
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>
        );
    }, [
        code,
        codeText,
        handleClear,
        isActiveSendCode,
        phone,
        sendCodeFun,
        showCountDown,
        showCoupon,
        toLogin,
        updateCode,
        updatePhone,
        handleClickRule
    ]);

    return (
        <Popup open={open} placement='bottom' rounded onClose={_setOpen}>
            <Popup.Backdrop />
            {options.closeable && <Popup.Close />}
            {genLoginDom}
            {
                <SmsCom
                    ref={smsRef}
                    sms={smsCode}
                    phone={smsPhone}
                    onLoginSuccess={handleLoginSuccess}
                    disableSuccessToast={disableSuccessToast}
                />
            }
        </Popup>
    );
};

export default memo(LoginPopup);
