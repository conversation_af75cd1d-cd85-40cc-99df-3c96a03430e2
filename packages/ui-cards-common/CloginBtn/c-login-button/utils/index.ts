import Taro, {getCurrentPages} from '@tarojs/taro';

export const isObject = (val: object): boolean =>
    Object.prototype.toString.call(val) === '[object Object]';

export const uuid = (len = 8, _radix = 16) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
    const value: string[] = [];
    let i = 0;
    const radix = _radix || chars.length;

    if (len) {
        for (i = 0; i < len; i++) {
            value[i] = chars[0 | (Math.random() * radix)];
        }
    } else {
        let r;

        value[8] = value[13] = value[18] = value[23] = '-';
        value[14] = '4';

        for (i = 0; i < 36; i++) {
            if (!value[i]) {
                r = 0 | (Math.random() * 16);
                value[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r];
            }
        }
    }

    return value.join('');
};

/**
 * 比较版本号（版本号位数需要相等）
 *
 * @param v1 版本1
 * @param v2 版本2
 * @returns 0：相等 | 1：v1大于v2 | -1：v1小于v2
 */
export const versionCompare = (v1: string | number, v2: string | number) => {
    if (v1 === v2) {
        return 0;
    }

    const toNum = (version: string | number) => {
        // eslint-disable-next-line no-param-reassign
        version = version.toString();
        // const versionArr = version.split('.')
        const versionArr = version.split(/\D/);
        const NUM_FILL = ['0000', '000', '00', '0', ''];

        for (let i = 0; i < versionArr.length; i++) {
            const len = versionArr[i].length;
            versionArr[i] = NUM_FILL[len] + versionArr[i];
        }

        return parseInt(versionArr.join(''), 10);
    };

    // eslint-disable-next-line no-param-reassign
    v1 = toNum(v1);
    // eslint-disable-next-line no-param-reassign
    v2 = toNum(v2);

    if (v1 > v2) {
        return 1;
    }
    if (v1 < v2) {
        return -1;
    }

    return 0;
};

export interface IfObject {
    [propName: string]: any;
}

/**
 * 扩展对象
 *
 * @param {...Object} args 扩展对象参数列表
 * @return {Object} 扩展后的对象
 */
export function extend(...args: IfObject[]) {
    const arr = Array.prototype.slice.call(args);
    const org = arr.shift();
    arr.forEach(function (obj: IfObject) {
        const o = obj || {};
        Object.getOwnPropertyNames(o).forEach(function (key: string) {
            if (isObject(o[key]) && isObject(org[key])) {
                extend(org[key], o[key]);
            } else {
                if (o[key] !== undefined) {
                    org[key] = o[key];
                }
            }
        });
    });

    return org;
}

/**
 * 将对象转化为query字符串
 * @param obj 对象
 * @returns 字符串
 */
export const getQueryStr = (obj: IfObject): string => {
    if (isObject(obj)) {
        return Object.getOwnPropertyNames(obj)
            .map((key: string) => `${key}=${obj[key]}`)
            .join('&');
    }

    return '';
};

interface PageProps {
    /**
     * 当前路由
     * @note 例如pages/gy/order-detail/index
     * @note Mars的uri可取route
     */
    route: string;

    /**
     * 路径
     * @note 例如/pages/gy/order-det/index?infoType=db2c&orderId=dbc_16502509820430887085
     */
    path: string;

    /**
     * 参数
     */
    options: Taro.Page;

    /**
     * 是否为当前唯一页面
     */
    isOnlyPage: boolean;
}

export const getCurrentPage = (): PageProps => {
    let result = {
        route: '',
        path: '',
        options: {},
        isOnlyPage: false
    };
    const currentPages = getCurrentPages();
    if (currentPages && currentPages.length > 0) {
        const currentPage = currentPages[currentPages.length - 1];

        result = {
            route: currentPage.route || '',
            path: currentPage.privateProperties.accessUri,
            options: currentPage.options,
            isOnlyPage: currentPages.length === 1
        };
    }

    return result;
};
