/* eslint-disable no-undef */
import {navigateTo, redirectTo, switchTab, reLaunch, navigateBack} from '@tarojs/taro';

// import { getAppInfoOfSwan } from '../getAppInfo';
import {WX_APP_KEY_MAP} from './index.contant';

import type {NavigateFnParams, OpenType} from './index.d';

type MiniAppName = keyof typeof WX_APP_KEY_MAP;

type NavigationOptionsWithUrl = {url: string; [key: string]: unknown};

type NavigationFunction = <T extends NavigationOptionsWithUrl>(options: T) => unknown;

const withUrlProcessing = (navFunc: NavigationFunction): NavigationFunction => {
    return (options: NavigationOptionsWithUrl) => {
        if (process.env.TARO_ENV === 'h5' && !options.url.startsWith('http')) {
            options.url = window.location.origin + options.url;
            window.location.href = options.url;
            return;
        }

        return navFunc(options);
    };
};

export type {OpenType};

const easybrowseConfig = {
    // 手百
    baiduboxapp(url: string) {
        return {
            module: 'easybrowse',
            action: 'open',
            parameters: {
                url,
                newbrowser: 1
            }
        };
    },
    // 地图
    bdmap(url: string) {
        return {
            authority: 'map',
            path: '/cost_share',
            parameters: {
                popRoot: 'no',
                url,
                hideshare: 'yes'
            }
        };
    },
    // 百度极速版
    bdlite(url: string) {
        return {
            module: 'easybrowse',
            action: 'open',
            parameters: {
                url,
                newbrowser: 1
            }
        };
    }
};

export const navigateFnOfSwan = (fnArgs: NavigateFnParams) => {
    /**
     *
     * @description 打开手百轻浏览框架跳转
     * @param arg
     */
    const easybrowseNavigate = (arg: NavigateFnParams) => {
        // const sys = getAppInfoOfSwan();
        // const { host = 'baiduboxapp' } = sys;

        // Tips：check 之前代码完全为默认值，暂时写死后续跟进；@wanghaoyu08
        const host = 'baiduboxapp';
        const config = easybrowseConfig[host](arg?.url || '');
        swan.openBdboxWebview(config);
    };

    /**
     *
     * @description 百度跳转三方小程序
     * @param arg
     * @param {string} arg.url 跳转链接 小程序拼接规则：baiduboxapp://swan/${appKey}/${path}?${query}
     * @example baiduboxapp://swan/********************************/pages/view/view?query=1&_baiduboxapp=%7B%22ext%22%3A%7B%7D%7D&callback=_bdbox_js_275&upgrade=0
     */
    const navigateotherMiniApp = (arg: NavigateFnParams) => {
        const matchs = (arg?.url && arg?.url.match(/baiduboxapp:\/\/swan\/(\w+)\/(.+)/)) || [];
        let appKeyData = '';
        let pathData = '';
        // 判断url中是否带有baiduboxapp://swan/${appKey}/
        if (arg?.url && arg?.url.includes('baiduboxapp:')) {
            [, appKeyData, pathData] = matchs;
        } else {
            if (arg?.miniAppName && arg.miniAppName in WX_APP_KEY_MAP) {
                appKeyData = WX_APP_KEY_MAP[arg.miniAppName as MiniAppName];
            }
            // url只有pages路径
            pathData = arg?.url || '';
        }
        swan.navigateToSmartProgram({
            appKey: appKeyData,
            path: pathData,
            fail: (error: Error) => {
                console.error('navigateToSmartProgram 出错：', error);
            }
        });
    };

    const wrappedNavigateTo = withUrlProcessing(navigateTo);
    const wrappedRedirectTo = withUrlProcessing(redirectTo);
    const wrappedReLaunch = withUrlProcessing(reLaunch);
    const wrappedSwitchTab = withUrlProcessing(switchTab);

    const fnMap: {[k in OpenType]?: (arg?: any) => unknown} = {
        switchTab: wrappedSwitchTab,
        navigateBack, // navigateBack doesn't typically take a URL
        navigate: wrappedNavigateTo,
        redirect: wrappedRedirectTo,
        relaunch: wrappedReLaunch,
        easybrowse: easybrowseNavigate,
        otherMiniApp: navigateotherMiniApp
    };

    fnMap[fnArgs?.openType]?.(fnArgs);
};
