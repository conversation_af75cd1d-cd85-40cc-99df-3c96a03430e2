import {HOME_PATH, TAB_BAR} from './index.contant';

interface AppInfoProps {
    /**
     * AppID
     */
    appId: string;

    /**
     * Id
     * @note 唯一标识符
     */
    id: string;

    /**
     * 主页地址
     */
    home: string;

    /**
     * TabBar配置
     */
    tabBar: string[];

    /**
     * platformType
     * @note 与后端约定的不同平台类型值
     */
    platformType?: number;
}

export const getAppInfoOfSwan = (): AppInfoProps => {
    const appId = '11806953'; // 目前仅有一个百度小程序, 暂时写死; 暂时没找到api返回此数据
    const id = `swan_${appId}`;

    return {
        appId,
        id,
        home: HOME_PATH[id],
        tabBar: TAB_BAR[id]
    } as AppInfoProps;
};
