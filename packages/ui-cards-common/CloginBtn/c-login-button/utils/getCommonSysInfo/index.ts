/* eslint-disable */
// @ts-nocheck
import {getCommonSysInfo as getSysInfo} from '@tarojs/taro';

import globalData from '../globalData/index';

let getCommonSysInfo = () => {
    // 空实现, 无意义
    return {
        sid: '',
        baidu_id: ''
    };
};

if (process.env.TARO_ENV === 'swan') {
    getCommonSysInfo = () => {
        new Promise(resolve => {
            let {baidu_id = '', cuid = '', sid = ''} = globalData.get('login') || {};
            if (baidu_id) {
                resolve({
                    baidu_id,
                    cuid,
                    sid
                });
            } else {
                getSysInfo({
                    success(res: {baidu_id: string; sid: string; cuid: string}) {
                        // eslint-disable-next-line no-shadow
                        baidu_id = res.baidu_id || '';
                        sid = res.sid || '';
                        cuid = res.cuid || '';
                        globalData.set('login', {
                            baidu_id,
                            sid,
                            cuid
                        });

                        resolve({
                            baiduid: baidu_id,
                            sid,
                            cuid
                        });
                    },
                    fail() {
                        resolve({
                            baiduid: '',
                            sid: ''
                        });
                    }
                });
            }
        });
    };
}

export {getCommonSysInfo};
