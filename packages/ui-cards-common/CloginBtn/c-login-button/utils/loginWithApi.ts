/* eslint-disable no-undef */
import {getCurrentPages} from '@tarojs/taro';

import {inH5, inSwan} from './env';
import {getAppInfoOfSwan} from './getAppInfo';
import {navigateFnOfSwan, type OpenType} from './navigateFn';
import {extend, getQueryStr, getCurrentPage} from './index';

export interface UserConfParams {
    params?: {
        detail?: {
            encryptedData: string;
            appid: string;
            wxCode: string;
        };
        wxCode?: string;
        src?: string;
    };
    href?: string;
    backUrl?: string;
    openType?: OpenType;
    type?: number; // 登录类型
    isReplaceUrl?: boolean;
    loginStatus?: boolean | number;
    interactionType?: 'popup' | 'page'; // 登录交互类型，主要针对微信小程序场景
    successCallback?: () => void;
    failCallback?: () => void;
}

/**
 *
 * @description H5小程序登录 api
 * @param userConf
 */
const h5LoginApi = (userConf?: UserConfParams) => {
    return new Promise((resolve, reject) => {
        try {
            const host = 'https://wappass.baidu.com';

            const conf = extend(
                {
                    type: 1,
                    params: {
                        src: 'se_020003',
                        from: 'common'
                    },
                    href: '',
                    isReplaceUrl: false
                },
                userConf || {}
            );

            let query: {[k in string]: unknown} = {};
            query = extend(
                {
                    adapter: 3, // 隐藏titlebar
                    tpl: 'askdoctor',
                    overseas: 1,
                    extrajson: JSON.stringify(conf.params),
                    u: conf.href
                        ? encodeURIComponent(window.location.origin + conf.href)
                        : encodeURIComponent(window.location.href)
                },
                conf.type === 2
                    ? {
                        sms: 1
                    }
                    : {}
            );
            if (conf.href.indexOf('http') === 0) {
                // 如果是完整的跳转地址直接封装url跳转
                query.u = encodeURIComponent(conf.href);
            }
            const url = `${host}?${getQueryStr(query)}`;
            if (conf.isReplaceUrl) {
                window.location.replace(url);
            } else {
                window.location.href = url;
            }
            resolve(null);
        } catch (err) {
            reject(err);
        }
    });
};

/**
 *
 * @description 百度小程序登录 api
 * @param userConf
 */
const swanLoginApi = (userConf?: UserConfParams) => {
    const appInfo = getAppInfoOfSwan();

    new Promise((resolve, reject) => {
        let canIUseLoginButton = false;
        canIUseLoginButton = swan.canIUse('button.open-type.login');
        if (canIUseLoginButton) {
            if (userConf?.loginStatus) {
                resolve(null);
            } else {
                reject();
            }
        } else {
            swan.login()
                .then(d => resolve(d))
                .catch(e => reject(e));
        }
    })
        .then(() => {
            // 处理登录成功跳转地址
            const currentPage = getCurrentPage();
            const defHref = `/${currentPage?.path}`;
            // 处理登录成功跳转方式
            let defOpenType = '';
            if (currentPage.isOnlyPage) {
                defOpenType = 'relaunch';
            } else {
                defOpenType = 'redirect';
            }
            const url = userConf?.href || defHref;
            navigateFnOfSwan({
                url,
                openType: userConf?.openType || (defOpenType as OpenType)
            });
        })
        .catch(() => {
            const pages = getCurrentPages();
            if (pages && pages.length - 1 > 0) {
                navigateFnOfSwan({
                    url: '',
                    openType: 'navigateBack'
                });
            } else if (pages && pages.length === 1) {
                // eslint-disable-next-line prefer-destructuring
                const curPage = pages[0];
                const homePath = appInfo.home;
                const tabBarPathArr = appInfo.tabBar;
                try {
                    const {msg} = getCurrentPage().options;
                    const msgObj = msg ? JSON.parse(msg) : {};
                    const to = msgObj.to || '';
                    if (
                        (to && tabBarPathArr.some(item => to.indexOf(item) > -1)) ||
                        (curPage && tabBarPathArr.some(item => curPage.indexOf(item) > -1))
                    ) {
                        navigateFnOfSwan({
                            url: homePath,
                            openType: 'relaunch'
                        });
                    }
                } catch (e) {
                    navigateFnOfSwan({
                        url: homePath,
                        openType: 'relaunch'
                    });
                }
            }
        });
};

/**
 * 跳转登录页
 *
 * @param {Object} userConf 用户配置，详见：http://wiki.baidu.com/pages/viewpage.action?pageId=537399885
 * @param {number} userConf.type        登录类型：1-账号密码；2-手机号验证码(H5使用)
 * @param {number} userConf.params      统计参数（H5使用）
 * @param {number} userConf.href        登录成功后跳转地址（通用）
 * @param {number} userConf.openType    登录成功后跳转页面方式（swan使用）
 * @param {number} userConf.isReplaceUrl 进入登录页是否replaceState（H5使用）
 * @param {boolean} userConf.loginStatus 登录成功状态，true：成功 / false： 失败
 */
export const toLoginWithApi: (userConf?: UserConfParams) => Promise<unknown> | void = (
    userConf = {}
) => {
    if (inH5) {
        return h5LoginApi(userConf);
    } else if (inSwan) {
        return swanLoginApi(userConf);
    }
};
