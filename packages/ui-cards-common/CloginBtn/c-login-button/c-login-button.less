/* stylelint-disable */
@wz-taro-prefix: wz-taro-;
@component-prefix: @wz-taro-prefix;

.@{component-prefix}c-login-button {
    line-height: 1;
    position: relative;

    &_real-button {
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        left: 0;
        z-index: 2;
        background: transparent !important;
        border: none;
    }

    &_real-button::after {
        border: none;
    }
}

.@{component-prefix}c-login-popup {
    line-height: 1 !important;
    height: 852px;
    padding-top: 72px;

    &_title {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 57px;
        margin-bottom: 8px;
        color: #1f1f1f;

        &-newRea {
            position: relative;

            &-newText {
                position: absolute;
                right: -260px;
                top: -10px;
                color: #fff;
                background: linear-gradient(90deg, #fd503e, #f55f4e);
                border-radius: 18px 18px 18px 0;
                padding: 6px 14px 6px 12px;
                font-size: 36px;
            }
        }
    }

    &_con {
        padding-left: 90px;
        padding-right: 90px;

        &-input {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f3f4f5;
            padding: 0 45px;
            margin-top: 63px;
            height: 132px;
            width: 100%;
            box-sizing: border-box;
            border-radius: 24px;

            &-codeText,
            &-activeCodeText {
                font-size: 45px !important;
                padding-left: 45px !important;
                margin-left: 45px !important;
                height: fit-content;
            }

            &-codeText {
                color: #ccc !important;
                border-left: 1px solid #e0e0e0 !important;
            }

            &-activeCodeText {
                color: #00c8c8 !important;
                border-left: 1px solid #e0e0e0 !important;
            }
        }

        &-code-input {
            background-color: #f3f4f5;
            padding: 0 45px;
            margin-top: 24px;
            height: 132px;
            width: 100%;
            box-sizing: border-box;
            border-radius: 24px;
            display: flex;
            align-items: center;
        }

        &-btn {
            margin-top: 45px;
            text-align: center;

            &-phoneLoginBtn {
                background: #00c8c8;
                color: #fff !important;
                height: 160px !important;
                border-radius: 66px !important;
                font-size: 54px;
            }

            &-item {
                background: #00c8c8 !important;
            }

            &-disableitem {
                background-image: linear-gradient(
                    -82deg,
                    rgba(0, 211, 234, 0.4) 0%,
                    rgba(0, 207, 163, 0.4) 100%
                ) !important;
                opacity: 40% !important;
            }
        }
    }

    &_desc {
        font-size: 39px;
        color: #b5b5b5;
        margin-top: 88px;
        padding-bottom: 60px;

        &-con {
            text-align: center;

            &-item1 {
                margin-top: 18px;
            }

            &-active {
                color: #858585;
            }
        }
    }
}
