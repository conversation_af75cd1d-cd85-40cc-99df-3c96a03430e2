import cx from 'classnames';
import {<PERSON>, Button} from '@tarojs/components';
import React, {FC, memo, useCallback, useState} from 'react';

import Portal from '../../Portal';

import {versionCompare} from './utils/index';
import {toLoginWithApi} from './utils/loginWithApi';
import {prefixClassname} from './utils/prefixClassname';
import {LoginButtonProps} from './index.d';
import ImLoginPopup from './components/LoginPopup';

import './index.less';

let __isLogin: boolean | string | number = false;
const canIUseSwanLoginButton = process.env.TARO_ENV === 'swan';

const LoginButton: FC<LoginButtonProps> = props => {
    const {
        isLogin = false,
        useH5QuickLogin,
        useH5CodeLogin,
        className,
        children,
        callbackUrl,
        closeShowNewUserTag,
        onLoginSuccess,
        onLoginFail,
        ubcCallback,
        disableSuccessToast = false
    } = props;

    const code = '';
    __isLogin = isLogin;

    const [loginOpen, setLoginOpen] = useState(false);

    /**
     * 登录成功回调
     * @event onLoginSuccess
     */
    const loginSuccess = useCallback(
        (event = {}) => {
            __isLogin = true;
            onLoginSuccess?.({
                ...event,
                loginStatus: true
            });
            setLoginOpen(false);
        },
        [onLoginSuccess]
    );

    /**
     * 登录失败回调
     * @event onLoginSuccess
     */
    const loginFail = useCallback(
        (event = {}) => {
            onLoginFail?.({
                ...event,
                loginStatus: false
            });
        },
        [onLoginFail]
    );

    const swanButtonLoginClick = useCallback(() => {
        ubcCallback?.({
            type: 'clk',
            value: 'c_login_swan_login_popup'
        });
    }, [ubcCallback]);

    /**
     *
     * @description 生成百度小程序登录按钮回调
     */
    const swanButtonLoginCallback = useCallback(
        e => {
            if (e && e.detail && e.detail.errMsg === 'login:ok') {
                /**
                 * 登录成功回调
                 * @event onLoginSuccess
                 */
                loginSuccess?.(e);
            } else {
                /**
                 * 登录失败回调（取消登录 + 登录失败）
                 * @event onLoginFail
                 */
                loginFail?.(e);
            }
        },
        [loginFail, loginSuccess]
    );

    const h5ButtonLoginCallback = useCallback(() => {
        // 因为挂载在全局的数据无法绑定dom，所以再判断一下
        if (__isLogin) {
            onLoginSuccess && onLoginSuccess({});

            return;
        }
        const boxx = require('@baidu/boxx').default || {};
        boxx.account &&
            boxx.account.login({
                loginType: 'fast',
                showThirdLogin: '1',
                loginSource: 'se_008000',
                normalizeAccount: '0',
                success: e => {
                    __isLogin = true;
                    loginSuccess(e);
                },
                fail: e => {
                    loginFail(e);
                }
            });
    }, [loginFail, loginSuccess, onLoginSuccess]);

    const h5HaokanButtonLoginCallback = useCallback(() => {
        window.loginresult = res => {
            try {
                // eslint-disable-next-line no-param-reassign
                res = JSON.parse(res);
                if (+res?.status === 0) {
                    loginSuccess(res);
                    __isLogin = true;
                } else {
                    loginFail(res);
                }
            } catch (error) {
                loginFail(error);
            }
        };
        try {
            window.location.href = 'baiduhaokan://action/login?from=tuoguan&callback=loginresult';
        } catch (error) {
            loginFail(error);
        }
    }, [loginFail, loginSuccess]);

    /**
     * 调用登录能力
     */
    const h5CommonLoginCallback = useCallback(
        e => {
            e.stopPropagation();
            try {
                ubcCallback?.({
                    type: 'clk',
                    value: 'c_login_h5_code_login'
                });
            } catch (error) {
                /* empty */
            }

            // 半弹窗H5登录
            if (useH5CodeLogin && process.env.TARO_ENV === 'h5') {
                setLoginOpen(true);

                return;
            }
            toLoginWithApi({
                href: callbackUrl,
                params: {...e, wxCode: code}
            });
        },
        [useH5CodeLogin, callbackUrl, ubcCallback]
    );

    /**
     *
     * @description 生成百度小程序登录按钮
     */
    const genSwanRealButton = useCallback(() => {
        return (
            <Button
                className={prefixClassname('c-login-button_real-button')}
                type='primary'
                open-type='login'
                onClick={swanButtonLoginClick}
                onLogin={swanButtonLoginCallback}
            />
        );
    }, [swanButtonLoginCallback, swanButtonLoginClick]);

    /**
     *
     * @description 生成百度 H5登录按钮
     */
    const genH5BdRealButton = useCallback(() => {
        // 未登录且启用才用一键登录的逻辑，因为大部分页面没有传isLogin，且因为端能力比较sd，不给判断丢给业务自己处理。。。
        // 手百一键登录 http://es.baidu-int.com/index#/apidetail/15/base
        const boxx = require('@baidu/boxx').default || {};
        try {
            /**
             * 可以支持的端能力描述表
             * 手百下小视频弹层应该是端上没下发，业务补发一下。wtf！！！
             */
            const {Descriptions} = require('./utils/adWebView/boxxDescription');
            boxx.setDefaultDescription(Descriptions);
        } catch (error) {
            console.error('[CLoginButton] 出错：genH5BdRealButton', error);
        }
        if (boxx.canIUse('account.login')) {
            return (
                <View
                    className={prefixClassname('c-login-button_real-button')}
                    onClick={h5ButtonLoginCallback}
                />
            );
        }
    }, [h5ButtonLoginCallback]);

    /**
     *
     * @description 生成真实登录按钮
     */
    const genRealButton = useCallback(() => {
        if (canIUseSwanLoginButton) {
            return genSwanRealButton();
        } else if (process.env.TARO_ENV === 'h5' && useH5QuickLogin) {
            const getPlatform = require('./utils/adWebView/getPlatform').default || {};
            const isHaokanApp = getPlatform.isHaokanApp();
            const isBaiduApp = getPlatform.isBaiduApp();

            if (isBaiduApp) {
                return genH5BdRealButton();
            } else if (isHaokanApp) {
                const haokanAppVersion = getPlatform.haokanAppVersion();
                const versionResult = versionCompare(haokanAppVersion, '********') || -1;

                return versionResult >= 0 ? (
                    <View
                        className={prefixClassname('c-login-button_real-button')}
                        onClick={h5HaokanButtonLoginCallback}
                    />
                ) : (
                    <></>
                );
            }

            return (
                <View
                    className={prefixClassname('c-login-button_real-button')}
                    onClick={h5CommonLoginCallback}
                />
            );
        }

        return (
            <View
                className={prefixClassname('c-login-button_real-button')}
                onClick={h5CommonLoginCallback}
            />
        );
    }, [
        genSwanRealButton,
        useH5QuickLogin,
        genH5BdRealButton,
        h5HaokanButtonLoginCallback,
        h5CommonLoginCallback
    ]);

    const ViewLoginSuccess = useCallback(() => {
        onLoginSuccess?.();
    }, [onLoginSuccess]);

    return (
        <>
            <View className={cx(prefixClassname('c-login-button'), className)}>
                {/* 如果未登录仅渲染children, 这样保证children上的event事件可以执行 */}
                {/* 如果已经登录 渲染children和登录元素（renderLoginedStatus）；
                 * 根据event冒泡原理，children上挂载的onClick会阻塞外层登录元素的执行，
                 * 为了保证登录事件执行，使用css手段，登录元素（renderLoginedStatus）绝对定位覆盖在children
                 * */}
                {__isLogin ? (
                    <View
                        className={prefixClassname('c-login-button_real-button')}
                        onClick={ViewLoginSuccess}
                    />
                ) : (
                    genRealButton()
                )}
                {children}
            </View>
            {/* 解决登录弹窗层级问题 */}
            <Portal>
                <ImLoginPopup
                    unShowNewUserTag={closeShowNewUserTag}
                    open={loginOpen}
                    ubcCallback={ubcCallback}
                    setOpen={setLoginOpen}
                    loginSuccess={loginSuccess}
                    disableSuccessToast={disableSuccessToast}
                />
            </Portal>
        </>
    );
};

export default memo(LoginButton);
