import {View, Image} from '@tarojs/components';
import {FC, memo} from 'react';
import cx from 'classnames';

import {imgUrlMap} from '../../../../pages-im/src/constants/resourcesOnBos';
import {FuDanInfoProps} from './index.d';

import styles from './index.module.less';

// 复旦排行标签组件
const FudanInfo: FC<FuDanInfoProps> = ({fuDanList}) => {
    const {fudanIcon} = imgUrlMap;

    return (
        <>
            {fuDanList?.length > 0 && (
                <View className={cx(styles.newRankContainer, 'wz-flex')}>
                    <Image src={fudanIcon} className={cx(styles.huangguan)} />
                    <View className={cx(styles.listContainer, 'c-line-clamp1, wz-flex')}>
                        <View
                            className={cx(
                                styles.rankText,
                                'c-line-clamp1, wz-fw-500, wz-ml-9, wz-fs-36'
                            )}
                        >
                            {fuDanList?.[0]?.text}
                        </View>
                    </View>
                </View>
            )}
        </>
    );
};

export default memo(FudanInfo);
