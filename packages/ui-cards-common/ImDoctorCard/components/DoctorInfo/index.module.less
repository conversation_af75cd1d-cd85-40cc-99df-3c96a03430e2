.docInfoWrapper {
    line-height: 1;

    .docNameWrapper {
        color: #1f1f1f;
        letter-spacing: 0;
        line-height: 42px;
        overflow: hidden;

        .name {
            color: #1f1f1f;
            letter-spacing: 0;
            line-height: 54px;
            flex-shrink: 0;
            display: inline;
        }

        .line {
            flex-shrink: 0;
            display: inline;
        }

        .department {
            white-space: nowrap;
            display: inline;
        }

        .portraitTagWrapper {
            .tagWrapper {
                display: inline-block;
                box-sizing: border-box;
                white-space: nowrap;

                .tagTxt {
                    line-height: 36px;
                }
            }
        }
    }

    .hosInfoWrapper {
        align-items: center;

        .hosName {
            color: #1f1f1f;
        }

        .tagWrap {
            min-width: 112px;
            font-size: 0;
        }

        .tagListWrapper {
            white-space: nowrap;
        }
    }

    .goodAtWrapper {
        .expertGoodText {
            color: #525252;
        }
    }

    .tips {
        color: #858585;
        flex-wrap: wrap;
        line-height: 54px;

        .serviceVal {
            margin-left: 6px;
        }

        .tipsLine {
            height: 42px;
            margin: 0 18px;
            position: relative;
            width: 0.99px;
            margin-bottom: 3px;
            background: #858585;
        }

        .tipsLine::after {
            content: '';
            position: absolute; /* 把父视图设置为relative，方便定位 */
            top: 0;
            left: 0;
            width: 200%;
            height: 200%;
            transform: scale(0.5);
            transform-origin: 0 0;
            box-sizing: border-box;
            border-radius: 40px;
            border: 1px solid #b6b6b6;
        }

        .blue {
            color: #00c8c8;
        }

        .grey {
            color: rgb(82 82 82);
        }
    }

    .priceServiceWrapper {
        width: 100%;

        .priceWrapper {
            color: #fd503e;

            .prePrice {
                color: #fd503e;
                font-weight: 600;
            }

            .price {
                position: relative;
                top: 4.5px;
            }

            .tip {
                color: #858585;
            }
        }

        .freeImgIcon {
            width: 36px;
            height: 36px;
        }

        .freeTag {
            color: #7c68f3;
        }

        .btn {
            margin-left: auto;
        }
    }
}
