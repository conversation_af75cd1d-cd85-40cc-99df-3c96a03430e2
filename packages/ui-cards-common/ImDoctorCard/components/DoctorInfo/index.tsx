import cx from 'classnames';
import {memo, useMemo, FC, useCallback, ReactNode} from 'react';
import {isEmpty} from '@baidu/vita-pages-im/src/utils';
import {View, Text} from '@tarojs/components';
import {Tag, WImage} from '@baidu/wz-taro-tools-core';
import {pxTransform} from '@tarojs/taro';
import {HButton} from '@baidu/health-ui';
import {formatPrice} from '@baidu/vita-pages-im/src/utils/generalFunction/price';

import {generateHighlightText} from '../../utils/generateHighlightText';
import SanjiaTag from '../SanjiaTag';
import {freeTagIcon} from '../../constants';

import type {DoctorInfoProps, ExpertGoodAt} from './index.d';
import styles from './index.module.less';

const DoctorInfo: FC<DoctorInfoProps> = ({
    expertName,
    expertDepartment,
    expertLevel,
    expertHospital,
    attributeTag,
    expertGoodAt,
    indicatorList,
    freeTag,
    price,
    pricePre,
    btnInfo,
    showPortraitTag,
    onBtnClick,
    onCardClick
}) => {
    // 金额转化
    const finalPrice = formatPrice(Number(price));

    // 渲染三甲、本地等标签
    const renderAttributeTagList = useMemo(() => {
        return (
            attributeTag &&
            attributeTag?.length > 0 &&
            attributeTag.map((i, idx) => {
                return i.key && i.key === 'hospitalLevel' ? (
                    <View key={idx} className={cx('wz-ml-18')}>
                        <SanjiaTag content={i.text} />
                    </View>
                ) : (
                    <Tag
                        key={idx}
                        size='medium'
                        shape='square'
                        variant='outlined'
                        style={{
                            padding: `${pxTransform(9)}
                                ${pxTransform(14)} ${pxTransform(8)}`,
                            fontSize: pxTransform(33),
                            backgroundColor: 'inherit',
                            color: i.color || '#FF6600',
                            borderColor: i.borderColor || 'rgba(255,102,0,0.50)',
                            fontWeight: 'bold'
                        }}
                        className={cx('wz-ml-18', styles.tagWrapper)}
                    >
                        <View className={styles.tagTxt}>{i.text}</View>
                    </Tag>
                );
            })
        );
    }, [attributeTag]);

    // 渲染曾经问过Tag
    const renderPortraitTag = useMemo(() => {
        return (
            showPortraitTag &&
            showPortraitTag?.length > 0 &&
            showPortraitTag.slice(0, 1).map((i, idx) => {
                return (
                    <Tag
                        key={idx}
                        size='medium'
                        shape='square'
                        variant='outlined'
                        style={{
                            padding: `${pxTransform(9)}
                                ${pxTransform(14)} ${pxTransform(8)}`,
                            fontSize: pxTransform(33),
                            backgroundColor: 'inherit',
                            color: '#858585',
                            borderColor: 'rgba(255,102,0,0.50)'
                        }}
                        className={cx(styles.tagWrapper, 'wz-fw-500')}
                    >
                        <View className={styles.tagTxt}>{i.value}</View>
                    </Tag>
                );
            })
        );
    }, [showPortraitTag]);

    const genComponent = useCallback((type: string, itemData: ExpertGoodAt) => {
        const componentsMap: {
            [k in string]: ReactNode;
        } = {
            text: (
                /* bca-disable */
                <View
                    className={cx(styles.expertGoodText, 'wz-taro-ellipsis')}
                    dangerouslySetInnerHTML={{__html: generateHighlightText(itemData?.value || '')}}
                />
            ),
            highLightText: (
                <Text className={cx(styles.expertGoodHighLightText, 'wz-fw-500')}>
                    {itemData?.value}
                </Text>
            )
        };

        return componentsMap[type] || null;
    }, []);

    // 文本展示
    const genContext = useCallback(() => {
        if (expertGoodAt) {
            return (
                !isEmpty(expertGoodAt) &&
                expertGoodAt?.map(i => {
                    return (
                        <View className={styles.expertGoodContentItem} key={i?.value}>
                            {genComponent(i?.type || '', i || '')}
                        </View>
                    );
                })
            );
        }
    }, [expertGoodAt, genComponent]);

    return (
        <View
            className={cx(styles.docInfoWrapper)}
            onClick={e => {
                e.stopPropagation();
                onCardClick?.();
            }}
        >
            {/* 医生姓名、等级、科室 */}
            <View
                className={cx(styles.docNameWrapper, 'wz-flex, wz-col-center, wz-mb-27, wz-fs-42')}
            >
                <View className={cx(styles.name, 'wz-mr-18, wz-fs-54, wz-fw-500')}>
                    {expertName && expertName?.length > 5
                        ? `${expertName.slice(0, 4)}...`
                        : expertName}
                </View>
                <View className={cx(styles.line, 'wz-mr-18, wz-fw-400, wz-taro-ellipsis')}>
                    {expertLevel}
                </View>
                <View className={cx(styles.department, 'wz-mr-18, wz-fw-400, wz-taro-ellipsis')}>
                    {expertDepartment}
                </View>
                {showPortraitTag && !isEmpty(showPortraitTag) && (
                    <View className={cx(styles.portraitTagWrapper)}>{renderPortraitTag}</View>
                )}
            </View>

            {/* 医院信息 */}
            <View className={cx(styles.hosInfoWrapper, 'wz-flex, wz-mb-27')}>
                {/* 医院名称 */}
                <View className={cx(styles.hosName, 'wz-fs-42, wz-fw-500, wz-taro-ellipsis')}>
                    {expertHospital}
                </View>
                {!isEmpty(attributeTag) && (
                    <View className={cx(styles.tagListWrapper, 'wz-flex')}>
                        {renderAttributeTagList}
                    </View>
                )}
            </View>

            {/* 擅长 */}
            {expertGoodAt && expertGoodAt?.length > 0 && (
                <View className={cx(styles.goodAtWrapper, 'wz-fs-42, wz-fw-400, wz-mb-27')}>
                    <View className={cx(styles.expertGoodAt)}>{genContext()}</View>
                </View>
            )}

            {/* 咨询量 */}
            <View className={cx('wz-flex, wz-fs-42, wz-mb-36', styles.tips)}>
                {Array.isArray(indicatorList) &&
                    indicatorList?.map((item, index) => (
                        <View key={index} className={cx('wz-flex')}>
                            <View>
                                {item?.text}
                                <Text
                                    className={cx(
                                        item?.value === '暂无' ? styles.gray : styles.blue,
                                        styles.serviceVal,
                                        'wz-fw-500'
                                    )}
                                >
                                    {item?.value}
                                </Text>
                            </View>
                            {indicatorList && indicatorList?.length > index + 1 && (
                                <View className={cx(styles.tipsLine)} />
                            )}
                        </View>
                    ))}
            </View>

            {/* 价格 & 次数 */}
            <View
                className={cx(
                    styles.priceServiceWrapper,
                    'wz-flex, wz-col-center, wz-row-between, wz-fs-42'
                )}
            >
                {!freeTag ? (
                    <View className='wz-flex'>
                        {/* 无优惠时展示价格 */}
                        {!isEmpty(price) && (
                            <View className={cx(styles.priceWrapper, 'wz-flex, wz-col-bottom')}>
                                {pricePre && (
                                    <Text className={cx(styles.prePrice, 'wz-fs-36 wz-mr-9')}>
                                        券后
                                    </Text>
                                )}
                                <Text className={cx(styles.mark, 'wz-fw-500, wz-fs-36')}>￥</Text>
                                <Text className={cx(styles.price, 'wz-fs-54, wz-fw-500')}>
                                    {finalPrice}
                                </Text>
                                <Text className={cx(styles.tip, 'wz-ml-9 wz-fs-36')}>起</Text>
                            </View>
                        )}
                    </View>
                ) : (
                    <View className='wz-flex  wz-col-center wz-row-center'>
                        <WImage className={cx(styles.freeImgIcon, 'wz-mr-9')} src={freeTagIcon} />
                        <View className={cx(styles.freeTag, 'wz-fs-36 wz-fw-500')}>
                            {freeTag || '支持医生分身免费问'}
                        </View>
                    </View>
                )}
                {btnInfo && (
                    <HButton
                        className={cx(styles.btn, 'wz-fw-500')}
                        text={btnInfo?.value || '去咨询'}
                        size={42}
                        width={216}
                        height={96}
                        padding='0 0'
                        bgColor='linear-gradient(270deg,#00C0C0,#00D5BD)'
                        onClick={e => {
                            e.stopPropagation();
                            onBtnClick?.();
                        }}
                    />
                )}
            </View>
        </View>
    );
};

DoctorInfo.displayName = 'DoctorInfo';
export default memo(DoctorInfo);
