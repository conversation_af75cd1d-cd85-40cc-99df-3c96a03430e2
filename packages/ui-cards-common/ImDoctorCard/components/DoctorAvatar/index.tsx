import {FC, memo} from 'react';
import cx from 'classnames';
import {View} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import type {DoctorAvatarProps} from './index.d';

import styles from './index.module.less';

const DoctorAvatar: FC<DoctorAvatarProps> = ({isOnline, expertPic, mode}) => {
    return (
        <View className={cx(styles.avatarWrapper)}>
            {expertPic && (
                <WImage
                    className={cx(
                        mode === 'direct' ? styles.avatar : styles.avatarComb,
                        isOnline === 1 && styles.onlineBorder,
                        !isOnline && mode === 'combination' && styles.offlineBorder,
                        'wz-mb-15'
                    )}
                    src={expertPic}
                    mode='aspectFill'
                />
            )}
            {isOnline === 1 && (
                <View
                    className={cx(
                        mode === 'direct' ? styles.online : styles.onlineComb,
                        'wz-fs-30'
                    )}
                >
                    在线
                </View>
            )}
        </View>
    );
};

DoctorAvatar.displayName = 'DoctorAvatar';
export default memo(DoctorAvatar);
