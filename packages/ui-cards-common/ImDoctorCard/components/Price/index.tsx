import {View} from '@tarojs/components';
import {FC, memo} from 'react';
import cx from 'classnames';

import styles from './index.module.less';

export interface PriceProps {
    price: string;
}

// 金额展示组件 x.y x为大号字体，y为小号字体
const Price: FC<PriceProps> = ({price}) => {
    const priceStr = price;
    // 根据小数点区分整数部分和小数部分
    const [intPart, decimalPart = ''] = priceStr.split('.');
    // 判断是否有小数部分
    const hasDecimal = priceStr.includes('.');
    // 如果没有小数部分，小数部分需要兜底显示0
    const displayDecimal = hasDecimal ? decimalPart || '0' : '0';

    return (
        <>
            <View className={cx(styles.price, 'wz-fs-48, wz-fw-700')}>
                {intPart}
                {'.'}
            </View>
            <View className={cx(styles.price, 'wz-fs-39, wz-fw-700')}>{displayDecimal}</View>
        </>
    );
};

export default memo(Price);
