.docInfoWrapper {
    line-height: 1;

    .docInfoFlexWrapper {
        width: 100%;
        align-items: start;

        .docInfoFlexLeftWrapper {
            width: 100%;
        }

        .docNameWrapper {
            color: #848691;
            letter-spacing: 0;
            overflow: hidden;
            font-size: 48px;
            line-height: 1;

            .name {
                color: #000311;
                letter-spacing: 0;
                display: inline;
            }

            .line {
                display: inline;
            }

            .department {
                white-space: nowrap;
                display: inline;
            }
        }

        .hosInfoWrapper {
            align-items: center;

            .hosName {
                color: #000311;
            }

            .tagWrap {
                min-width: 112px;
                font-size: 0;
            }

            .tagListWrapper {
                white-space: nowrap;
            }
        }

        .priceServiceWrapper {
            flex-direction: column;
            margin-left: 60px;

            .priceWrapper {
                color: #fd503e;

                .prePrice {
                    color: #fd503e;
                    font-weight: 600;
                }

                .priceNum {
                    position: relative;
                    white-space: nowrap;
                }

                .tip {
                    color: #858585;
                }
            }

            .btnWrapper {
                align-self: flex-end;
            }

            .freeImgIcon {
                width: 36px;
                height: 36px;
            }

            .freeTag {
                color: #7c68f3;
            }
        }
    }

    .tips {
        color: #858585;
        flex-wrap: wrap;
        font-size: 39px;
        line-height: 1;

        .tipsItem {
            max-width: 50%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .serviceVal {
            margin-left: 6px;
        }

        .tipsLine {
            height: 42px;
            margin: 0 18px;
            position: relative;
            width: 0.99px;
            margin-bottom: 3px;
            background: #e0e0e0;
        }

        .tipsLine::after {
            content: '';
            position: absolute; /* 把父视图设置为relative，方便定位 */
            top: 0;
            left: 0;
            width: 200%;
            height: 200%;
            transform: scale(0.5);
            transform-origin: 0 0;
            box-sizing: border-box;
            border-radius: 40px;
            border: 1px solid #b6b6b6;
        }

        .blue {
            color: #00c8c8;
        }

        .grey {
            color: rgb(82 82 82);
        }
    }

    .goodAtWrapper {
        .expertGoodText {
            color: #000311;
        }
    }
}
