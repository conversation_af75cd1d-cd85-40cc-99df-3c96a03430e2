import xss from 'xss';

// 处理文本样式
export const generateHighlightText = (str: string, color = '#00c8c8') => {
    const option = {
        whiteList: {
            span: ['style', 'class'],
            div: ['style', 'class']
        }
    };

    const decodeUnicode = (s: string) =>
        s.replace(/\\u([\dA-Fa-f]{4})/g, (_, g) => String.fromCharCode(parseInt(g, 16)));

    const decodeHTMLEntities = (s: string) =>
        s.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');

    const decoded = decodeHTMLEntities(decodeUnicode(str));

    // 统一替换 em 或 b 为自定义 span
    const highlighted = decoded
        .replace(/<(em|b)>/g, `<div style="color:${color};display:inline;" class="wz-fw-400">`)
        .replace(/<\/(em|b)>/g, '</div>');

    return xss(highlighted, option);
};
