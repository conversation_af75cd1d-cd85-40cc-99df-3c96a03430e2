import Taro from '@tarojs/taro';

interface fileInterface {
    path: string;
}

interface ImageInfo {
    width: number;
    height: number;
    content: string;
}

// 图片格式校验 检测图片尺寸
export const getImageInfo = async (file: fileInterface) => {
    return new Promise<ImageInfo>(resolve => {
        Taro.getImageInfo({
            src: file.path,
            success: res => {
                resolve({
                    width: res.width,
                    height: res.height,
                    content: file.path
                });
            },
            fail: () => {
                resolve({
                    width: 360,
                    height: 360,
                    content: file.path
                });
            }
        });
    });
};
