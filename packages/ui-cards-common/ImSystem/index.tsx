import cx from 'classnames';
import {forwardRef, useCallback, useImperativeHandle, useMemo, useState} from 'react';
import {View} from '@tarojs/components';
import ErrorBoundary from '@baidu/wz-taro-tools-core/error-boundary';

import ImSystemText from './components/ImSystemText';
import ImSystemButton from './components/ImSystemButton';
import ImSystemCountdown from './components/ImSystemCountDown';
import ImSystemIcon from './components/ImSystemIcon';
import type {
    InteractionType,
    ImSystemInstance,
    ImSystemProps,
    TypeEnum,
    ImSystemChildItem
} from './index.d';
import styles from './index.module.less';

const ImSystem = forwardRef<ImSystemInstance, ImSystemProps>((props, ref) => {
    const {data, onThrowEvent} = props;
    const {renderType = 0} = data?.cardStyle || {};

    const [changedData, setChangedData] = useState<ImSystemChildItem[][] | null>(null);

    useImperativeHandle(ref, () => ({
        getData: () => data
    }));

    const onNodeChange = useCallback((arg: ImSystemChildItem) => {
        arg?.statusChangednode?.content?.list &&
            setChangedData(arg.statusChangednode?.content?.list);
    }, []);

    const dataList = useMemo(() => {
        return changedData ? changedData : data?.content?.list ? data?.content?.list : [];
    }, [changedData, data?.content?.list]);

    const genComponent = useCallback((type: keyof typeof TypeEnum, itemData: ImSystemChildItem) => {
        const componentsMap = {
            text: (
                <ImSystemText
                    type='text'
                    key={itemData?.value}
                    value={itemData?.value}
                    onThrowEvent={onThrowEvent}
                />
            ),
            underLineText: (
                <ImSystemText
                    type='underLineText'
                    key={itemData?.value}
                    value={itemData?.value}
                    interactionInfo={itemData?.interactionInfo}
                    interaction={(itemData?.interaction as InteractionType)!}
                    onThrowEvent={onThrowEvent}
                />
            ),
            textline: (
                <ImSystemText
                    type='text'
                    key={itemData?.value}
                    value={`${itemData?.value}\n`}
                    onThrowEvent={onThrowEvent}
                />
            ),
            redText: (
                <ImSystemText
                    type='redText'
                    key={itemData?.value}
                    value={itemData?.value}
                    onThrowEvent={onThrowEvent}
                />
            ),
            secondaryText: (
                <ImSystemText
                    type='secondaryText'
                    key={itemData?.value}
                    value={itemData?.value}
                    interactionInfo={itemData?.interactionInfo}
                    interaction={(itemData?.interaction as InteractionType)!}
                    onThrowEvent={onThrowEvent}
                />
            ),
            highLightText: (
                <ImSystemText
                    type='highLightText'
                    key={itemData?.value}
                    value={itemData?.value}
                    interactionInfo={itemData?.interactionInfo}
                    interaction={(itemData?.interaction as InteractionType)!}
                    onThrowEvent={onThrowEvent}
                />
            ),
            button: (
                <ImSystemButton
                    variant={renderType === 5 ? 'outlined' : 'contained'}
                    data={itemData}
                />
            ),
            countdown: (
                <ImSystemCountdown
                    data={itemData}
                    key={itemData?.value}
                    onNodeChange={onNodeChange}
                />
            ),
            icon: (
                <ImSystemIcon
                    key={itemData?.value}
                    value={itemData?.value}
                    renderType={renderType}
                    interactionInfo={itemData?.interactionInfo}
                    interaction={(itemData?.interaction as InteractionType)!}
                />
            )
        };
        return componentsMap[type] || null;
    }, []);

    const genContextItem = useCallback(
        (list: ImSystemChildItem[], idx: number) => (
            <View key={`sysItem${idx}`} className={styles.imSystemItem}>
                {list?.map(i => genComponent(i?.type as keyof typeof TypeEnum, i))}
            </View>
        ),
        [genComponent]
    );

    const genContext = useCallback(() => {
        return dataList.map((i, idx) => genContextItem(i, idx));
    }, [dataList, genContextItem]);

    return (
        <ErrorBoundary>
            <View
                className={cx(
                    styles.imSystem,
                    renderType ? styles[`imSystemType${renderType}`] : undefined
                )}
            >
                <View
                    className={cx(
                        'wz-flex',
                        [4, 5].includes(renderType)
                            ? styles[`imSystemType${renderType}`]
                            : undefined
                    )}
                >
                    {renderType === 1 && <View className={cx(styles.imSystemLine, 'wz-mr-30')} />}
                    {genContext()}
                    {renderType === 1 && <View className={cx(styles.imSystemLine, 'wz-ml-30')} />}
                </View>
            </View>
        </ErrorBoundary>
    );
});

ImSystem.displayName = 'ImSystem';

export default ImSystem;
