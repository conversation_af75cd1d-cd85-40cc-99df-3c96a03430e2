import cx from 'classnames';
import {Text} from '@tarojs/components';
import {memo, useCallback} from 'react';
import useCountdown from './hook';

import {ImSystemCountdownProps} from '../../index.d';

import styles from './index.module.less';

const ImSystemCountdown = memo((props: ImSystemCountdownProps) => {
    const {data, onNodeChange} = props;

    const [time] = useCountdown({
        initTime: Number(data?.value),
        callbackFn: () => {
            onNodeChange && onNodeChange(data);
        }
    });

    const genContent = useCallback(() => {
        return <Text className={cx(styles.imSystemCountdownText, 'wz-fw-500')}>{time}</Text>;
    }, [time]);

    return genContent();
});

ImSystemCountdown.displayName = 'ImSystemCountdown';
export default ImSystemCountdown;
