import cx from 'classnames';
import {memo, useCallback, useEffect} from 'react';
import {View} from '@tarojs/components';
import {WImage, Button} from '@baidu/wz-taro-tools-core';
import type {ImSystemButtonProps} from '../../index.d';

import styles from './index.module.less';

const ImSystemButton = memo((props: ImSystemButtonProps) => {
    const {data, variant = 'contained'} = props;
    const {value, icon, interaction, interactionInfo} = data || {};

    const onSubmit = useCallback(() => {
        console.log('点击消息按钮', interactionInfo);
    }, [interaction, interactionInfo]);

    useEffect(() => {
        // 打点实现
        console.log('打点实现');
    }, [interactionInfo?.params?.from]);

    return (
        <Button
            className={cx(
                styles['imSystemButton'],
                styles[`imSystemButton${variant}`],
                'wz-plr-36'
            )}
            color='primary'
            variant={variant}
            onClick={onSubmit}
        >
            <View className='wz-flex wz-col-center'>
                {icon && <WImage className={cx(styles.imSystemButtonIcon)} src={icon} />}
                <View className='wz-fs-39 wz-fw-900'>{value}</View>
            </View>
        </Button>
    );
});

ImSystemButton.displayName = 'ImSystemButton';
export default ImSystemButton;
