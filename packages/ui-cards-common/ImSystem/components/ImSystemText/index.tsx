import cx from 'classnames';
import {memo, useMemo, ReactNode, useCallback} from 'react';
import {debounce} from 'lodash-es';
import {Text} from '@tarojs/components';
import type {InteractionType} from '@baidu/wz-taro-tools-core/msg';

import type {ImSystemTextProps, TypeEnum} from '../../index.d';

import styles from './index.module.less';

const ImSystemText = memo((props: ImSystemTextProps) => {
    const {type, value, interaction, interactionInfo, onThrowEvent} = props;

    const clickHighLight = useCallback(() => {
        onThrowEvent &&
            onThrowEvent({
                interaction: interaction as InteractionType,
                interactionInfo: interactionInfo!
            });
    }, [interaction, interactionInfo, onThrowEvent]);

    const componentsMap = useMemo<{
        [key in keyof typeof TypeEnum]?: ReactNode;
    }>(() => {
        return {
            text: <Text className={cx(styles.imSystemText)}>{value}</Text>,
            // 新版本字体下添加下划线
            underLineText: (
                <Text
                    className={cx(styles.imSystemUnderlineText)}
                    onClick={debounce(() => clickHighLight(), 500)}
                >
                    {value}
                </Text>
            ),
            redText: (
                <Text className={cx(styles.imSystemRedText, 'wz-mlr-12, wz-fw-500')}>{value}</Text>
            ),
            secondaryText: (
                <Text className={cx(styles.imSystemSecondaryText)} onClick={clickHighLight}>
                    {value}
                </Text>
            ),
            highLightText: (
                <Text className={cx(styles.imSystemHighLightText)} onClick={clickHighLight}>
                    {value}
                </Text>
            )
        };
    }, [value, clickHighLight]);

    return <>{componentsMap[type]}</>;
});

ImSystemText.displayName = 'ImSystemText';

export default ImSystemText;
