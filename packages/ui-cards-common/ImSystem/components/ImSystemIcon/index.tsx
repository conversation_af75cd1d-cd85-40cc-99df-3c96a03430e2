import cx from 'classnames';
import {memo, useMemo, useCallback} from 'react';
import {View} from '@tarojs/components';
import {WiseFoldRight} from '@baidu/wz-taro-tools-icons';
import type {ImSystemIconProps, iconMapProps} from '../../index.d';

import styles from './index.module.less';

const ImSystemIcon = memo((props: ImSystemIconProps) => {
    const {value, interaction, renderType, interactionInfo} = props;

    const sendClkUbc = useCallback(() => {
        // 打点实现
        console.log('打点实现');
    }, [value]);

    // 非http图片链接时，通过value进行map映射icon组件
    const iconMap: iconMapProps = useMemo(() => {
        return {
            rightArrow: <WiseFoldRight color='#b8b8b8' size={54} />
        };
    }, []);

    const click = useCallback(() => {
        // 为value为非链接形式的icon，发送点击事件
        Object.keys(iconMap).includes(String(value)) && sendClkUbc();
    }, [iconMap, interaction, interactionInfo, sendClkUbc, value]);

    const genIcon = useMemo(() => {
        try {
            // 非http链接匹配
            if (Object.keys(iconMap).includes(String(value))) {
                return (
                    <View className='wz-flex' onClick={click}>
                        {iconMap[value]}
                    </View>
                );
            }
            // http链接匹配
            if (String(value)?.includes('http://') || String(value)?.includes('https://')) {
                return (
                    <View
                        className={cx(
                            styles.imSystemIcon,
                            renderType === 0 && styles.imSystemIconSmall
                        )}
                        style={{
                            backgroundImage: `url(${value})`
                        }}
                        key={value}
                        onClick={click}
                    />
                );
            }

            return <></>;
        } catch (err) {
            return null;
        }
    }, [click, renderType, value, iconMap]);

    return <>{genIcon}</>;
});

ImSystemIcon.displayName = 'ImSystemIcon';
export default ImSystemIcon;
