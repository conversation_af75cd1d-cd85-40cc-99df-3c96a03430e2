import type {InteractionInfo, InteractionType} from '@baidu/vita-pages-im/src/typings';
import type {CardsCommonProps, DispatchEventCallbackParams} from '@baidu/wz-taro-tools-core/msg';
import type {ReactNode} from 'react';
export interface ImSystemInstance {
    getData: () => any;
}

export interface ImSystemProps extends CardsCommonProps<ImSystemConProps> {}

export interface ImSystemConProps {
    list: ImSystemChildItem[][];
}

export interface ImSystemChildItem {
    type: keyof typeof TypeEnum;
    value: string;
    icon?: string;
    interaction?: InteractionType;
    interactionInfo?: InteractionInfo;
    statusChangednode?: {
        content: {
            list: ImSystemChildItem[][];
        };
    };
}

export enum TypeEnum {
    'text' = '文本',
    'underLineText' = '下划线文本',
    'redText' = '红色文本',
    'secondaryText' = '次要文本',
    'highLightText' = '高亮文本',
    'countdown' = '倒计时',
    'button' = '按钮',
    'icon' = 'icon 图标'
}

export interface ImSystemTextProps {
    type: keyof typeof TypeEnum;
    value: string | number;
    interaction?: InteractionType;
    interactionInfo?: InteractionInfo;
    onThrowEvent?: (args: DispatchEventCallbackParams) => void;
}

export interface ImSystemButtonProps {
    data: ImSystemChildItem;
    variant?: 'contained' | 'text' | 'outlined';
}

export interface ImSystemIconProps {
    value: string | number;
    interaction?: InteractionType;
    interactionInfo?: InteractionInfo;
    renderType?: number;
}

export interface iconMapProps {
    [key: string]: ReactNode;
}

export interface ImSystemCountdownProps {
    data: ImSystemChildItem;
    onNodeChange: (args: ImSystemChildItem) => void;
}

export {InteractionType};
