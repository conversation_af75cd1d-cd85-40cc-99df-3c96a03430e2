import type {ActionInfo} from '@baidu/vita-pages-im/src/typings';
import type {ButtonInfo, PortraitTag} from '../../index.d';

export interface pennantItemData {
    name: string;
    count: number;
}

export interface DoctorInfoProps {
    expertName?: string;
    expertDepartment?: string;
    expertLevel?: string;
    expertHospital?: string;
    attributeTag?: AttributeTags[];
    expertGoodAt?: ExpertGoodAt[];
    indicatorList?: IndicatorList[];
    freeTag?: string;
    price?: string;
    goodCommentCount?: number;
    goodCommentList?: pennantItemData[];
    ghCount?: string;
    pricePre?: string;
    btnInfo?: ButtonInfo;
    actionInfo?: ActionInfo;
    showPortraitTag?: PortraitTag[];
    onCardClick?: () => void;
    onBtnClick?: () => void;
}

export interface AttributeTags {
    text: string;
    key: string;
    color: string;
    borderColor: string;
}

export interface ExpertGoodAt {
    type?: string;
    value?: string;
}

export interface IndicatorList {
    text: string;
    value: string;
    highLightColor: string;
}
