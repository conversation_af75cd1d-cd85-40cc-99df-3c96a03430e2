.docInfoWrapper {
    line-height: 1;

    .docNameWrapper {
        color: #1f1f1f;
        letter-spacing: 0;
        line-height: 42px;
        overflow: hidden;

        .name {
            color: #1f1f1f;
            letter-spacing: 0;
            line-height: 54px;
            flex-shrink: 0;
            display: inline;
        }

        .line {
            flex-shrink: 0;
            display: inline;
        }

        .department {
            white-space: nowrap;
            display: inline;
        }

        .portraitTagWrapper {
            .tagWrapper {
                display: inline-block;
                box-sizing: border-box;
                white-space: nowrap;

                .tagTxt {
                    line-height: 36px;
                }
            }
        }
    }

    .hosInfoWrapper {
        align-items: center;

        .hosName {
            color: #1f1f1f;
            line-height: 45px;
        }

        .tagWrap {
            min-width: 112px;
            font-size: 0;
        }

        .tagListWrapper {
            white-space: nowrap;
        }
    }

    .goodAtWrapper {
        .expertGoodText {
            color: #525252;
        }
    }

    .tips {
        color: #858585;
        flex-wrap: wrap;
        line-height: 54px;

        .serviceVal {
            margin-left: 6px;
            font-size: 48px;
            line-height: 48px;
        }

        .tipsLine {
            height: 42px;
            margin: 0 18px;
            position: relative;
            width: 0.99px;
            margin-bottom: 3px;
            background: #858585;
        }

        .tipsLine::after {
            content: '';
            position: absolute; /* 把父视图设置为relative，方便定位 */
            top: 0;
            left: 0;
            width: 200%;
            height: 200%;
            transform: scale(0.5);
            transform-origin: 0 0;
            box-sizing: border-box;
            border-radius: 40px;
            border: 1px solid #b6b6b6;
        }

        .blue {
            color: #00c8c8;
        }

        .grey {
            color: rgb(82 82 82);
        }
    }

    .priceServiceWrapper {
        width: 100%;

        .priceWrapper {
            color: #fd503e;

            .prePrice {
                color: #fd503e;
                font-weight: 600;
            }

            .price {
                position: relative;
                top: 4.5px;
            }

            .tip {
                color: #858585;
            }
        }

        .freeImgIcon {
            width: 36px;
            height: 36px;
        }

        .freeTag {
            color: #7c68f3;
        }
    }

    .expertPennant {
        height: 54px;
        line-height: 54px;
        font-size: 36px;
        font-family: PingFangSC-Regular;
        border-radius: 12px;
        overflow: hidden;
        display: flex;
        align-items: center;
        color: #bd4c00;

        .img {
            flex: none;
            margin-right: 9px;
        }

        .text {
            display: inline-block;
            margin-right: 21px;
        }

        .total {
            display: flex;
            border-radius: 12px;
            padding-left: 21px;
            align-items: center;
            font-weight: 700;
            font-family: PingFangSC-Medium;
            background: linear-gradient(180deg, #fff7f1 0%, #ffcfb1 100%);
            background-size: contain;
            position: relative;
            z-index: 1;
            white-space: nowrap;
            text-align: center;

            &::after {
                content: '';
                position: absolute;
                width: 36px;
                height: 51px;
                right: -27px;
                top: 0;
                background: url('https://med-fe.cdn.bcebos.com/gh/goodEvaluate/sign_bg.png')
                    no-repeat;
                background-size: contain;
            }
        }

        .pennantlist {
            display: inline-block;
            height: 54px;
            padding: 0 18px 0 30px;
            margin-left: -12px;
            background: rgb(255 102 0 / 6%);
            border-radius: 0 12px 12px 0;
            z-index: 0;

            .name {
                display: inline-block;
                white-space: normal;
            }

            .-name:not(:last-child) {
                padding-right: 24px;
                position: relative;
            }
        }
    }
}
