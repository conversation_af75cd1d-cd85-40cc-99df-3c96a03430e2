import cx from 'classnames';
import {memo, useMemo, FC, useCallback, ReactNode} from 'react';
import xss from 'xss';
import {isEmpty} from '@baidu/vita-pages-im/src/utils';
import {View, Text} from '@tarojs/components';
import {Tag} from '@baidu/wz-taro-tools-core';
import {pxTransform} from '@tarojs/taro';
import {HButton, UImage} from '@baidu/health-ui';

import SanjiaTag from '../SanjiaTag';

import type {DoctorInfoProps, ExpertGoodAt} from './index.d';
import styles from './index.module.less';

const DoctorInfo: FC<DoctorInfoProps> = ({
    expertName,
    expertDepartment,
    expertLevel,
    expertHospital,
    attributeTag,
    expertGoodAt,
    goodCommentCount,
    goodCommentList,
    ghCount,
    btnInfo,
    showPortraitTag,
    onBtnClick,
    onCardClick
}) => {
    // 渲染三甲、本地等标签
    const renderAttributeTagList = useMemo(() => {
        return (
            attributeTag &&
            attributeTag?.length > 0 &&
            attributeTag.map((i, idx) => {
                return i.key && i.key === 'hospitalLevel' ? (
                    <View key={idx} className={cx('wz-ml-18')}>
                        <SanjiaTag
                            content={i.text}
                            variant='contained'
                            bgColor='#EBF7EF'
                            color='#39B362'
                        />
                    </View>
                ) : (
                    <Tag
                        key={idx}
                        size='medium'
                        shape='square'
                        variant='outlined'
                        style={{
                            padding: `${pxTransform(9)}
                                ${pxTransform(14)} ${pxTransform(8)}`,
                            fontSize: pxTransform(33),
                            backgroundColor: 'inherit',
                            color: i.color || '#FF6600',
                            borderColor: i.borderColor || 'rgba(255,102,0,0.50)',
                            fontWeight: 'bold'
                        }}
                        className={cx('wz-ml-18', styles.tagWrapper)}
                    >
                        <View className={styles.tagTxt}>{i.text}</View>
                    </Tag>
                );
            })
        );
    }, [attributeTag]);

    // 处理文本样式
    const highlightText = (str: string) => {
        const option = {
            whiteList: {
                span: ['style', 'class'],
                div: ['style', 'class']
            }
        };

        const decodeUnicode = (s: string) =>
            s.replace(/\\u([\dA-Fa-f]{4})/g, (_, g) => String.fromCharCode(parseInt(g, 16)));

        const decodeHTMLEntities = (s: string) =>
            s.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');

        const decoded = decodeHTMLEntities(decodeUnicode(str));

        // 统一替换 em 或 b 为自定义 span
        const highlighted = decoded
            .replace(/<(em|b)>/g, '<div style="color: #00c8c8;display:inline;" class="wz-fw-500">')
            .replace(/<\/(em|b)>/g, '</div>');

        return xss(highlighted, option);
    };

    // 渲染曾经问过Tag
    const renderPortraitTag = useMemo(() => {
        return (
            showPortraitTag &&
            showPortraitTag?.length > 0 &&
            showPortraitTag.slice(0, 1).map((i, idx) => {
                return (
                    <Tag
                        key={idx}
                        size='medium'
                        shape='square'
                        variant='outlined'
                        style={{
                            padding: `${pxTransform(9)}
                                ${pxTransform(14)} ${pxTransform(8)}`,
                            fontSize: pxTransform(33),
                            backgroundColor: 'inherit',
                            color: '#858585',
                            borderColor: 'rgba(255,102,0,0.50)'
                        }}
                        className={cx(styles.tagWrapper, 'wz-fw-500')}
                    >
                        <View className={styles.tagTxt}>{i.value}</View>
                    </Tag>
                );
            })
        );
    }, [showPortraitTag]);

    const genComponent = useCallback((type: string, itemData: ExpertGoodAt) => {
        const componentsMap: {
            [k in string]: ReactNode;
        } = {
            text: (
                /* bca-disable */
                <View
                    className={cx(styles.expertGoodText, 'wz-taro-ellipsis')}
                    dangerouslySetInnerHTML={{
                        __html: highlightText(itemData?.value ? `擅长：${itemData?.value}` : '')
                    }}
                />
            ),
            highLightText: (
                <Text className={cx(styles.expertGoodHighLightText, 'wz-fw-500')}>
                    {itemData?.value}
                </Text>
            )
        };

        return componentsMap[type] || null;
    }, []);

    // 文本展示
    const genContext = useCallback(() => {
        if (expertGoodAt) {
            return (
                !isEmpty(expertGoodAt) &&
                expertGoodAt?.map(i => {
                    return (
                        <View className={styles.expertGoodContentItem} key={i?.value}>
                            {genComponent(i?.type || '', i || '')}
                        </View>
                    );
                })
            );
        }
    }, [expertGoodAt, genComponent]);

    const pennantStr = useMemo(() => {
        if (goodCommentList?.length) {
            const newStr = goodCommentList.map(el => `${el.name}${el.count}`);
            return newStr.join('·');
        }
    }, [goodCommentList]);

    return (
        <View
            className={cx(styles.docInfoWrapper)}
            onClick={e => {
                e.stopPropagation();
                onCardClick?.();
            }}
        >
            {/* 医生姓名、等级、科室 */}
            <View
                className={cx(styles.docNameWrapper, 'wz-flex, wz-col-center, wz-mb-30, wz-fs-42')}
            >
                <View className={cx(styles.name, 'wz-mr-18, wz-fs-54, wz-fw-500')}>
                    {expertName && expertName?.length > 5
                        ? `${expertName.slice(0, 4)}...`
                        : expertName}
                </View>
                <View className={cx(styles.line, 'wz-mr-18, wz-fw-400, wz-taro-ellipsis')}>
                    {expertLevel}
                </View>
                <View className={cx(styles.department, 'wz-mr-18, wz-fw-400, wz-taro-ellipsis')}>
                    {expertDepartment}
                </View>
                {showPortraitTag && !isEmpty(showPortraitTag) && (
                    <View className={cx(styles.portraitTagWrapper)}>{renderPortraitTag}</View>
                )}
            </View>

            {/* 医院信息 */}
            <View className={cx(styles.hosInfoWrapper, 'wz-flex, wz-mb-30')}>
                {/* 医院名称 */}
                <View className={cx(styles.hosName, 'wz-fs-42, wz-fw-500, wz-taro-ellipsis')}>
                    {expertHospital}
                </View>
                {!isEmpty(attributeTag) && (
                    <View className={cx(styles.tagListWrapper, 'wz-flex')}>
                        {renderAttributeTagList}
                    </View>
                )}
            </View>

            {/* 门诊好评 */}
            {goodCommentList && (
                <View className={cx(styles.expertPennant, 'wz-mb-30')}>
                    <View className={styles.total}>
                        <UImage
                            src='https://med-fe.cdn.bcebos.com/gh/goodEvaluate/icon_good1.png'
                            className={styles.img}
                            width={36}
                            height={36}
                            hackWhiteStroke={false}
                        />
                        <View className={styles.text}>门诊好评{goodCommentCount}个</View>
                    </View>
                    {!!(goodCommentList && goodCommentList.length) && (
                        <View className={cx(styles.pennantlist, 'c-line-clamp1')}>
                            {pennantStr}
                        </View>
                    )}
                </View>
            )}

            {/* 擅长 */}
            {expertGoodAt && expertGoodAt?.length > 0 && (
                <View className={cx(styles.goodAtWrapper, 'wz-fs-42, wz-fw-400, wz-mb-30')}>
                    <View className={cx(styles.expertGoodAt)}>{genContext()}</View>
                </View>
            )}

            {/* 价格 & 次数 */}
            <View
                className={cx(
                    styles.priceServiceWrapper,
                    'wz-flex, wz-col-center, wz-row-between, wz-fs-42'
                )}
            >
                {/* 咨询量 */}
                <View className={cx('wz-flex, wz-fs-42', styles.tips)}>
                    {ghCount && (
                        <View className={cx('wz-flex')}>
                            <View>
                                挂号量
                                <Text className={cx(styles.blue, styles.serviceVal, 'wz-fw-500')}>
                                    {ghCount}
                                </Text>
                            </View>
                        </View>
                    )}
                </View>
                {btnInfo && (
                    <HButton
                        className='wz-fw-500'
                        text={btnInfo?.value || '去咨询'}
                        size={42}
                        width={216}
                        height={96}
                        padding='0 0'
                        bgColor='linear-gradient(90deg,#00CFA3,#00D3EA)'
                        onClick={e => {
                            e.stopPropagation();
                            onBtnClick?.();
                        }}
                    />
                )}
            </View>
        </View>
    );
};

DoctorInfo.displayName = 'DoctorGuahaoInfo';
export default memo(DoctorInfo);
