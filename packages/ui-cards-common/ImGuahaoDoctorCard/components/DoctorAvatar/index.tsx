import {FC, memo} from 'react';
import cx from 'classnames';
import {View} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import type {DoctorAvatarProps} from './index.d';

import styles from './index.module.less';

const DoctorAvatar: FC<DoctorAvatarProps> = ({isOnline, expertPic}) => {
    return (
        <View className={cx(styles.avatarWrapper)}>
            {expertPic && (
                <WImage
                    className={cx(styles.avatar, isOnline === 1 && styles.onlineBorder, 'wz-mb-15')}
                    src={expertPic}
                    mode='aspectFill'
                />
            )}
            {isOnline === 1 && <View className={cx(styles.online, 'wz-fs-33')}>在线</View>}
        </View>
    );
};

DoctorAvatar.displayName = 'DoctorAvatar';
export default memo(DoctorAvatar);
