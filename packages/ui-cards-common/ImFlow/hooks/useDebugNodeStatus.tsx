import {useEffect} from 'react';
import {ThinkingChainProps} from '../components/ThinkingChain/index.type';

/**
 * 思维链组件的调试 Hook
 * @param plan 节点数据
 * @param nodeStates 节点状态
 * @param isComponentRendering 组件是否正在渲染
 * @param debug 是否启用调试
 */
export const useDebugNodeStatus = (
    plan: ThinkingChainProps['plan'],
    nodeStates: unknown,
    isComponentRendering: boolean,
    debug: boolean = false
) => {
    // 初始化节点状态日志
    useEffect(() => {
        if (!plan || !debug || !isComponentRendering) return;

        console.groupCollapsed('[ThinkingChain] 节点初始化状态');
        plan.forEach(item => {
            const state = nodeStates[item.id] || {};
            console.log(`节点 ${item.name} (ID: ${item.id})`, {
                startTime: state.startTime,
                completed: state.completed,
                status: item.status
            });
        });
        console.groupEnd();
    }, [plan, nodeStates, debug, isComponentRendering]);

    // 节点状态变化日志
    useEffect(() => {
        if (!plan || !debug || !isComponentRendering) return;

        const newStates = {...nodeStates};
        let hasChanged = false;
        const changedNodes: any[] = [];

        plan.forEach(item => {
            const prevState = nodeStates[item.id];
            const currentState = newStates[item.id] || {};

            // 检测状态变化
            if (!prevState && currentState) {
                hasChanged = true;
                changedNodes.push({
                    action: 'added',
                    node: item,
                    state: currentState
                });
            } else if (prevState && currentState.completed !== prevState.completed) {
                hasChanged = true;
                changedNodes.push({
                    action: 'status-changed',
                    node: item,
                    from: prevState.completed,
                    to: currentState.completed
                });
            }
        });

        if (hasChanged && debug) {
            console.groupCollapsed(
                `[ThinkingChain] 节点状态更新 (${changedNodes.length}个节点变化)`
            );
            changedNodes.forEach(change => {
                console.log(`节点 ${change.node.name} (ID: ${change.node.id})`, {
                    action: change.action,
                    ...(change.action === 'status-changed'
                        ? {from: change.from, to: change.to}
                        : {})
                });
            });
            console.groupEnd();
        }
    }, [plan, nodeStates, debug, isComponentRendering]);

    // 定期打印实时状态
    useEffect(() => {
        if (!plan || !debug || !isComponentRendering) return;

        const interval = setInterval(() => {
            console.groupCollapsed(
                `[ThinkingChain] 实时节点状态 (${new Date().toLocaleTimeString()})`
            );
            plan.forEach(item => {
                const state = nodeStates[item.id] || {startTime: 0, completed: false};
                console.log(`节点 ${item.name} (ID: ${item.id})`, {
                    completed: state.completed,
                    elapsedTime: Date.now() - state.startTime,
                    status: item.status
                });
            });
            console.groupEnd();
        }, 3000);

        return () => clearInterval(interval);
    }, [plan, nodeStates, debug, isComponentRendering]);
};
