//初始化按钮组件
import {memo, type FC, useCallback, useEffect} from 'react';
import {View} from '@tarojs/components';
import {HButton} from '@baidu/health-ui';
import cx from 'classnames';
import {hooks, utils} from '@baidu/vita-pages-im';
import {debounce} from 'lodash-es';

import MarkDown from '../MarkDown';

import {MarkDownBtnProps} from './index.d';
import styles from './index.module.less';

const MarkDownBtn: FC<MarkDownBtnProps> = ({markdownBtn, msgId, ext}) => {
    const {useCapsuleToolsController, useUpload} = hooks;
    const {handleUpload} = useUpload();
    const {updateWzMessage} = useCapsuleToolsController();
    const {actionInfo, description} = markdownBtn || {};
    const {interaction, interactionInfo, value} = actionInfo || {};

    // 曝光点位
    useEffect(() => {
        utils?.ubcCommonViewSend({
            value: 'im_flow_markdown_btn_view',
            ext: {
                product_info: {
                    msgId,
                    ...ext
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 发送消息
    const handleClickSendMsg = useCallback(() => {
        if (interaction === 'sendMsg') {
            updateWzMessage?.(interactionInfo, {
                sceneType: 'imFlowOptions'
            });
            utils?.ubcCommonClkSend({
                value: 'im_flow_markdown_btn_click',
                ext: {
                    product_info: {
                        msgId,
                        ...ext
                    }
                }
            });
        }
        if (interaction === 'sendImg') {
            handleUpload({count: 1, sceneType: 'imFlowOptions'});
            utils?.ubcCommonClkSend({
                value: 'im_flow_markdown_btn_img'
            });
        }
    }, [ext, handleUpload, interaction, interactionInfo, msgId, updateWzMessage]);

    return (
        <View className={styles.markdownBtn}>
            <MarkDown content={description} msgEnd={true} />
            <HButton
                onClick={debounce(() => handleClickSendMsg(), 500)}
                className={cx(styles.markdownBtnBtn, 'c-click-status')}
                text={value}
                size={48}
            />
        </View>
    );
};
MarkDownBtn.displayName = 'MarkDownBtn';
export default memo(MarkDownBtn);
