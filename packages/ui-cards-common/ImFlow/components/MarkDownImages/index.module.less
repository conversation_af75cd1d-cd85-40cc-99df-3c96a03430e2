.markdownImage {
    position: relative;
    border-radius: 45px;
    border: 1.5px solid #dcdde0;

    &:nth-child(3n) {
        margin-right: 0;
    }

    &Mask {
        position: absolute;
        inset: 0;
        z-index: 9;
        color: #fff;
        display: flex;
        flex-direction: column;
        border-radius: 45px;
    }

    &View {
        background: #00000080;
        backdrop-filter: blur(20px);
    }

    &Text {
        line-height: 54px;
    }

    &Num {
        background: rgb(0 0 0 / 30%);
        font-weight: 700;
    }

    &Btn {
        width: 144px;
        height: 72px;
        border-radius: 40px;
        border: 1.5px solid #ffffff80;
        font-weight: 700;
        box-sizing: border-box;
    }

    &Con {
        border-radius: 45px;
        overflow: hidden;
    }
}
