//初始化按钮组件
import {memo, type FC, useEffect, useState, useCallback} from 'react';
import cx from 'classnames';
import {isEmpty} from 'lodash';
import {View} from '@tarojs/components';
import {HImage} from '@baidu/health-ui';
import {previewImage, pxTransform, nextTick} from '@tarojs/taro';
import {utils} from '@baidu/vita-pages-im';

import {MarkDownImagesProps} from './index.d';
import styles from './index.module.less';

const MarkDownImages: FC<MarkDownImagesProps> = ({images, msgId, ext, isFinish, className}) => {
    const len = images?.length || 0;
    const [list, setList] = useState<MarkDownImagesProps['images']>([]);
    const restImagesNum = len - 3;
    const [customStyle, setCustomStyle] = useState({width: '', height: ''});

    const imgSize = useCallback(() => {
        switch (len) {
            case 1:
                return {
                    width: `calc(100vw - ${pxTransform(162)})`,
                    height: `calc(56.67vw - ${pxTransform(91.8)})`
                };
            case 2:
                return {
                    width: `calc(50vw - ${pxTransform(93)})`,
                    height: `calc(50vw - ${pxTransform(93)})`
                };
            default:
                return {
                    width: `calc(33.33vw - ${pxTransform(70)})`,
                    height: `calc(33.33vw - ${pxTransform(70)})`
                };
        }
    }, [len]);

    useEffect(() => {
        const imgStyle = imgSize();
        setCustomStyle(imgStyle);
    }, [imgSize]);

    useEffect(() => {
        images && setList(images);
    }, [images]);

    // 曝光点位
    useEffect(() => {
        nextTick(() => {
            const showImgs = restImagesNum ? (images || []).slice(0, 3) : images || [];
            const resImgs = restImagesNum ? (images || []).slice(3) : [];
            !isEmpty(showImgs) &&
                showImgs.forEach((img, index) => {
                    utils?.ubcCommonViewSend({
                        value: 'ImFlowImage',
                        ext: {
                            pos: index + 1,
                            product_info: {
                                msgId,
                                image: img,
                                ...ext
                            }
                        }
                    });
                });
            !isEmpty(resImgs) &&
                utils?.ubcCommonViewSend({
                    value: 'ImFlowImageMore',
                    ext: {
                        product_info: {
                            msgId,
                            imageList: resImgs,
                            ...ext
                        }
                    }
                });
        });
    }, [msgId]);

    // 更改敏感图片蒙层状态
    const changeMaskStatus = useCallback(
        ind => {
            const newlist = [...(list || [])];
            newlist[ind].hasMask = false;
            setList(newlist);
        },
        [list]
    );

    const handleViewMore = useCallback(
        (img, ind) => {
            const {origin = ''} = img || {};
            previewImage({
                current: origin,
                urls: (images || []).map(item => item.origin || '')
            });
            utils.ubcCommonClkSend({
                value: 'ImFlowImageMore',
                ext: {
                    pos: ind + 1,
                    product_info: {
                        msgId,
                        image: img,
                        ...ext
                    }
                }
            });
        },
        [ext, images, msgId]
    );

    // 展示预览图片
    const changePreviewImage = useCallback(
        (img, ind) => {
            const {origin = ''} = img || {};
            previewImage({
                current: origin,
                urls: (images || []).map(item => item.origin || '')
            });
            utils.ubcCommonClkSend({
                value: 'ImFlowImage',
                ext: {
                    pos: ind + 1,
                    product_info: {
                        msgId,
                        image: img,
                        ...ext
                    }
                }
            });
        },
        [ext, images, msgId]
    );

    const handleErrorImage = useCallback(
        (img, ind) => {
            utils.ubcCommonViewSend({
                value: 'ImFlowImageError',
                ext: {
                    pos: ind + 1,
                    product_info: {
                        msgId,
                        image: img,
                        ...ext
                    }
                }
            });
        },
        [ext, msgId]
    );

    return isFinish && images ? (
        <View className={cx(styles.markdownImages, 'wz-flex wz-mb-45', className || '')}>
            {list &&
                (restImagesNum ? list.slice(0, 3) : list).map((img, index) => {
                    const {maskInfo = {}, hasMask = false} = img;
                    return (
                        <View key={index} className={cx(styles.markdownImage, 'wz-mr-24')}>
                            {index === 2 && restImagesNum ? (
                                <View
                                    className={cx(
                                        styles.markdownImageMask,
                                        styles.markdownImageNum,
                                        'wz-flex wz-text-center wz-fs-54 wz-row-center'
                                    )}
                                    style={customStyle}
                                    onClick={() => handleViewMore(img, index)}
                                >
                                    <View className='wz-plr-36'>+{restImagesNum}</View>
                                </View>
                            ) : hasMask ? (
                                <View
                                    className={cx(
                                        'wz-text-center wz-fs-36 wz-row-center wz-col-center',
                                        styles.markdownImageMask,
                                        styles.markdownImageView
                                    )}
                                    style={customStyle}
                                >
                                    <View className={cx(styles.markdownImageText, 'wz-plr-36')}>
                                        {maskInfo.text || ''}
                                    </View>
                                    <View
                                        onClick={() => changeMaskStatus(index)}
                                        className={cx(
                                            styles.markdownImageBtn,
                                            'wz-mt-27 wz-flex wz-row-center'
                                        )}
                                    >
                                        查看
                                    </View>
                                </View>
                            ) : null}
                            <HImage
                                src={img.origin || ''}
                                modeType='aspectFill'
                                className={styles.markdownImageCon}
                                width={customStyle.width}
                                height={customStyle.height}
                                onClick={() => changePreviewImage(img, index)}
                                onError={() => handleErrorImage(img, index)}
                            />
                        </View>
                    );
                })}
        </View>
    ) : null;
};
MarkDownImages.displayName = 'MarkDownImages';
export default memo(MarkDownImages);
