.medicalCard {
    border-color: #ededf0;

    .avator {
        width: 156px;
        height: 156px;
        flex: none;
    }

    .content {
        flex-direction: column;

        .hospitalName {
            color: #000311;
            max-width: 900px;
        }

        .score {
            color: #ff471a;
        }

        .remark {
            color: #802e00;
            max-width: 750px;
        }

        .fudanWrap {
            height: 48px;
            line-height: 48px;
            background-color: #fffbe5e5;
            border-radius: 12px;

            .fudanIcon {
                width: 144px;
                height: 48px;
            }

            .fudanText {
                color: #802e00;
            }
        }

        .tagWrap {
            margin-bottom: 60px;

            .tag {
                height: 48px;
                box-sizing: border-box;
                padding-top: 6px;
                padding-bottom: 6px;
                background-color: #edf0fa;
            }
        }

        .btnGroup {
            .btn {
                width: 426px;
                text-align: center;
                height: 120px;
                line-height: 120px;
                background-color: #e4faf9;
            }
        }
    }
}
