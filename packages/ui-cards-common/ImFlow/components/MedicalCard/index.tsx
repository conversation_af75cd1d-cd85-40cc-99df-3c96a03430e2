import {memo, useEffect, useCallback} from 'react';
import {View, Text} from '@tarojs/components';
import cx from 'classnames';
import {WImage} from '@baidu/wz-taro-tools-core';
import {imgUrlMap} from '@baidu/vita-pages-im/src/constants/resourcesOnBos';
import {
    ubcCommonViewSend,
    ubcCommonClkSend
} from '@baidu/vita-pages-im/src/utils/generalFunction/ubc';
import SanjiaTag from '../../../../ui-cards-common/ImDoctorCard/components/SanjiaTag';
import {isEmpty} from '../../../../pages-im/src/utils';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';

import type {medicalCardProps} from './index.d';
import styles from './index.module.less';

const MedicalCard = memo((props: medicalCardProps) => {
    const {pos = null} = props;
    const {
        hospitailLogo = '',
        hospitailName = '',
        hospitailScore = '',
        hospitailRemark = '',
        fudanHospitalRank = '',
        attributeTag = [],
        btnInfo = [],
        actionInfo = {}
    } = props?.content || {};

    useEffect(() => {
        const ubcEvents = !pos
            ? ['doctorcard', 'doctorcard_yygh', 'doctorcard_ckjs']
            : ['hospitallist'];

        ubcEvents.forEach(event => ubcCommonViewSend({value: event}));
    }, [pos]);

    // 医院卡跳转
    const handleJump = useCallback(
        (data, idx = 0, type = '') => {
            const {interaction, interactionInfo} = data;
            if (interaction === 'openLink') {
                const url = interactionInfo?.url;
                if (!url) return;

                const isOuterUrl = url.startsWith('http') || url.startsWith('https');
                const openType =
                    isOuterUrl && process.env.TARO_ENV === 'swan' ? 'easybrowse' : 'navigate';

                navigate({
                    url,
                    openType
                });

                if (type && !pos) {
                    ubcCommonClkSend({
                        value: idx ? 'doctorcard_yygh' : 'doctorcard_ckjs'
                    });
                } else if (type && pos) {
                    ubcCommonClkSend({
                        value: idx ? `hospitallist_qck_${pos}` : `hospitallist_qgh_${pos}`
                    });
                }
            }
        },
        [pos]
    );

    return (
        <View
            className={cx(
                styles.medicalCard,
                'wz-flex wz-row-left wz-col-top wz-ptb-63 wz-mb-45 wz-taro-hairline--bottom'
            )}
            onClick={() => handleJump(actionInfo)}
        >
            <WImage src={hospitailLogo} round className={cx(styles.avator, 'wz-pr-45')} />
            <View className={cx(styles.content, 'wz-flex wz-col-top')}>
                <Text
                    className={cx(
                        styles.hospitalName,
                        'wz-fw-500 wz-fs-48 wz-mb-27 wz-taro-ellipsis'
                    )}
                >
                    {hospitailName}
                </Text>
                <View className='wz-flex wz-mb-27'>
                    <Text className={cx(styles.score, 'wz-fw-500 wz-fs-42 wz-mr-18')}>
                        {hospitailScore}
                    </Text>
                    <Text className={cx(styles.remark, 'wz-fs-42 wz-taro-ellipsis')}>
                        {hospitailRemark}
                    </Text>
                </View>
                <View className={cx(styles.fudanWrap, 'wz-mb-27 wz-flex wz-col-center')}>
                    <WImage src={imgUrlMap.fudanIcon} className={styles.fudanIcon} />
                    <Text className={cx(styles.fudanText, 'wz-plr-9 wz-fs-36 wz-fw-500')}>
                        {fudanHospitalRank}
                    </Text>
                </View>
                {!isEmpty(attributeTag) ? (
                    <View className={cx(styles.tagWrap, 'wz-flex wz-col-center')}>
                        {attributeTag.map(el =>
                            el.key === 'hospitalLevel' ? (
                                <SanjiaTag
                                    key={el.key}
                                    content={el.text}
                                    bgColor='#EBF7EF'
                                    color='#39B362'
                                    variant='contained'
                                />
                            ) : (
                                <View
                                    key={el.key}
                                    className={cx(
                                        styles.tag,
                                        'wz-mr-18 wz-plr-12 wz-fs-36 wz-ml-18 wz-br-9'
                                    )}
                                >
                                    {el.text}
                                </View>
                            )
                        )}
                    </View>
                ) : null}

                {!isEmpty(btnInfo) ? (
                    <View className={cx(styles.btnGroup, 'wz-flex wz-row-between')}>
                        {btnInfo.map((el, index) => (
                            <View
                                key={index}
                                className={cx(
                                    styles.btn,
                                    'wz-fs-42 wz-fw-500 wz-br-54',
                                    index === 1 && 'wz-ml-27'
                                )}
                                onClick={() => handleJump(el, index, 'btn')}
                            >
                                {el.value}
                            </View>
                        ))}
                    </View>
                ) : null}
            </View>
        </View>
    );
});

MedicalCard.displayName = 'MedicalCard';
export default MedicalCard;
export {type medicalCardProps};
