export interface medicalCardProps {
    pos?: number;
    content: {
        hospitailLogo: string;
        hospitailName: string;
        hospitailScore: string;
        hospitailRemark: string;
        fudanHospitalRank: string;
        actionInfo: {
            interaction: string;
            interactionInfo: {
                url: string;
            };
        };
        attributeTag: {
            text: string;
            key: string;
            color: string;
            borderColor: string;
        }[];
        btnInfo: {
            value: string;
            disabled: boolean;
            interaction: string;
            interactionInfo: {
                url: string;
            };
        }[];
    };
}
