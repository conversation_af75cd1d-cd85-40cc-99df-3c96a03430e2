export const markdownTest = `
# 一级标题
## 二级标题
### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题

## 文本格式
这是普通文本
*这是斜体文本*
**这是粗体文本**
***这是粗斜体文本***
~~这是删除线文本~~

## 列表
### 无序列表
- 项目1
- 项目2
  - 子项目2.1
  - 子项目2.2
- 项目3

### 有序列表
1. 第一项
2. 第二项
   1. 子项2.1
   2. 子项2.2
3. 第三项

## 引用
> 这是一段引用文本
> 
> 这是引用的第二段
> > 这是嵌套引用

## 代码
\`\`\`javascript
const hello = 'world';
console.log(hello);
\`\`\`

行内代码: \`const x = 1\`

## 链接和图片
[这是一个链接](https://www.example.com)
![这是一张图片](https://med-fe.cdn.bcebos.com/health-pc/logo%40x2.png)

## 表格
| 表头1 | 表头2 | 表头3 |
|-------|-------|-------|
| 内容1 | 内容2 | 内容3 |
| 行2列1| 行2列2| 行2列3|

## 水平线
---
***
___

## 任务列表
- [x] 已完成任务
- [ ] 未完成任务
- [ ] 另一个未完成任务

## 脚注
这是一个带有脚注的文本[^1]

[^1]: 这是脚注内容

## 转义字符
\\* 星号
\\_ 下划线
\\\` 反引号

## 数学公式（如果支持）
行内公式: $E=mc^2$

块级公式:
$$
\\frac{n!}{k!(n-k)!} = \\binom{n}{k}
$$

## 高亮（如果支持）
==这是高亮文本==

## 上标和下标
H~2~O 是水的化学式
X^2^ 是平方

## 定义列表
术语1
: 定义1

术语2
: 定义2
`;
