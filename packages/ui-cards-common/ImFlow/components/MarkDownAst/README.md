# MarkDownAst 组件使用文档

## 简介

MarkDownAst 是一个基于 marked 库的 Markdown 渲染组件，支持在 Taro 环境中渲染 Markdown 内容。该组件提供了丰富的 Markdown 语法支持，并针对移动端做了优化。

## 基本用法

```tsx
import MarkDownAst from './components/MarkDownAst';

// 基础用法
<MarkDownAst content="# 标题" />

// 带自定义类名
<MarkDownAst content="# 标题" className="custom-class" />
```

## 支持的 Markdown 语法

### 1. 标题

支持 1-6 级标题：

```markdown
# 一级标题

## 二级标题

### 三级标题

#### 四级标题

##### 五级标题

###### 六级标题
```

### 2. 文本格式

- 斜体：`*斜体文本*`
- 粗体：`**粗体文本**`
- 删除线：`~~删除线文本~~`

### 3. 列表

- 无序列表：

```markdown
- 项目1
- 项目2
    - 子项目2.1
    - 子项目2.2
```

- 有序列表：

```markdown
1. 第一项
2. 第二项
    1. 子项2.1
    2. 子项2.2
```

### 4. 引用

```markdown
> 这是一段引用文本
>
> 这是引用的第二段
>
> > 这是嵌套引用
```

### 5. 代码

- 代码块：

````markdown
```javascript
const hello = 'world';
console.log(hello);
```
````

- 行内代码：`` `const x = 1` ``

### 6. 链接和图片

- 链接：`[链接文本](https://example.com)`
- 图片：`![图片描述](图片URL)`

### 7. 表格

```markdown
| 表头1  | 表头2  | 表头3  |
| ------ | ------ | ------ |
| 内容1  | 内容2  | 内容3  |
| 行2列1 | 行2列2 | 行2列3 |
```

### 8. 其他功能

- 水平线：`---` 或 `***` 或 `___`
- 换行：`<br>`
- HTML 内容支持
- 自定义 loading 状态：`:loading:`

## 特殊功能

### 1. Loading 状态

组件支持通过 `:loading:` 语法显示加载状态：

```markdown
正在加载中:loading:
```

### 2. 链接处理

- H5 环境：直接使用 `window.href` 打开链接
- 小程序环境：使用 `utils.navigate` 进行页面跳转

### 3. 图片处理

- 支持懒加载
- 支持图片说明文字
- 自适应宽度

## 样式定制

组件提供了丰富的样式类名，可以通过 CSS 进行自定义：

- `.markdown` - 根容器
- `.h1` 到 `.h6` - 标题样式
- `.p` - 段落样式
- `.strong` - 粗体样式
- `.em` - 斜体样式
- `.a` - 链接样式
- `.ol` 和 `.ul` - 列表样式
- `.code-block` - 代码块样式
- `.table` - 表格样式
- `.blockquote` - 引用样式
- `.loading-icon` - 加载图标样式

## 注意事项

1. 组件默认使用 PingFang SC 字体
2. 默认字体大小为 51px
3. 行高为 78px
4. 支持自动换行（word-break: break-all）
5. 表格支持横向滚动
6. 代码块支持横向滚动

## 性能优化

1. 使用 `memo` 进行组件缓存
2. 图片使用 `lazyLoad` 属性
3. 表格使用虚拟滚动优化

## 安全特性

1. 支持 XSS 防护
2. 链接跳转安全处理
3. 图片加载安全处理

## 示例代码

```tsx
import React from 'react';
import MarkDownAst from './components/MarkDownAst';

const Example = () => {
    const markdownContent = `
# 示例文档

这是一个**粗体**文本，这是*斜体*文本。

## 列表示例
- 项目1
- 项目2
  1. 子项2.1
  2. 子项2.2

## 代码示例
\`\`\`javascript
const hello = 'world';
console.log(hello);
\`\`\`

## 表格示例
| 名称 | 类型 | 描述 |
|------|------|------|
| name | string | 用户名 |
| age | number | 年龄 |

正在加载中:loading:
  `;

    return (
        <div className='markdown-container'>
            <MarkDownAst content={markdownContent} />
        </div>
    );
};

export default Example;
```

## 依赖项

- @tarojs/components
- marked
- react
- @baidu/vita-pages-im (用于小程序环境下的导航)

## 浏览器支持

- 支持所有现代浏览器
- 支持 H5 环境
- 支持小程序环境（通过 Taro 适配）
