import {memo, useCallback, useEffect} from 'react';
import {View} from '@tarojs/components';
import cx from 'classnames';
import {hooks, utils} from '@baidu/vita-pages-im';

import styles from './index.module.less';
import {QuickReplyProps} from './index.d';

const QuickReply = memo((props: QuickReplyProps) => {
    const {quickReply = [], msgId = '', ext = {}, ubcValue = 'ImFlowQuickReply'} = props;
    const {createConversation} = hooks.useConversationDataController();
    const {ubcCommonClkSend, ubcCommonViewSend} = utils;

    useEffect(() => {
        ubcCommonViewSend({
            value: ubcValue,
            ext: {
                product_info: {
                    quickReplyList: quickReply?.map(item => item.content),
                    msgId,
                    ...ext
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 发送快捷回复
    const handleQuickReply = useCallback(
        content => {
            createConversation({
                msg: {
                    type: 'text',
                    content,
                    sceneType: 'imFlowOptions'
                }
            });
            ubcCommonClkSend({
                value: ubcValue,
                ext: {
                    product_info: {
                        quickReply: content,
                        msgId,
                        ...ext
                    }
                }
            });
        },
        [createConversation, ext, msgId, ubcCommonClkSend, ubcValue]
    );

    return (
        <View className={cx('wz-fs-51')}>
            {quickReply?.map((item, index) => {
                if (!item?.content) {
                    return null;
                }

                return (
                    <View key={index}>
                        <View
                            className={cx(
                                styles.aiQuickReplyItem,
                                'wz-ptb-33',
                                'wz-plr-39',
                                'wz-fs-48',
                                'wz-mt-36',
                                'wz-flex',
                                'wz-col-center',
                                'wz-row-left'
                            )}
                            onClick={() => handleQuickReply(item?.content)}
                        >
                            {item?.content}
                        </View>
                    </View>
                );
            })}
        </View>
    );
});

QuickReply.displayName = 'QuickReply';

export default QuickReply;
