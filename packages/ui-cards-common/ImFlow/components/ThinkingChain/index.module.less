/* 外层容器 */
.markdownPlanContainer {
    display: flex;
    justify-content: center;
    width: 100%;
    overflow-x: auto;
    padding: 0 20px;
}

/* 水平排列容器 */
.markdownPlanHorizontal {
    display: flex;
    align-items: center;
    position: relative;
    padding: 48px 0;
    min-width: 100%;
}

/* 节点包装器 */
.planNodeWrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    min-width: 120px;
}

/* 节点主体 */
.planNode {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
}

/* 节点圆点 - 默认样式 */
.planNodeDot {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* pending状态：浅灰色背景 */
.planNodeDot.pending {
    background-color: #eff3f9;
    animation: none;
}

/* doing状态：绿色动画效果 */
.planNodeDot.doing {
    background-color: #00c8c833;
    animation: node-pulse 1.5s infinite;
}

/* completed状态：绿色实心圆 */
.planNodeDot.completed {
    background-color: #00c8c8;
    animation: none;
}

/* 节点中心小圆点 - 默认样式 */
.planNodeInnerDot {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    position: absolute;
    z-index: 1;
}

/* pending状态时小圆点颜色 */
.planNodeInnerDot.pendingInnerDot {
    background-color: #c8d3e4;
}

/* doing状态时小圆点颜色 */
.planNodeInnerDot.doingInnerDot {
    background-color: #00c8c8;
}

/* 显示/隐藏 */
.hide {
    display: none;
}

/* 节点文本 - 默认颜色 */
.planNodeText {
    margin-top: 18px;
    font-size: 48px;
    color: #333;
}

/* pending状态时文字颜色 */
.planNodeText.pendingText {
    color: #b7b9c1;
}

/* 节点间连线 */
.planNodeLine {
    position: absolute;
    top: 24px; /* 垂直居中，与节点圆点中心对齐 */
    right: -50%; /* 向右偏移50%的容器宽度 */
    width: 100%; /* 宽度为整个容器宽度 */
    height: 5px;
    z-index: 1;

    /* 线性渐变实现两端透明效果 */
    background: linear-gradient(
        to right,
        transparent 50px,
        #eff3f9 50px,
        #eff3f9 calc(100% - 50px),
        transparent calc(100% - 50px)
    );
}

/* 节点加载动画 */
@keyframes node-pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.5);
    }

    100% {
        transform: scale(1);
    }
}

/* 绿色对勾 */
.planNodeCheck {
    position: absolute;
    top: 50%;
    left: 50%;
    border-left: 6px solid #fff;
    border-bottom: 6px solid #fff;
    transform: translate(-50%, -75%) rotate(-45deg);
    border-radius: 1px;
    z-index: 2;
    width: 20px;
    height: 10px;
}
