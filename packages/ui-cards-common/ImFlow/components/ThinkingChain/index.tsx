import {FC, useState, useEffect, useMemo, useRef} from 'react';
import {View, Text} from '@tarojs/components';
import cx from 'classnames';
import {useDebugNodeStatus} from '../../hooks/useDebugNodeStatus';
import styles from './index.module.less';
import type {NodeStatus, ThinkingChainProps} from './index.type.ts';

const WAIT_TIME = 1200;

/*  目前针对于皮肤病后端使用的策略算法如下： 对前端需要感知到的是  '相当于假的节点默认值的状态都是completed'
    有1个步骤的场景：1、2：completed，3：取真实的。
    有2个步骤的场景：1：completed，2、3：取真实的。
    有3个步骤的场景：1、2、3：都取真实的。
    有4个步骤的场景：1、2、3：都取真实的。（对应于2、3、4）
**/
const ThinkingChain: FC<ThinkingChainProps> = ({plan, forceNoShow, debug = false}) => {
    // 存储每个节点的开始时间和完成状态
    const [nodeStates, setNodeStates] = useState<{
        [key: string]: {
            startTime: number;
            status: NodeStatus;
            isInitialComplete: boolean;
        };
    }>({});

    // 存储模拟的节点状态
    const [simulatedPlan, setSimulatedPlan] = useState(plan);
    // 存储计时器ID
    const timeoutIds = useRef<ReturnType<typeof setTimeout>[]>([]);
    const intervalIds = useRef<ReturnType<typeof setInterval>[]>([]); // 新增：存储 interval ID
    const isComponentRendering = useRef(true);
    // 添加标志来跟踪是否已启动自动推进
    const hasStartedAutoProgress = useRef(false);
    // 跟踪最后一个节点的真实状态
    const lastNodeRealStatus = useRef<NodeStatus>('pending');

    // 清理所有定时器
    const clearAllTimers = () => {
        // 清除所有 timeout
        timeoutIds.current.forEach(id => clearTimeout(id));
        timeoutIds.current = [];

        // 清除所有 interval
        intervalIds.current.forEach(id => clearInterval(id));
        intervalIds.current = [];
    };

    // 组件卸载时清理定时器
    useEffect(() => {
        return () => {
            clearAllTimers();
            isComponentRendering.current = false;
        };
    }, []);

    // 根据真实状态更新模拟状态 - 特别处理最后一个节点
    useEffect(() => {
        if (!plan || plan.length === 0) return;

        // 更新最后一个节点的真实状态
        lastNodeRealStatus.current = plan[plan.length - 1].status;

        // 检查是否需要更新模拟状态
        let shouldUpdate = false;
        const newSimulatedPlan = [...(simulatedPlan || [])];

        // 只处理最后一个节点
        const lastIndex = plan.length - 1;
        if (newSimulatedPlan[lastIndex] && plan[lastIndex]) {
            // 真实状态领先于模拟状态时更新
            if (
                plan[lastIndex].status === 'completed' &&
                newSimulatedPlan[lastIndex].status !== 'completed'
            ) {
                newSimulatedPlan[lastIndex] = {...plan[lastIndex]};
                shouldUpdate = true;
            }
        }

        if (shouldUpdate) {
            setSimulatedPlan(newSimulatedPlan);
        }
    }, [plan]);

    // 初始化模拟状态和计时器 - 重新播放所有节点的动画
    useEffect(() => {
        if (!plan || plan.length === 0 || hasStartedAutoProgress.current) return;

        // 清除现有计时器
        clearAllTimers();
        hasStartedAutoProgress.current = true;

        // 重置所有节点为pending - 确保重新播放动画
        const resetPlan = plan.map(node => ({...node, status: 'pending' as NodeStatus}));
        setSimulatedPlan(resetPlan);

        // 启动动画序列
        const startAnimationSequence = () => {
            // 处理所有节点（除了最后一个）
            for (let i = 0; i < plan.length - 1; i++) {
                // 立即设置节点为doing
                timeoutIds.current.push(
                    setTimeout(() => {
                        setSimulatedPlan(prev => {
                            const newPlan = [...prev];
                            newPlan[i] = {...newPlan[i], status: 'doing'};
                            return newPlan;
                        });
                    }, i * WAIT_TIME)
                );

                // WAIT_TIME后设置节点为completed
                timeoutIds.current.push(
                    setTimeout(
                        () => {
                            setSimulatedPlan(prev => {
                                const newPlan = [...prev];
                                newPlan[i] = {...newPlan[i], status: 'completed'};
                                return newPlan;
                            });
                        },
                        (i + 1) * WAIT_TIME
                    )
                );
            }

            // 处理最后一个节点 - 特殊逻辑
            const lastIndex = plan.length - 1;

            // 立即设置最后一个节点为doing
            timeoutIds.current.push(
                setTimeout(() => {
                    setSimulatedPlan(prev => {
                        const newPlan = [...prev];
                        newPlan[lastIndex] = {...newPlan[lastIndex], status: 'doing'};
                        return newPlan;
                    });
                }, lastIndex * WAIT_TIME)
            );

            // 等待WAIT_TIME后，检查真实状态
            timeoutIds.current.push(
                setTimeout(
                    () => {
                        // 如果真实状态已经是completed，立即更新
                        if (lastNodeRealStatus.current === 'completed') {
                            setSimulatedPlan(prev => {
                                const newPlan = [...prev];
                                newPlan[lastIndex] = {...newPlan[lastIndex], status: 'completed'};
                                return newPlan;
                            });
                        }
                        // 否则设置一个监听器，等待真实状态更新
                        else {
                            const checkStatusInterval = setInterval(() => {
                                if (lastNodeRealStatus.current === 'completed') {
                                    setSimulatedPlan(prev => {
                                        const newPlan = [...prev];
                                        newPlan[lastIndex] = {
                                            ...newPlan[lastIndex],
                                            status: 'completed'
                                        };
                                        return newPlan;
                                    });
                                    clearInterval(checkStatusInterval);
                                }
                            }, 100);

                            // 存储interval ID以便清理
                            intervalIds.current.push(checkStatusInterval);
                        }
                    },
                    (lastIndex + 1) * WAIT_TIME
                )
            );
        };

        startAnimationSequence();

        return () => {
            clearAllTimers();
        };
    }, [plan]);

    // 初始化节点状态
    useEffect(() => {
        if (simulatedPlan && simulatedPlan.length > 0) {
            const newStates: typeof nodeStates = {};
            simulatedPlan.forEach(item => {
                const isInitialComplete = item.status === 'completed';
                const startTime = isInitialComplete ? Date.now() - WAIT_TIME : Date.now();

                newStates[item.id] = {
                    startTime,
                    status: item.status,
                    isInitialComplete
                };
            });
            setNodeStates(newStates);
        }

        return () => {
            isComponentRendering.current = false;
        };
    }, [simulatedPlan]);

    // 监听节点状态变化
    useEffect(() => {
        if (!simulatedPlan) return;

        const newStates = {...nodeStates};
        let hasChanged = false;

        simulatedPlan.forEach(item => {
            const prevState = nodeStates[item.id];
            if (!prevState) return;

            const newStatus = item.status || 'pending';
            const prevStatus = prevState.status;

            if (newStatus !== prevStatus) {
                newStates[item.id] = {
                    ...prevState,
                    startTime: Date.now(),
                    status: newStatus,
                    isInitialComplete:
                        newStatus === 'completed' && prevStatus !== 'completed'
                            ? false
                            : prevState.isInitialComplete
                };
                hasChanged = true;
            }
        });

        if (hasChanged) setNodeStates(newStates);
    }, [simulatedPlan, nodeStates]);

    // 调试Hook
    useDebugNodeStatus(plan, nodeStates, isComponentRendering.current, debug);

    const shouldRenderNothing = useMemo(() => {
        if (!plan) return true;
        return (
            (plan.length > 0 && plan[plan.length - 1]?.status === 'completed') ||
            plan.every(item => item?.status === 'completed') ||
            forceNoShow
        );
    }, [forceNoShow, plan]);

    if (shouldRenderNothing) {
        isComponentRendering.current = false;
        return null;
    }

    isComponentRendering.current = true;

    return (
        <View className={styles.markdownPlanContainer}>
            <View className={styles.markdownPlanHorizontal}>
                {simulatedPlan?.map((item, index) => {
                    const nodeState = nodeStates[item.id] || {
                        startTime: Date.now(),
                        status: 'pending',
                        isInitialComplete: false
                    };

                    const {status, isInitialComplete} = nodeState;
                    const elapsedTime = Date.now() - nodeState.startTime;
                    const isLast = index === simulatedPlan.length - 1;
                    const isComplete = status === 'completed';

                    // 计算动画是否应该停止
                    const shouldStopAnimation =
                        (isInitialComplete && elapsedTime >= WAIT_TIME) ||
                        (isComplete && (!isInitialComplete || elapsedTime >= WAIT_TIME));

                    // 控制线段显示：不是最后一个节点显示
                    const shouldShowLine = !isLast;

                    return (
                        <View key={`node-${item.id}`} className={styles.planNodeWrapper}>
                            {/* 节点主体 */}
                            <View className={styles.planNode}>
                                <View
                                    className={cx(styles.planNodeDot, {
                                        // pending状态：浅灰色背景
                                        [styles.pending]: status === 'pending',

                                        // doing状态：绿色动画效果
                                        [styles.doing]: status === 'doing' && !shouldStopAnimation,

                                        // completed状态：绿色实心圆
                                        [styles.completed]:
                                            status === 'completed' || shouldStopAnimation
                                    })}
                                >
                                    {/* 中心小圆点 - 所有状态都显示，但颜色不同 */}
                                    <View
                                        className={cx(styles.planNodeInnerDot, {
                                            // 隐藏条件：completed状态或动画停止
                                            [styles.hide]:
                                                status === 'completed' || shouldStopAnimation,
                                            [styles.pendingInnerDot]: status === 'pending',
                                            [styles.doingInnerDot]: status === 'doing'
                                        })}
                                    />
                                    {/* 对勾 - completed状态时显示 */}
                                    {(status === 'completed' || shouldStopAnimation) && (
                                        <View className={styles.planNodeCheck} />
                                    )}
                                </View>
                                {/* 节点文本 */}
                                <Text
                                    className={cx(styles.planNodeText, {
                                        [styles.pendingText]: status === 'pending'
                                    })}
                                >
                                    {item?.name}
                                </Text>
                            </View>

                            {shouldShowLine && <View className={styles.planNodeLine} />}
                        </View>
                    );
                })}
            </View>
        </View>
    );
};

ThinkingChain.displayName = 'ThinkingChain';
export default ThinkingChain;
