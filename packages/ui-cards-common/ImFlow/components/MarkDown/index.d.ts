export interface MarkDdownProps {
    content: string;
    speed?: number;
    stepWordNum?: number;
    className?: string;
    isFinish?: boolean;
    isTypewriter?: boolean;
    forcedShowLoading?: boolean;
    msgEnd?: boolean;
    isShowLoading?: boolean;
    typewriterSuccessCallback?: () => void;
    typewriterStartCallback?: () => void;
    typewriterProcessCallback?: (num: number) => void;
    markdownClassName?: string;
    loadingType?: ':loading:' | ':gradient:';
    isMockTypwer?: boolean;
}
