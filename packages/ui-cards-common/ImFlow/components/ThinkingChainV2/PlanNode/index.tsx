import cx from 'classnames';
import React, {type FC} from 'react';
import {View} from '@tarojs/components';
import styles from './index.module.less';

interface PlanNodeProps {
    text: string;
    delay: number;
    isLast?: boolean;
    animationStarted: boolean;
    animationDuration: number;
    showLine?: boolean;
    forceComplete?: boolean;
}

const PlanNode: FC<PlanNodeProps> = ({
    text,
    delay,
    isLast = false,
    animationStarted,
    animationDuration,
    showLine = true,
    forceComplete = false
}) => {
    return (
        <View className={`wz-flex ${styles.planNodeWrapper} ${isLast ? styles.lastNode : ''}`}>
            <View className={cx(styles.planNode, 'wz-flex')}>
                <View
                    className={cx(styles.planNodeDot, {[styles.forceComplete]: forceComplete})}
                    style={
                        {
                            animationPlayState:
                                animationStarted && !forceComplete ? 'running' : 'paused',
                            '--node-delay': `${delay}s`
                        } as React.CSSProperties
                    }
                >
                    <View
                        className={cx(styles.planNodeInnerDot, {
                            [styles.forceComplete]: forceComplete
                        })}
                        style={
                            {
                                animationPlayState:
                                    animationStarted && !forceComplete ? 'running' : 'paused',
                                '--node-delay': `${delay}s`
                            } as React.CSSProperties
                        }
                    />
                    <View
                        className={cx(styles.planNodeCheck, {
                            [styles.forceComplete]: forceComplete
                        })}
                        style={
                            {
                                animationPlayState:
                                    animationStarted && !forceComplete ? 'running' : 'paused',
                                '--node-delay': `${delay}s`
                            } as React.CSSProperties
                        }
                    />
                </View>
                <View
                    className={cx(styles.planNodeText, {[styles.forceComplete]: forceComplete})}
                    style={
                        {
                            animationPlayState:
                                animationStarted && !forceComplete ? 'running' : 'paused',
                            '--node-delay': `${delay}s`
                        } as React.CSSProperties
                    }
                >
                    {text}
                </View>
            </View>

            {showLine && (
                <View
                    className={styles.planNodeLine}
                    style={
                        {
                            '--line-delay': `${delay + animationDuration}s`,
                            animationPlayState: animationStarted ? 'running' : 'paused'
                        } as React.CSSProperties
                    }
                />
            )}
        </View>
    );
};

export default PlanNode;
