.planNodeWrapper {
    flex: 1;
    flex-direction: column;
    align-items: center;
    position: relative !important;
    min-width: 120px;
}

.planNode {
    flex-direction: column;
    align-items: center;
    position: relative !important;
    z-index: 2;
}

/* 节点圆点 */
.planNodeDot {
    width: 54px;
    height: 54px;
    border-radius: 50%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #eff3f9;

    /* 动画 */
    animation:
        bg-pending-to-doing 0.1s calc(var(--node-delay, 0s) + 0.3s) forwards,
        node-pulse 1.2s calc(var(--node-delay, 0s) + 0.3s) forwards,
        bg-doing-to-completed 0.1s calc(var(--node-delay, 0s) + 1.5s) forwards;
}

/* 节点内圆点 */
.planNodeInnerDot {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    position: absolute;
    z-index: 1;
    background-color: #c8d3e4;

    /* 动画 */
    animation:
        inner-pending-to-doing 0.1s calc(var(--node-delay, 0s) + 0.3s) forwards,
        fade-out 0.2s calc(var(--node-delay, 0s) + 1.5s) forwards;
}

/* 节点对勾 */
.planNodeCheck {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -75%) rotate(-45deg);
    border-radius: 1px;
    z-index: 2;
    width: 20px;
    height: 10px;
    opacity: 0;

    /* 书写效果 */
    animation:
        fade-in 0.1s calc(var(--node-delay, 0s) + 1.5s) forwards,
        draw-check-vertical 0.1s calc(var(--node-delay, 0s) + 1.6s) forwards,
        draw-check-horizontal 0.1s calc(var(--node-delay, 0s) + 1.7s) forwards;
}

/* 节点文本 */
.planNodeText {
    margin-top: 18px;
    font-size: 48px;
    color: #b7b9c1;
    font-weight: 500;
    text-align: center;

    /* 动画 */
    animation: text-active 0s calc(var(--node-delay, 0s) + 0.6s) forwards;
}

/* 连接线 */
.planNodeLine {
    position: absolute;
    top: 24px;
    left: 50%;
    right: -50%;
    height: 2px;

    /* 线性渐变实现两端透明效果 */
    background: linear-gradient(
        to right,
        transparent 50px,
        #eff3f9 50px,
        #eff3f9 calc(100% - 50px),
        transparent calc(100% - 50px)
    );
    z-index: 0;

    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background-color: #00c8c8;
        animation: line-fill 0.5s var(--line-delay, 0s) forwards;
    }
}

/* 覆盖最后一个节点的动画 */
.lastNode {
    /* 节点圆点：保持进行中状态 + 无限呼吸 */
    .planNodeDot {
        animation:
            bg-pending-to-doing 0.1s calc(var(--node-delay, 0s) + 0.3s) forwards,
            node-pulse 1.2s calc(var(--node-delay, 0s) + 0.3s) infinite !important; // 无限循环
    }

    // 内圆点：保持显示不消失
    .planNodeInnerDot {
        animation: inner-pending-to-doing 0.1s calc(var(--node-delay, 0s) + 0.3s) forwards !important;
    }

    // 对勾：始终隐藏
    .planNodeCheck {
        animation: none !important;
        opacity: 0 !important;
    }
}

/* 强制完成状态 */
.forceComplete {
    &.planNodeDot {
        background-color: #00c8c8 !important;
        animation: none !important;
    }

    &.planNodeInnerDot {
        opacity: 0 !important;
        animation: none !important;
    }

    &.planNodeCheck {
        opacity: 1 !important;
        animation: none !important;

        /* 确保对勾显示 */
        border-left: 6px solid #fff;
        border-bottom: 6px solid #fff;
        width: 20px;
        height: 10px;
    }

    &.planNodeText {
        color: #272933 !important;
        animation: none !important;
    }
}

/* 容器退出动画 */
.exitingContainer {
    animation: fade-out 0.5s forwards;
}

/* 呼吸动画 */
@keyframes node-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgb(0 200 200 / 20%);
    }

    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 8px rgb(0 200 200 / 20%);
    }

    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgb(0 200 200 / 0%);
    }
}

/* 背景色变化动画 */
@keyframes bg-pending-to-doing {
    to {
        background-color: #00c8c833; /* 进行中状态 */
        box-shadow: none;
    }
}

@keyframes bg-doing-to-completed {
    to {
        background-color: #00c8c8; /* 完成状态 */
        animation: none;
    }
}

/* 内圆点颜色变化 */
@keyframes inner-pending-to-doing {
    to {
        background-color: #00c8c8;
    }
}

/* 淡入效果 */
@keyframes fade-in {
    to {
        opacity: 1;
    }
}

/* 淡出效果 */
@keyframes fade-out {
    to {
        opacity: 0;
    }
}

/* 文字激活效果 */
@keyframes text-active {
    to {
        color: #272933;
    }
}

/* 连接线填充动画 */
@keyframes line-fill {
    to {
        width: 100%;

        /* 进度条效果 */
        background: linear-gradient(
            to right,
            transparent 50px,
            #00c8c8 50px,
            #00c8c8 calc(100% - 50px),
            transparent calc(100% - 50px)
        );
    }
}

/* 对勾动画  */
@keyframes draw-check-vertical {
    0% {
        border-left: 0 solid #fff;
        height: 0;
    }

    100% {
        border-left: 6px solid #fff;
        height: 10px; /* 竖笔长度 */
    }
}

@keyframes draw-check-horizontal {
    0% {
        border-bottom: 0 solid #fff;
        width: 0;

        /* 从竖笔底部开始 */
        margin-left: -3px; /* 起始位置 */
        margin-top: 9px; /* 起始位置 */
    }

    100% {
        border-bottom: 6px solid #fff;
        width: 20px; /* 横笔长度 */
        margin-left: 0;
        margin-top: 0;
    }
}

/* 从初始状态直接到完成状态 */
@keyframes bg-pending-to-completed {
    to {
        background-color: #00c8c8;
        box-shadow: none;
    }
}
