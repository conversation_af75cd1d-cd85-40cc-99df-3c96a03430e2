import cx from 'classnames';
import React, {type FC, useEffect, useState} from 'react';
import {View} from '@tarojs/components';
import PlanNode from './PlanNode';
import styles from './index.module.less';
import {ThinkingChainProps} from './index.type';
const nodeDelay = 1.8;

const ThinkingChain: FC<ThinkingChainProps> = ({plan, forceNoShow, autoPlay = true}) => {
    const [animationStarted, setAnimationStarted] = useState(false);
    const [exiting, setExiting] = useState(false); // 控制退出动画状态
    const animationDuration = 1.8;

    useEffect(() => {
        if (autoPlay) {
            setAnimationStarted(true);
        }
    }, [autoPlay]);

    useEffect(() => {
        if (forceNoShow) {
            // 触发退出动画
            setExiting(true);
        }
    }, [forceNoShow]);

    // 当退出动画完成时返回null
    if (exiting && forceNoShow) {
        return null;
    }

    return (
        <View className={cx(styles.markdownPlanContainer, 'wz-flex', {[styles.exiting]: exiting})}>
            <View className={cx(styles.markdownPlanHorizontal, 'wz-flex')}>
                {plan.map((node, index) => (
                    <PlanNode
                        key={node.id}
                        text={node.name}
                        delay={nodeDelay * index}
                        isLast={index === plan.length - 1}
                        animationStarted={animationStarted}
                        animationDuration={animationDuration}
                        showLine={index < plan.length - 1}
                        forceComplete={exiting}
                    />
                ))}
            </View>
        </View>
    );
};

ThinkingChain.displayName = 'ThinkingChain';
export default ThinkingChain;
