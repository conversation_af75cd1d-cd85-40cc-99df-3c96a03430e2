import {memo} from 'react';
import {View} from '@tarojs/components';
import cx from 'classnames';

import {ToolBarProps} from './index.d';
import FeedbackBtn from './components/FeedbackBtn';

const Toolbar = memo((props: ToolBarProps) => {
    const {msgData} = props;
    const {msgId = '', data} = msgData || {};
    const {feature, content} = data || {};
    const {ext} = content?.data || {};
    return (
        <View className={cx('aiToolsWrapper', 'wz-flex wz-rol-center wz-mb-36')}>
            {/* 点赞，点踩 */}
            <FeedbackBtn feature={feature} msgId={msgId} ext={ext} />
        </View>
    );
});

Toolbar.displayName = 'Toolbar';
export default Toolbar;
