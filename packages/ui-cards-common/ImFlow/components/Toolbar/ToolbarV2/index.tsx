import {memo, useMemo, useCallback, useEffect, useState} from 'react';
import {View} from '@tarojs/components';
import {eventCenter} from '@tarojs/taro';

import {ToolBarProps} from '../index.d';
import CopyBtn from '../components/CopyBtn';
import VoiceBroadcastBtn from '../components/VoiceBroadcastBtn';
import FeedbackBtnV2 from '../components/FeedbackBtn/FeedbackBtnV2';

const ToolbarV2 = memo((props: ToolBarProps) => {
    const {msgData} = props;
    const {msgId = '', data} = msgData || {};
    const {feature, content} = data || {};
    const {ext} = content?.data || {};
    const [tabIndex, setTabIndex] = useState(0);

    const convertData = useMemo(() => {
        // todo暂取最后一段content(待确认)
        const originList = data?.content?.data?.content?.list || [];
        const targetContent = originList[originList.length - 1] || {};
        const {content, tabList} = targetContent;
        if (content) {
            return content;
        }
        let tabContent = '';
        if (tabList) {
            const {header, body} = tabList;
            if (header?.length) {
                tabContent += header.map(item => `${item.title}：${item.content}\n`).join('');
            }
            if (body?.length) {
                const {title = '', list = []} = body[tabIndex];
                tabContent += `\n${title}\n`;
                tabContent += list.map(item => `\n${item.title}\n${item.content}`).join('');
            }
            return tabContent || '';
        }
    }, [data?.content?.data?.content?.list, tabIndex]);

    const changeTab = useCallback(async (index: number) => {
        setTabIndex(index);
        eventCenter.trigger('stopTTS');
    }, []);

    useEffect(() => {
        eventCenter.on('changeMsgTab', changeTab);
        return () => {
            eventCenter.off('changeMsgTab', changeTab);
        };
    }, [changeTab]);

    return (
        <View className={'wz-flex wz-pb-45 wz-row-between'}>
            <View className={'wz-flex'}>
                {feature?.copyInfo ? (
                    <CopyBtn copyInfo={feature?.copyInfo} copyContent={convertData} />
                ) : null}
                {feature?.dislike ? (
                    <FeedbackBtnV2 feature={feature} msgId={msgId} ext={ext} />
                ) : null}
            </View>
            {/* 本期只做小程序版本的语音播报 */}
            {feature?.tts && process.env.TARO_ENV !== 'h5' ? (
                <VoiceBroadcastBtn voiceInfo={feature?.tts} voiceContent={convertData} />
            ) : null}
        </View>
    );
});

ToolbarV2.displayName = 'ToolbarV2';
export default ToolbarV2;
