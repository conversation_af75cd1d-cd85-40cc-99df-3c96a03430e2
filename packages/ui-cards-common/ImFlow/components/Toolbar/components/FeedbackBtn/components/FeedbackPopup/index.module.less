/* stylelint-disable */
.feebackMain {
    .scrollView {
        max-height: calc(80vh - 560px);
    }

    &CheckBox {
        &Item {
            width: 552px;
            height: 114px;
            border-radius: 120px;
            box-sizing: border-box;
            background: #f5f6fa;
            color: #000;
            font-family: PingFang SC;
        }

        .selected {
            background: #e6fafa;
            color: #00c8c8;
        }
    }

    .diseaseFillTextarea {
        .textareaBox {
            position: relative;
            height: 318px;
            background: #f5f6fa;
            padding: 45px 27px 20px 45px;
            overflow: scroll;

            .textareaCon {
                height: 238px;
                line-height: 72px;
                box-sizing: border-box;
            }

            .textareaPlaceholder,
            .textareaCon::placeholder {
                color: #b8b8b8;
                line-height: 72px;
            }

            .textareaCount {
                position: absolute;
                bottom: 36px;
                right: 36px;
                color: #999;
            }
        }
    }
}

.footerBtn {
    outline: none;
    border: none;
    height: 132px;
    background: #00c8c8;
    color: #fff;
    box-sizing: border-box;
}
// 重置弹窗样式
.popup {
    border-radius: 63px 63px 0 0 !important;
}
