import cx from 'classnames';
import {View, Image} from '@tarojs/components';
import {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {hooks, utils} from '@baidu/vita-pages-im';
import {vibrateShort} from '@tarojs/taro';

import Portal from '../../../../../../Portal';
import {useGetState} from '../../../../../hooks';
import type {FeedbackPopupDataProps} from '../../../../../index.d';

import FeedbackPopup from '../components/FeedbackPopup';
import {disLikeIcon, disLikeSolid} from '../../../../../constants';
import {
    likestatusEnum,
    type Likestatus,
    type FeedbackPopupData
} from '../components/FeedbackPopup/index.type';
import type {FeedbackBtnProps} from '../index.d';
import styles from './index.module.less';

const LikestatusMap = {
    0: 'unselect',
    2: 'disLike'
};

const FeedbackBtnV2 = memo((props: FeedbackBtnProps) => {
    const {useGetSessionId, useGetCardEventCallback} = hooks;
    const {showToast, ubcCommonClkSend, ubcCommonViewSend} = utils;
    const {msgId = '', feature, ext = {}} = props;
    const {onRequest} = useGetCardEventCallback();
    const sessionId = useGetSessionId();

    const [showPopup, setShowPopup] = useState(false);
    const [likestatus, setLikeStatus, getLikeStatus] = useGetState<Likestatus>('unselect');
    const [popupData, setPopupData] = useState<FeedbackPopupDataProps>();

    // 点踩数据上报
    const reportFeedbackData = useCallback(
        (data: FeedbackPopupData) => {
            const {interactionInfo = {}, submitData = {}} = data || {};
            interactionInfo.params = {
                ...interactionInfo.params,
                bizActionType: 'attitude',
                chatData: {
                    sessionId
                },
                bizActionData: {
                    voteMsgInfo: {
                        msgId,
                        attitude: likestatusEnum[getLikeStatus()],
                        ...submitData
                    }
                }
            };

            onRequest({
                info: interactionInfo
            }).catch(error => {
                const msg = error?.[0]?.msg || '网络异常~';

                showToast({
                    title: msg,
                    icon: 'none',
                    duration: 2000
                });
            });

            ubcCommonClkSend({
                value: 'agentReply_dislike',
                ext: {
                    product_info: {
                        submitData: submitData || {},
                        msgId,
                        ...ext
                    }
                }
            });
        },
        [ext, getLikeStatus, msgId, onRequest, sessionId, showToast, ubcCommonClkSend]
    );

    const handlePopupToast = useCallback(
        (key, eventType) => {
            const actionInfo = props?.feature?.[key]?.actionInfo;
            // 加抖动效果
            vibrateShort({type: 'light'});

            if (eventType === 'toast') {
                showToast({
                    title: '您的反馈已取消',
                    icon: 'none',
                    duration: 2000
                });
            }

            if (eventType === 'popup' && actionInfo?.interaction === 'popup') {
                setShowPopup(true);
                setPopupData(actionInfo?.interactionInfo?.popupInfo);
            }

            reportFeedbackData({
                type: key,
                interactionInfo: actionInfo?.interactionInfo
            });
        },
        [props?.feature, reportFeedbackData, showToast]
    );

    // 点击不喜欢按钮状态变更
    const handleClickLike = useCallback(
        _btnType => {
            switch (_btnType) {
                case 'WiseDislike':
                    setLikeStatus('disLike');
                    handlePopupToast('dislike', 'popup');
                    break;

                case 'WiseDislikeSolid':
                    setLikeStatus('unselect');
                    handlePopupToast('dislike', 'toast');
                    break;

                default:
                    break;
            }
        },

        [handlePopupToast, setLikeStatus]
    );

    // 根据状态渲染喜欢不喜欢按钮组件
    const memoLiseAndDislike = useMemo(() => {
        // 渲染不喜欢按钮组件
        const WiseDislikeIcon = <Image className={styles.dislikeIcon} src={disLikeIcon} />;

        // 渲染不喜欢按钮组件（选中状态）
        const WiseDislikeSolidIcon = <Image className={styles.dislikeIcon} src={disLikeSolid} />;

        const likeMap = {
            unselect: <>{WiseDislikeIcon}</>,
            disLike: <>{WiseDislikeSolidIcon}</>
        };

        return (
            <View
                className={cx('wz-flex wz-flex-1 wz-row-center', {
                    [styles.unlikeAnimate]: likestatus === 'disLike'
                })}
            >
                {likeMap[likestatus]}
            </View>
        );
    }, [likestatus]);

    useEffect(() => {
        const likestatus = LikestatusMap[feature?.attributeStatus || ''];
        if (likestatus) {
            setLikeStatus(likestatus);
        }

        ubcCommonViewSend({
            value: 'agentReplyBtn',
            ext: {
                product_info: {
                    msgId,
                    ...ext
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <View
            className={cx(styles.toolIcon, 'wz-flex wz-rol-center wz-row-center')}
            onClick={() => {
                handleClickLike(likestatus === 'unselect' ? 'WiseDislike' : 'WiseDislikeSolid');
            }}
        >
            {memoLiseAndDislike}
            <Portal>
                <FeedbackPopup
                    reportFeedbackData={reportFeedbackData}
                    showPopup={showPopup}
                    popupData={popupData}
                    likestatus={likestatus}
                    onClosePopup={() => setShowPopup(false)}
                />
            </Portal>
        </View>
    );
});

FeedbackBtnV2.displayName = 'FeedbackBtnV2';

export default FeedbackBtnV2;
