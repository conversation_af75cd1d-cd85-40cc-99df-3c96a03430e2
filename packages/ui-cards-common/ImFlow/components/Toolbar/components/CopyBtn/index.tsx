import cx from 'classnames';
import {View, Image} from '@tarojs/components';
import {memo, useCallback} from 'react';
import {utils} from '@baidu/vita-pages-im';
import {copyTextFn} from '../../../../../../pages-im/src/utils/basicAbility/copy';
import {imCopy} from '../../../../constants';
import styles from './index.module.less';
import {CopyBtnProps} from './index.d';

const CopyBtn = memo((props: CopyBtnProps) => {
    const {copyInfo, copyContent = ''} = props;
    const {enable = true} = copyInfo;
    const {ubcCommonClkSend} = utils;

    // 点击复制
    const handleClickCopy = useCallback(() => {
        ubcCommonClkSend({
            value: 'agentReply_copy'
        });
        if (enable) {
            copyTextFn(copyContent, '回答内容已复制');
        }
    }, [enable, copyContent, ubcCommonClkSend]);

    return (
        <View
            className={cx(styles.toolIcon, 'wz-flex wz-rol-center wz-row-center wz-mr-45')}
            onClick={() => {
                handleClickCopy();
            }}
        >
            <Image className={styles.copyIcon} src={imCopy} />
        </View>
    );
});

CopyBtn.displayName = 'CopyBtn';

export default CopyBtn;
