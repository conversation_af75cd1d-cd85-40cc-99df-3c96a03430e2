import cx from 'classnames';
import {View, Image} from '@tarojs/components';
import {memo, useCallback, useRef, useMemo, useState, useEffect} from 'react';
import {utils} from '@baidu/vita-pages-im';
import {getVoiceTexts} from '@baidu/vita-pages-im/src/utils/markDown';
import {eventCenter, useDidHide} from '@tarojs/taro';
import {voiceIcon, voicePlayingIcon} from '../../../../constants';
import TTSManager from './TTSManager';
import styles from './index.module.less';
import {VoiceBtnProps} from './index.d';

const VoiceBroadcastBtn = memo((props: VoiceBtnProps) => {
    const {voiceInfo, voiceContent = ''} = props;
    const {speaker = ''} = voiceInfo;
    const [playing, setPlaying] = useState(false);
    const tts = useRef(TTSManager.getInstance());
    const {ubcCommonClkSend} = utils;

    const ttsInfo = useMemo(() => {
        const voiceInfo = getVoiceTexts(voiceContent);
        return voiceInfo;
    }, [voiceContent]);

    // 点击播报
    const handleClickVoice = useCallback(async () => {
        ubcCommonClkSend({
            value: 'agentReply_play_voice'
        });
        if (playing) {
            setPlaying(false);
            await tts.current.stop();
        } else {
            setPlaying(true);
            await tts.current.play({
                content: ttsInfo,
                speaker,
                onStart: () => {
                    setPlaying(true);
                },
                onEnd: () => {
                    setPlaying(false);
                },
                onError: error => {
                    setPlaying(false);
                    console.error('播放出错:', error);
                }
            });
        }
    }, [ttsInfo, playing, speaker, ubcCommonClkSend]);

    const stopTTS = useCallback(() => {
        tts.current.stop();
        setPlaying(false);
    }, []);

    useEffect(() => {
        eventCenter.on('stopTTS', stopTTS);
        return () => {
            eventCenter.off('stopTTS', stopTTS);
            tts.current.destroy();
        };
    }, [stopTTS]);

    useDidHide(() => {
        stopTTS();
    });

    return (
        <View className={cx(styles.toolIcon, 'wz-flex wz-row-center')} onClick={handleClickVoice}>
            {playing ? (
                <Image className={styles.voiceIcon} src={voicePlayingIcon} />
            ) : (
                <Image className={styles.voiceIcon} src={voiceIcon} />
            )}
        </View>
    );
});

VoiceBroadcastBtn.displayName = 'VoiceBroadcastBtn';

export default VoiceBroadcastBtn;
