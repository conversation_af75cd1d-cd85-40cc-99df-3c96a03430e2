declare const swan: {
    getStreamTTSManager: () => {
        playStream: (options: {
            title: string;
            context: Record<string, any>;
            content: string;
        }) => Promise<void>;
        stop: () => void;
        onPlay?: () => void;
        onEnded?: () => void;
        onError?: (error: any) => void;
    };
};

interface TTSText {
    data: string;
    type: string;
    index: number;
}

interface TTSOptions {
    content: TTSText[];
    speaker?: string;
    onStart?: () => void;
    onEnd?: () => void;
    onError?: (error: any) => void;
}

class TTSManager {
    private static instance: TTSManager | null = null;
    private isPlaying: boolean = false;
    private audio: any = null;
    private currentOptions: TTSOptions | null = null;

    private constructor() {
        // TODO 暂时使用 swan.getStreamTTSManager() 初始化音频实例 h5本期先不做
        this.audio = process.env.TARO_ENV === 'swan' && swan.getStreamTTSManager();
        this.attachEventListeners();
    }

    private attachEventListeners() {
        if (this.audio) {
            this.audio.onPlay &&
                this.audio.onPlay(() => {
                    this.isPlaying = true;
                    this.currentOptions?.onStart?.();
                });

            this.audio.onEnded &&
                this.audio.onEnded(() => {
                    this.isPlaying = false;
                    this.currentOptions?.onEnd?.();
                });

            this.audio.onError &&
                this.audio.onError((error: any) => {
                    this.isPlaying = false;
                    this.currentOptions?.onError?.(error);
                });
        }
    }

    public static getInstance(): TTSManager {
        if (!TTSManager.instance) {
            TTSManager.instance = new TTSManager();
        }
        return TTSManager.instance;
    }

    /**
     * 播放文本
     * @param options TTS配置选项
     */
    public async play(options: TTSOptions): Promise<void> {
        try {
            if (this.isPlaying) {
                this.stop();
                return;
            }

            this.isPlaying = true;
            this.currentOptions = options;
            options.onStart?.();

            await this.audio.playStream({
                title: ' ',
                context: {
                    paragraphCount: options.content.length,
                    disableFloatView: true,
                    engine: {
                        speaker: options.speaker
                    }
                },
                content: options.content
            });
        } catch (error) {
            this.isPlaying = false;
            options.onError?.(error);
            this.currentOptions = null;
        }
    }

    /**
     * 停止播放
     */
    public stop(): void {
        if (this.audio) {
            this.audio.stop();
        }
        this.isPlaying = false;
    }

    /**
     * 销毁实例，在组件卸载时调用
     */
    public destroy(): void {
        this.stop();
        this.audio = null;
        TTSManager.instance = null;
    }
}

export default TTSManager;
