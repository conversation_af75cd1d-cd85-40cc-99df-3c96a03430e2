import {useMemo} from 'react';
import {View} from '@tarojs/components';
import cx from 'classnames';
import {useGetLastMsgId} from '../../../../pages-im/src/hooks/triageStream/pageDataController';
// import Toolbar from '../Toolbar';
import ToolbarV2 from '../Toolbar/ToolbarV2';
import styles from './index.module.less';
import {MarkDownCardProps} from './index.d';

const MarkDownCard = (props: MarkDownCardProps) => {
    const {
        msgData,
        type = 'doctor',
        children,
        className,
        header = null,
        showToolbar,
        isInterrupted = false,
        isQuickSkuReply = false
    } = props || {};

    const {lastMsgId} = useGetLastMsgId();
    const isLatest = lastMsgId === msgData?.msgId;

    // 判断卡片样式
    const memoRenderCardBorder = useMemo(() => {
        return type === 'doctor' ? styles.doctorCardBorder : styles.patientCardBorder;
    }, [type]);

    // 判断是否显示底部工具栏
    // const memoRenderToolbar = useMemo(() => {
    //     const {like, dislike} = msgData?.data?.feature || {};

    //     return showToolbar && like && dislike && !isInterrupted;
    // }, [showToolbar, msgData?.data?.feature, isInterrupted]);

    // 判断是否显示底部工具栏V2
    const memoRenderToolbar = useMemo(() => {
        return (
            showToolbar &&
            !isInterrupted &&
            msgData?.data?.feature &&
            Object.keys(msgData?.data?.feature).length &&
            isLatest
        );
    }, [showToolbar, isInterrupted, msgData?.data?.feature, isLatest]);

    return (
        <View className={cx(styles.aiCardStyle, 'wz-mt-51', memoRenderCardBorder, className)}>
            {/* 标题 */}
            {header}
            <View className='wz-plr-45'>
                {/* markdown 内容 */}
                {children}

                {/* 打断 */}
                {isInterrupted && !isQuickSkuReply && (
                    <View className={cx(styles.interrupted, 'wz-fw-400 wz-fs-48 wz-mb-36')}>
                        回答已终止
                    </View>
                )}

                {/* 工具栏 */}
                {memoRenderToolbar ? <ToolbarV2 msgData={msgData} /> : null}
            </View>
        </View>
    );
};

export default MarkDownCard;
