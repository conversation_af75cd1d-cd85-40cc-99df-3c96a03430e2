//初始化按钮组件
import {memo, type FC, useEffect, useState, useCallback} from 'react';
import cx from 'classnames';
import {View} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import {pxTransform, nextTick} from '@tarojs/taro';
import {utils} from '@baidu/vita-pages-im';

import VideoModal from '../VideoModal';
import Portal from '../../../../Portal';
import {SimpleVideoProps} from './index.d';
import styles from './index.module.less';

const SimpleVideo: FC<SimpleVideoProps> = ({video = {}, msgId = '', ext = {}}) => {
    const {thumb = '', width = 0, height = 0} = video;
    const isVertical = width / height <= 0.75;

    // 视频封面
    const [videoThumb, setVideoThumb] = useState('');
    const [showModal, setShowModal] = useState(false);

    // 显示播放按钮
    const showPlay = true;

    useEffect(() => {
        nextTick(() => {
            utils?.ubcCommonViewSend({
                value: 'ImFlowVideo',
                ext: {
                    product_info: {
                        msgId,
                        video,
                        ...ext
                    }
                }
            });
        });
    }, [msgId]);

    useEffect(() => {
        if (videoThumb?.split('?')?.[0] !== thumb?.split('?')?.[0]) {
            // 防止重复渲染导致封面闪烁
            setVideoThumb(thumb);
        }
    }, [thumb, videoThumb]);

    const handleClick = useCallback(() => {
        setShowModal(true);
        utils?.ubcCommonClkSend({
            value: 'ImFlowVideo',
            ext: {
                product_info: {
                    msgId,
                    video,
                    ...ext
                }
            }
        });
    }, [ext, msgId, video]);

    return video ? (
        <View
            className={cx(
                styles.markDownVideo,
                isVertical ? styles.markDownVideoVertical : styles.markDownVideoHorizontal,
                'wz-mb-45'
            )}
            style={{
                borderRadius: pxTransform(45),
                overflow: 'hidden'
            }}
            onClick={handleClick}
        >
            <WImage
                src={thumb}
                mode='aspectFill'
                className={
                    isVertical ? styles.markDownVideoVertical : styles.markDownVideoHorizontal
                }
                fallback={
                    <View className={styles.markDownVideoImgCon}>
                        <View className={styles.markDownVideoImgDefault} />
                    </View>
                }
            />
            {showPlay && (
                <View className={styles.markDownVideoPlayCon}>
                    <WImage
                        src='https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vitaVideoPlayIcon.png'
                        className={styles.markDownVideoPlayConImg}
                    />
                </View>
            )}
            <Portal>
                {showModal ? (
                    <VideoModal
                        video={video}
                        msgId={msgId}
                        ext={ext}
                        onClosePopup={() => setShowModal(false)}
                    />
                ) : null}
            </Portal>
        </View>
    ) : null;
};
SimpleVideo.displayName = 'SimpleVideo';
export default memo(SimpleVideo);
