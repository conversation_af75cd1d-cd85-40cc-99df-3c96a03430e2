import {Video, View} from '@tarojs/components';
import cx from 'classnames';
import {memo, type FC, useCallback, useRef} from 'react';
import {pxTransform, useUnload, showToast} from '@tarojs/taro';
import {WiseClose} from '@baidu/wz-taro-tools-icons';
import {getSystemInfo} from '@baidu/vita-pages-im/src/utils/taro';
import {utils} from '@baidu/vita-pages-im';

import styles from './index.module.less';

interface IProps {
    onClosePopup: () => void;
    video: {
        origin?: string;
        thumb?: string;
        width?: number;
        height?: number;
    };
    msgId?: string;
    ext?: {
        [key: string]: string | number;
    };
}

const VideoModal: FC<IProps> = ({onClosePopup, video = {}, msgId = '', ext = {}}) => {
    const {origin = ''} = video;
    const systemInfo = getSystemInfo();
    const {barHeight = 0} = systemInfo;
    const defaultTime = Date.now();
    const totalTime = useRef<number>(0);
    const playTime = useRef<number>(defaultTime);
    const isPlay = useRef<boolean>(false);

    const close = useCallback(() => {
        onClosePopup && onClosePopup();
        let time = totalTime.current;
        const now = Date.now();
        if (isPlay.current) {
            time = totalTime.current + (now - playTime.current);
            isPlay.current = false;
        }
        playTime.current = now;
        totalTime.current = 0;
        utils.ubcCommonTimingSend({
            value: 'ImFlowVideo',
            duration: time / 1000,
            ext: {
                product_info: {
                    msgId,
                    video,
                    ...ext
                }
            }
        });
    }, [ext, msgId, onClosePopup, video]);

    useUnload(() => {
        close();
    });

    const handleVideoError = useCallback(() => {
        showToast({
            title: '该视频链接已失效',
            icon: 'none'
        });
        utils.ubcCommonViewSend({
            value: 'ImFlowVideoError',
            ext: {
                product_info: {
                    msgId,
                    video,
                    ...ext
                }
            }
        });
    }, [ext, msgId, video]);

    const handleOnEnded = useCallback(() => {
        utils.ubcCommonViewSend({
            value: 'ImFlowVideoEnded',
            ext: {
                product_info: {
                    msgId,
                    video,
                    ...ext
                }
            }
        });
    }, [ext, msgId, video]);

    const handleOnPlay = () => {
        isPlay.current = true;
        playTime.current = Date.now();
    };

    const handleOnPause = () => {
        totalTime.current = totalTime.current + (Date.now() - playTime.current);
        isPlay.current = false;
    };

    return (
        <View className={cx(styles.video, process.env.TARO_ENV === 'h5' ? styles.videoH5 : '')}>
            <Video
                id='im-video'
                src={origin || ''}
                autoplay
                loop={false}
                showFullscreenBtn={true}
                showProgress={true}
                className={styles.videoCon}
                onPlay={handleOnPlay}
                onPause={handleOnPause}
                onError={handleVideoError}
                onEnded={handleOnEnded}
            />
            <WiseClose
                size={96}
                color='#fff'
                className={styles.close}
                style={{
                    position: 'absolute',
                    right: pxTransform(51),
                    // 兼容安卓h5中关闭按钮被遮挡
                    top:
                        process.env.TARO_ENV === 'h5'
                            ? pxTransform(51)
                            : pxTransform(51 + barHeight * 3),
                    zIndex: 100
                }}
                onClick={close}
            />
        </View>
    );
};

VideoModal.displayName = 'VideoModal';
export default memo(VideoModal);
