//视频组件
import {memo, type FC} from 'react';
import SimpleVideo from './SimpleVideo';

import {MarkDownVideosProps} from './index.d';

const MarkDownVideos: FC<MarkDownVideosProps> = ({videos = [], msgId, ext, isFinish}) => {
    return videos && isFinish
        ? videos.map((video, index) => {
            return <SimpleVideo key={index} video={video} msgId={msgId} ext={ext} />;
        })
        : null;
};
MarkDownVideos.displayName = 'MarkDownVideos';
export default memo(MarkDownVideos);
