/* stylelint-disable */
.loadAnimation() {
    @keyframes loadAnimation {
        0% {
            transform: scale(1);
            background-color: #30a34e;
            opacity: 0.4;
        }

        50% {
            transform: scale(1.7);
            background-color: #30a34e;
            opacity: 0.15;
        }

        100% {
            transform: scale(1);
            background-color: #30a34e;
            opacity: 0.4;
        }
    }
}

.loadingIcon() {
    .loadingIcon {
        display: inline-block;
        width: 30px;
        aspect-ratio: 1;
        border-radius: 50%;
        background: rgba(#30a34e, 0.4);
        opacity: 0.4;
        animation: loadAnimation 1s linear infinite;
        animation-direction: reverse;
    }
    .loadingIcon:local {
        animation: loadAnimation 1s linear infinite;
    }
}

.markdownLoadingMixin () {
    .markdownLoading {
        display: inline-block;
        width: 50px;
        height: 50px;
        vertical-align: middle;
    }
}
