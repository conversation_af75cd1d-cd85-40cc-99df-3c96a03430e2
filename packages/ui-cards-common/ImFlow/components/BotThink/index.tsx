import React, {memo, type FC} from 'react';
import {View, Text} from '@tarojs/components';
import cx from 'classnames';
import styles from './index.module.less';
import type {BotThinkProps} from './index.d';

const BotThink: FC<BotThinkProps> = props => {
    const {text} = props;

    return (
        <View className={cx(styles.aiBotThink, 'wz-flex wz-row-left wz-col-center')}>
            <View className={cx(styles.loadingIcon, 'wz-mr-24')} />
            <Text className='wz-fs-45'>{text}</Text>
        </View>
    );
};

export default memo(BotThink);
