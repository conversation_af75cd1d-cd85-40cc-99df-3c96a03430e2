import React, {useCallback, useEffect, useMemo, useRef, useState, type FC} from 'react';
import {View, Text} from '@tarojs/components';
import cx from 'classnames';
import {eventCenter} from '@tarojs/taro';

import {
    ubcCommonClkSend,
    ubcCommonViewSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';
import {useScrollControl} from '../../../../pages-im/src/hooks/triageStream/useScrollControl';
import MarkDown from '../MarkDown';
import MarkDownImages from '../MarkDownImages';
import MarkDownVideos from '../MarkDownVideos';
import type {MarkdownTabListProps} from './index.d';
import styles from './index.module.less';

const AutoStepper: React.FC<{
    onShow?: () => void;
    children: React.ReactNode;
}> = ({onShow, children}) => {
    useEffect(() => {
        onShow?.();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    return <>{children}</>;
};

interface ComponentItem {
    type: React.FC<unknown>;
    props?: {
        [key: string]: unknown;
    };
    children?: React.ReactElement;
}

const SPEED = 15;
const STEP_WORD_NUM = 2;

const MarkdownTabList: FC<MarkdownTabListProps> = ({
    data,
    defaultActiveTab = 0,
    isTypewriter,
    onStart,
    onComplete
}) => {
    const [step, setStep] = useState(1);
    const [active, setActive] = useState<number>(defaultActiveTab || 0);
    const componentLen = useRef(0);

    const {disableScroll} = useScrollControl();

    const stepper = useCallback(() => {
        setStep(prev => prev + 1);
    }, []);

    useEffect(() => {
        if (isTypewriter) {
            onStart?.();
        }
    }, []);

    useEffect(() => {
        if (isTypewriter && step === componentLen.current + 1) {
            onComplete?.();
        }
    }, [step, isTypewriter]);

    const generateH1TitleMd = useCallback(title => `# ${title} \n\n`, []);

    const generateTopDescMd = useCallback(desc => {
        return desc?.reduce(
            (prev, cur) =>
                prev +
                `**${cur.title}：** ${cur.content.replace(/\*\*(\S+)\*\*/g, '```$1```')}\n\n`,
            ''
        );
    }, []);

    const generateH2TitleMd = useCallback(list => {
        return list?.reduce((prev, cur) => prev + `## ${cur.title} \n\n${cur.content}\n\n`, '');
    }, []);

    const renderContents = useMemo(() => {
        return (
            <View className={styles.contents}>
                {data?.body?.map((i, index: number) => (
                    <View
                        key={index}
                        className={cx(styles.content, active === index && styles.active)}
                    >
                        <MarkDown
                            content={generateH2TitleMd(i.list)}
                            msgEnd={!isTypewriter || index !== defaultActiveTab}
                            isTypewriter={index === defaultActiveTab}
                            isFinish={index !== defaultActiveTab}
                            speed={SPEED}
                            stepWordNum={STEP_WORD_NUM}
                            typewriterSuccessCallback={() => {
                                index === defaultActiveTab && stepper();
                            }}
                        />
                        {/* 为处理媒体资源的顺序输出，第一个tab下的媒体内容不显示在这里 */}
                        {index !== defaultActiveTab && (
                            <>
                                <MarkDownImages images={i?.media?.images} isFinish />
                                <MarkDownVideos videos={i?.media?.videos} isFinish />
                            </>
                        )}
                    </View>
                ))}
            </View>
        );
    }, [active, data?.body, defaultActiveTab, generateH2TitleMd, isTypewriter, stepper]);

    const handleClickTab = useCallback(
        (index: number) => {
            disableScroll('check_mark_tab');
            // 非打印效果，或者 step已完成时，允许切换tab
            if (!isTypewriter || step === componentLen.current + 1) {
                ubcCommonClkSend({
                    value: 'ImFlowTabList',
                    ext: {
                        product_info: {
                            tab_index: index,
                            tab_name: data.body?.[index]?.title,
                            tabs: data.body?.map(item => item.title)
                        }
                    }
                });
                setActive(index);
                eventCenter.trigger('changeMsgTab', index);
            }
        },
        [data.body, isTypewriter, step]
    );

    useEffect(() => {
        if (data.body?.length > 1) {
            ubcCommonViewSend({
                value: 'ImFlowTabList',
                ext: {
                    product_info: {
                        tabs: data.body?.map(item => item.title)
                    }
                }
            });
        }
    }, [data.body]);

    const renderTabs = useMemo(() => {
        return (
            <View className={styles.tabs}>
                {data.body?.map((item, index: number) => (
                    <View
                        key={index}
                        className={cx(styles.tab, active === index && styles.active)}
                        onClick={() => handleClickTab(index)}
                    >
                        <Text className={styles.tabText}>{item.title}</Text>
                    </View>
                ))}
            </View>
        );
    }, [active, data.body, handleClickTab]);

    const generateComponents = useMemo(() => {
        const {header, body = []} = data;
        // 是否隐藏tab
        const tabHidden = body?.length <= 1;
        // 顶部描述
        const headerMdString = generateTopDescMd(header);
        const index = tabHidden ? 0 : defaultActiveTab;
        const bodyItem = body?.[index];
        const {title, list, media} = bodyItem || {};
        // 单tab标题
        const bodyTitleString = tabHidden && title ? generateH1TitleMd(title) : '';
        // 单tab内容
        const bodyMdString = list?.length ? generateH2TitleMd(list) : '';
        // 拼接内容
        const content = headerMdString + bodyTitleString + bodyMdString;
        const components: ComponentItem[] = [
            {
                type: MarkDown,
                props: {
                    content: tabHidden ? content : headerMdString,
                    isTypewriter: isTypewriter,
                    msgEnd: !isTypewriter,
                    isFinish: step !== 1,
                    speed: SPEED,
                    stepWordNum: STEP_WORD_NUM,
                    typewriterSuccessCallback: () => {
                        step === 1 && stepper();
                    }
                }
            }
        ];
        const mediaComponents: ComponentItem[] = [
            {
                type: AutoStepper,
                props: {
                    children:
                        tabHidden || active === defaultActiveTab ? (
                            <MarkDownImages images={media?.images} isFinish />
                        ) : null,
                    onShow: stepper
                }
            },
            {
                type: AutoStepper,
                props: {
                    children:
                        tabHidden || active === defaultActiveTab ? (
                            <MarkDownVideos videos={media?.videos} isFinish />
                        ) : null,
                    onShow: stepper
                }
            }
        ];
        if (tabHidden) {
            components.push(...mediaComponents);
        } else {
            components.push(
                {
                    type: AutoStepper,
                    props: {
                        children: renderTabs,
                        onShow: stepper
                    }
                },
                {
                    type: AutoStepper,
                    props: {
                        children: renderContents
                    }
                },
                ...mediaComponents
            );
        }
        componentLen.current = components.length;
        return components;
    }, [
        active,
        data,
        defaultActiveTab,
        generateH1TitleMd,
        generateH2TitleMd,
        generateTopDescMd,
        isTypewriter,
        renderContents,
        renderTabs,
        step,
        stepper
    ]);

    const stepController = useCallback(
        (components: ComponentItem[], skip = false) => {
            return components?.slice(0, skip ? components.length : step)?.map((cfg, idx) => {
                const {type: Comp, props} = cfg;
                return <Comp key={idx} {...props} />;
            });
        },
        [step]
    );

    const renderMain = useMemo(() => {
        if (!data || Object.values(data).length === 0) return null;
        const components = generateComponents;
        return stepController(components, !isTypewriter);
    }, [data, generateComponents, stepController, isTypewriter]);

    return <View className={styles.tabListContainer}>{renderMain}</View>;
};

MarkdownTabList.displayName = 'MarkdownTabList';
export default MarkdownTabList;
