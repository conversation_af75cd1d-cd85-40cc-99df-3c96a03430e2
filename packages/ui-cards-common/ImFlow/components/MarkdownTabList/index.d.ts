export interface ImageItem {
    icon: string;
    small: string;
    origin: string;
    hasMask?: boolean;
    maskInfo?: {
        text: string;
    };
}

export interface VideoItem {
    origin: string;
    thumb: string;
    width: number;
    height: number;
}

export interface Media {
    images: ImageItem[];
    videos: VideoItem[];
}
export interface TabBaseItem {
    title: string;
    content: string;
}
export interface TabHeaderItem extends TabBaseItem {}

export interface TabBodyItem {
    title: string;
    list: TabBaseItem[];
    media?: Media;
}
export interface MarkdownTabListProps {
    isTypewriter?: boolean;
    data: {
        header: TabHeaderItem[];
        body: TabBodyItem[];
    };
    defaultActiveTab?: number;
    onStart?: () => void;
    onComplete?: () => void;
}
