.tabListContainer {
    font-family: PingFang SC;
    width: 100%;
    box-sizing: border-box;
    min-height: 240px;

    .tabs {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: -24px;

        .tab {
            flex: 1 1 0;
            min-width: 0;
            background-color: #f5f6fa;
            height: 117px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 30px;
            margin-right: 24px;
            padding: 0 30px;

            &Text {
                white-space: nowrap;
                overflow-x: hidden;
                text-overflow: ellipsis;
                width: 100%;
                display: block;
                text-align: center;
                color: #000311;
                font-size: 45px;
                letter-spacing: 0;
                font-family: PingFang SC;
            }

            &.active {
                background: linear-gradient(90deg, #00cfa3 0%, #00d3ea 100%);
                position: relative;

                .tabText {
                    color: #fff;
                    font-weight: 700;
                    font-family: PingFang SC;
                    letter-spacing: 0;
                    font-size: 45px;
                }

                &::after {
                    content: '';
                    position: absolute;
                    width: 30px;
                    height: 30px;
                    border-radius: 0 0 6px;
                    background: linear-gradient(45deg, #00d1c3 0%, #00d1c9 100%);
                    transform: rotate(45deg) translateX(-21px);
                    transform-origin: center;
                    left: 50%;
                    bottom: -29px;
                    z-index: 0;
                }
            }
        }
    }

    .contents {
        .content {
            display: none;

            &.active {
                display: block;
            }
        }
    }
    /* stylelint-disable selector-pseudo-class-no-unknown */
    :global {
        .markdownContainer {
            .markdown {
                padding: 33px 0 27px;

                &:first-child {
                    .inline-code {
                        font-style: normal;
                        font-weight: 500;
                        color: #00c8c8;
                        display: inline;
                        background-color: transparent;
                        padding: 0 12px;
                        font-family: PingFang SC;
                    }
                }

                .bold {
                    font-weight: 500;
                }

                .p {
                    line-height: 87px;
                    margin-bottom: 18px;
                }

                .h1 {
                    color: #000311;
                    font-size: 72px;
                    font-weight: 600;
                    line-height: 72px;
                    padding: 36px 0;
                    border-bottom: 1px solid #e0e0e0;
                    margin-bottom: 18px;
                }

                .h2 {
                    font-size: 57px;
                    font-weight: 600;
                    line-height: 57px;
                    position: relative;
                    margin-bottom: 0;
                    padding: 45px 0 45px 36px;

                    &::before {
                        content: '';
                        display: block;
                        position: absolute;
                        left: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 12px;
                        height: 57px;
                        background-color: #00c8c8;
                        border-radius: 18px;
                    }
                }
            }
        }
    }
}
