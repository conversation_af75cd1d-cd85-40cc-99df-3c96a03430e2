import React, {useMemo, useCallback} from 'react';
import {View} from '@tarojs/components';
import cx from 'classnames';
import {WiseRightArrow} from '@baidu/wz-taro-tools-icons';
import MarkDown from '../MarkDown';
import MedicalCard, {type medicalCardProps} from '../MedicalCard';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';

import {type ContentList, type ShowMoreData, type ImFlowProps} from '../../index.d';
import {type MarkDdownProps} from '../MarkDown/index.d';
import styles from './index.module.less';
interface MarkDownListProps {
    contentList: ContentList;
    className?: string;
    sectionShowMore?: ShowMoreData;
    // 全局透传属性
    globalProps?: Partial<ImFlowProps> & {
        msgEnd?: boolean;
        interrupted?: boolean;
        dataSource?: string;
        msgId?: string;
        ext?: Record<string, unknown>;
        forcedShowLoading?: boolean;
    };
}

// 扩展 MarkDdownProps 以支持索引签名
interface ExtendedMarkDdownProps extends MarkDdownProps {
    [key: string]: unknown;
}

// 组件适配器
interface ComponentAdapter<T = Record<string, unknown>> {
    component: React.ComponentType<T>;
    propsTransformer?: (
        item: ContentList['data'][string],
        globalProps?: MarkDownListProps['globalProps']
    ) => T;
    validator?: (item: ContentList['data'][string]) => boolean;
}

const componentRegistry: Record<string, ComponentAdapter<MarkDdownProps>> = {
    markdown: {
        component: MarkDown as unknown as React.ComponentType<MarkDdownProps>,
        propsTransformer: (
            item: ContentList['data'][string],
            globalProps?: MarkDownListProps['globalProps']
        ): ExtendedMarkDdownProps => ({
            isTypewriter: true,
            content: item.contentAdd || '',
            forcedShowLoading: globalProps?.forcedShowLoading,
            isFinish: globalProps?.msgEnd || false,
            msgEnd: globalProps?.msgEnd || false
        })
    },
    medicalCard: {
        component: MedicalCard as unknown as React.ComponentType<medicalCardProps>,
        propsTransformer: (
            item: ContentList['data'][string],
            globalProps?: MarkDownListProps['globalProps']
        ): medicalCardProps => ({
            content: item.contentReplace || '',
            pos: item.pos,
            isSingleCustomCard: item.isSingleCustomCard,
            ...globalProps
        })
    }
    // ... 其他组件适配器
} as const;

type ComponentType = keyof typeof componentRegistry;

const MarkDownList: React.FC<MarkDownListProps> = ({
    contentList,
    sectionShowMore = {},
    className,
    globalProps = {}
}) => {
    const {ids, data} = contentList;

    // 获取当前卡片渲染位置
    const getCurPos = useCallback(
        (id: string) => {
            const filterIdsComponent = [] as string[];

            Object.keys(data).forEach(el => {
                if (data[el].component && data[el].component !== 'markdown') {
                    filterIdsComponent.push(el);
                }
            });
            const targetIndex = filterIdsComponent.findIndex(el => el === id);

            return targetIndex >= 0 ? targetIndex + 1 : 0;
        },
        [data]
    );

    // 整卡底部跳转
    const handleJump = useCallback(data => {
        const {interaction, interactionInfo} = data;
        if (interaction === 'openLink') {
            const {url} = interactionInfo || {};
            navigate({
                url,
                openType: 'navigate'
            });
        }
    }, []);

    // 渲染单个组件项
    const renderComponent = useMemo(() => {
        return (id: string) => {
            const item = data[id];
            if (!item?.component) return null;

            const adapter = componentRegistry[item.component as ComponentType];
            if (!adapter) {
                // eslint-disable-next-line no-console
                console.warn(`Unknown component type: ${item.component}`);
                return null;
            }

            const {component: Component, propsTransformer} = adapter;

            try {
                // 组件属性转换
                const componentProps = propsTransformer
                    ? propsTransformer(
                        {
                            ...item,
                            pos: getCurPos(id)
                        },
                        globalProps
                    )
                    : {
                        content: item.contentAdd || '',
                        isFinish: item.isFinish ?? false
                    };

                return <Component key={id} {...componentProps} />;
            } catch (error) {
                const err = error as Error;
                // eslint-disable-next-line no-console
                console.error(`Error rendering component ${item.component}:`, err);
                return null;
            }
        };
    }, [data, getCurPos, globalProps]);

    // 过滤有效的组件ID
    const validIds = useMemo(
        () =>
            ids.filter(id => {
                const item = data[id];
                if (!item?.component) return false;

                const adapter = componentRegistry[item.component as ComponentType];
                if (!adapter) return false;

                return adapter?.validator ? adapter.validator(item) : true;
            }),
        [ids, data]
    );

    if (!validIds.length) return null;

    return (
        <View className={className}>
            {validIds.map(renderComponent)}
            {/* 整卡底部跳转 */}
            {sectionShowMore?.content && (
                <View
                    className={cx(styles.more, 'wz-flex wz-row-center wz-fs-42 wz-mb-45')}
                    onClick={() => handleJump(sectionShowMore)}
                >
                    {sectionShowMore.content}
                    <WiseRightArrow className='wz-fs-42 wz-ml-9' color='#848691' />
                </View>
            )}
        </View>
    );
};

export default MarkDownList;
