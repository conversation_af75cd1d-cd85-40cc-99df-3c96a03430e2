import {type FC} from 'react';
import {View, Text} from '@tarojs/components';
import {HImage} from '@baidu/health-ui';
import cx from 'classnames';

import MarkDown from '../MarkDown';
import {imFlowPlanActive, imFlowPlanComplete} from '../../constants';

import styles from './index.module.less';
import type {MarkdownPlanProps} from './index.d';

const MarkdownPlan: FC<MarkdownPlanProps> = ({plan, msgEnd}) => {
    return (
        <View className={styles.markdownPlan}>
            <View className='markdownPlanContent'>
                {plan?.map(item => {
                    const isFinish = msgEnd || item?.status === 'completed';
                    const imageSrc = isFinish ? imFlowPlanComplete : imFlowPlanActive;

                    const isLast = item.id === plan[plan?.length - 1]?.id;
                    return (
                        <View key={item.id} className={cx('markdownPlanContentItem', 'wz-fs-48')}>
                            <View
                                className={cx(
                                    styles.markdownPlanContentItemTitle,
                                    'wz-fw-700 wz-flex wz-align-center'
                                )}
                            >
                                <HImage src={imageSrc} width={48} height={48} />
                                <Text className='wz-pl-18'>{item?.name}</Text>
                            </View>
                            <View
                                className={cx(
                                    styles.markdownPlanContentItemDescription,
                                    'wz-fs-48'
                                )}
                            >
                                <MarkDown
                                    isTypewriter
                                    content={item?.description}
                                    isShowLoading={false}
                                    isFinish={isFinish}
                                    msgEnd={msgEnd || !isLast}
                                    className={cx(styles.markdown, 'wz-fs-48')}
                                />
                                {/* 添加一个div，用于显示border */}
                                <View className={styles.markdownBorder}></View>
                            </View>
                        </View>
                    );
                })}
            </View>
        </View>
    );
};

MarkdownPlan.displayName = 'MarkdownPlan';
export default MarkdownPlan;
