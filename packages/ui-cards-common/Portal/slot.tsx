import {type ReactNode, useEffect, useState} from 'react';
import {View} from '@tarojs/components';

import {usePortal} from './provider';

const PortalSlot = () => {
    const [conMap, setConMap] = useState(new Map<symbol, ReactNode>());
    const {getPortals, subscribe} = usePortal();

    useEffect(() => {
        const updateCon = () => {
            const newPortals = getPortals();
            setConMap(prevMap => {
                const updatedMap = new Map(prevMap);

                newPortals.forEach((element, key) => {
                    if (!updatedMap.has(key) || updatedMap.get(key) !== element) {
                        updatedMap.set(key, element);
                    }
                });

                return updatedMap;
            });
        };

        subscribe(updateCon);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <View style={{position: 'absolute', zIndex: 1050, width: '100vw', height: 'auto'}}>
            {Array.from(conMap.entries()).map(([key, element]) => (
                <View key={key.description}>{element}</View>
            ))}
        </View>
    );
};

export default PortalSlot;
