import {createContext, useContext, useRef, useCallback, type ReactNode} from 'react';

interface PortalContextType {
    unmount: (key: symbol) => void;
    mount: (key: symbol, element: ReactNode) => void;
    getPortals: () => Map<symbol, ReactNode>;
    subscribe: (callback: () => void) => () => void;
}

const PortalContext = createContext<PortalContextType | null>(null);

export const PortalProvider = ({children}: {children: ReactNode}) => {
    const listeners = useRef<(() => void)[]>([]);
    const portalsRef = useRef(new Map<symbol, ReactNode>());

    const mount = useCallback((key: symbol, element: ReactNode) => {
        portalsRef.current.set(key, element);
        listeners.current.forEach(listener => listener());
    }, []);

    const unmount = useCallback((key: symbol) => {
        if (portalsRef.current.has(key)) {
            portalsRef.current.delete(key);
            listeners.current.forEach(listener => listener());
        }
    }, []);

    const getPortals = useCallback(() => portalsRef.current, []);

    const subscribe = useCallback((callback: () => void) => {
        listeners.current.push(callback);

        return () => {
            listeners.current = listeners.current.filter(l => l !== callback);
        };
    }, []);

    return (
        <PortalContext.Provider value={{mount, unmount, getPortals, subscribe}}>
            {children}
        </PortalContext.Provider>
    );
};

export const usePortal = () => {
    const context = useContext(PortalContext);
    if (!context) {
        throw new Error('usePortal context 获取失败');
    }

    return context;
};
