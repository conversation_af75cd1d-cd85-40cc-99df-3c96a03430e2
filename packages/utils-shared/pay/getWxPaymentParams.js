/**
 * @file 获取微信原生支付所需参数
 * <AUTHOR>
 */

/**
 * 获取男科微信原生支付所需参数
 *
 * @param {Object} wxPayInfo 接口返回的微信支付参数
 * @param {string} successUrl 支付成功回调地址
 * @param {string} failUrl 支付失败回调地址
 * @param {string} titleMsg 跳转后title
 * @return {Object} 原生支付所需参数
 */
export const getNkWxPaymentParams = (wxPayInfo, successUrl, failUrl = '', titleMsg = '我的咨询') => {
    return {
        payParams: wxPayInfo,
        // TODO 这个地方的井号拼接 需要review
        successUrl: `${window ? window.location.origin : ''}/#${successUrl}`,
        failUrl: failUrl ? `${window ? window.location.origin : ''}/#${failUrl}` : '',
        from: 'consultation',
        titleMsg
    };
};
