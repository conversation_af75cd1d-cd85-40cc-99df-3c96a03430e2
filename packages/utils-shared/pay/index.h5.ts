import {getQueryStr, getQueryObject} from '../queryUtils';

/**
 * 调起支付
 *
 * @param {Object} userConf 用户配置
 * @param {string} userConf.url 糯米支付地址
 * @param {Function} userConf.success 支付成功回调
 * @param {Function} userConf.fail 支付失败回调
 * @param {Function} userConf.complete 支付结束回调
 */
export const pay = userConf => {
    if (!userConf.url) {
        return;
    }

    try {
        const payUrl = new URL(userConf.url);
        const urlPath = payUrl.origin + (payUrl.pathname ? payUrl.pathname : '');
        const urlParams = getQueryObject(userConf.url);

        if (urlParams.ftr) {
            delete urlParams.ftr;
        }
        window.location.href = `${urlPath}?${getQueryStr(urlParams)}`;
    } catch (error) {
        window.location.href = userConf.url;
    }

    if (typeof userConf.fail === 'function') {
        setTimeout(userConf.fail, 500);
    }

    if (typeof userConf.complete === 'function') {
        setTimeout(userConf.complete, 500);
    }
};

// 吊起h5收银台
export const hisPay = userConfig => {
    // eslint-disable-next-line
    console.info('暂不支持，请实现his对应平台支付逻辑', userConfig);
};
