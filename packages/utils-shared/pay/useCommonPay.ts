import {useCallback} from 'react';
import {showLoading, hideLoading} from '@tarojs/taro';
import {isEmpty} from '../../pages-im/src/utils';
import {API_HOST} from '../../pages-im/src/models/apis/host';
import {PayResponse} from '../../pages-im/src/typings/service';
import {InteractionInfo, ICardProps} from '../../pages-im/src/typings';
import {getUseprotocolDetail} from '../../pages-im/src/models/services/common';
import {showToast} from '../../pages-im/src/utils/customShowToast';
import {navigate} from '../../pages-im/src/utils/basicAbility/commonNavigate';
import {wxPayPreCheck} from '../../pages-im/src/utils/generalFunction/wxPayPreCheck';
import {InteractionInfo as InteractionInfoType} from 
    '../../pages-im/src/components/ImCollectedAndNoNDirectSkuTypewriter/hooks/usePreCheckOrderModal';
import {pay} from './index';

interface IPayProps {
    cardData?: ICardProps<unknown>;
    interactionInfo?: InteractionInfo;
    skuId?: string;
    loading?: boolean;
    onFreshSku?: () => void;
    onCancelPay?: () => void;
    loadingText?: string;
}

export interface PayResult {
    isExpertUnavailable?: boolean;
    isUserUnavailable?: boolean;
    actionInfo?: InteractionInfoType;
}

export const useCommonPay = (): {
    confirmPay: (props: IPayProps) => Promise<PayResult | null>;
} => {
    const confirmPay = useCallback((props: IPayProps) => {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve, reject) => {
            try {
                const {
                    interactionInfo = {},
                    skuId,
                    loading = true,
                    onFreshSku,
                    onCancelPay,
                    loadingText = '正在跳转支付'
                } = props;

                const {method, url} = interactionInfo!;
                if (!url) {
                    console.error('支付出错，不存在支付地址');

                    return;
                }
                if (loading) {
                    showLoading({title: loadingText, mask: true});
                }

                // 支付前预校验微信账号绑定关系
                await wxPayPreCheck();

                const params = {
                    url: API_HOST + url,
                    serviceCallbackUrl: API_HOST + url,
                    method,
                    data: Object.assign({}, interactionInfo?.params, skuId ? {skuId} : {}),
                    header: {'Content-Type': 'application/x-www-form-urlencoded'}
                };
                const [err, data] = await getUseprotocolDetail(params);
                hideLoading();
                if (!err) {
                    if (data?.toast) {
                        showToast({
                            title: data?.toast || '',
                            icon: 'none',
                            duration: 3000
                        });
                    }
                    const {
                        payUrl,
                        jumpMsUrl,
                        wxPayInfo,
                        successJumpUrl,
                        sceneType,
                        cancelJumpUrl,
                        isExpertUnavailable,
                        isUserUnavailable,
                        actionInfo
                    } = data?.data as PayResponse;

                    // actionInfo字段存在的时候执行预检逻辑。预检下单逻辑需要。!payUrl兜底在能支付的情况的不要被预检逻辑拦截
                    if (actionInfo && !payUrl) {
                        resolve({actionInfo});
                        return;
                    }

                    // 义诊名额判断
                    if (isExpertUnavailable || isUserUnavailable) {
                        resolve({isExpertUnavailable, isUserUnavailable});

                        return;
                    }

                    // 支付跳转
                    const inlinePay = interactionInfo?.params?.inlinePay;
                    const inlinePaySign =
                        (inlinePay && inlinePay.detail && inlinePay?.detail.inlinePaySign) || '';
                    const hasPayParam =
                        process.env.TARO_ENV === 'weapp' ? !isEmpty(wxPayInfo) : payUrl;
                    if (sceneType && sceneType === 'makeOrderInplace' && hasPayParam) {
                        // 直接调起支付
                        pay({
                            url: payUrl,
                            inlinePaySign,
                            wxPayInfo,
                            success: () => {
                                // 支付成功跳转
                                successJumpUrl &&
                                    navigate({
                                        url: successJumpUrl,
                                        openType: 'navigate'
                                    });
                            },
                            fail: error => {
                                // 支付失败
                                if (cancelJumpUrl) {
                                    // 支付取消
                                    if (error.errCode === 2) {
                                        onCancelPay && onCancelPay();
                                    }
                                    navigate({
                                        url: cancelJumpUrl,
                                        openType: 'navigate'
                                    });
                                } else {
                                    onFreshSku && onFreshSku();
                                }
                            }
                        });
                    } else {
                        // 跳转到订单详情页调起支付
                        jumpMsUrl &&
                            navigate({
                                url: jumpMsUrl,
                                openType: 'navigate'
                            });
                    }
                    resolve(null);
                }
            } catch (err) {
                const errToast = err?.[0]?.toast;
                hideLoading();

                errToast &&
                    showToast({
                        title: errToast,
                        icon: 'none',
                        duration: 3000
                    });
                console.error('支付 出错：', err);
                // weirwoodErrorReport({ error: new Error(err) });
                reject(err);
            } finally {
                hideLoading();
            }
        });
    }, []);

    return {
        confirmPay
    };
};
