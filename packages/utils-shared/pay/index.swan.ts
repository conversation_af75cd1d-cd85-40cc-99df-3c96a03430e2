import Taro from '@tarojs/taro';
import {showToast} from '../../pages-im/src/utils/customShowToast';

import {getSystemInfo} from '../../pages-im/src/utils/taro';
import {getCurrentPage} from '../taroUtils';
import {extend} from '../../pages-im/src/utils';
import httpRequest from '../../pages-im/src/utils/basicAbility/comonRequest/common';
import {navigate, OpenType} from '../../pages-im/src/utils/basicAbility/commonNavigate';

import {getQuery} from './common';
import {type swan} from './index.d';

// 获取cuid
const getCuid = () => {
    return new Promise(resolve => {
        swan.getCommonSysInfo({
            success: info => {
                resolve({
                    cuid: info.cuid || ''
                });
            },
            fail: () => {
                resolve({
                    cuid: ''
                });
            }
        });
    });
};

let payState = 'init';
const invokeSwanPay = userConf => {
    if (payState !== 'init') {
        return;
    }
    payState = 'paying';
    // 5s手动重置
    setTimeout(() => {
        payState = 'init';
    }, 5000);
    const conf = extend(
        {
            data: {},
            success: null,
            fail: null,
            complete: null
        },
        {
            data: JSON.parse(userConf.data.tpData),
            success: userConf.success,
            fail: userConf.fail,
            complete: userConf.complete
        }
    );
    // 触发支付
    const bizInfo = conf.data;
    const orderInfo = {
        dealId: bizInfo.dealId,
        appKey: bizInfo.appKey,
        totalAmount: bizInfo.totalAmount,
        tpOrderId: bizInfo.tpOrderId,
        dealTitle: bizInfo.dealTitle,
        rsaSign: bizInfo.rsaSign,
        bizInfo: {
            dealId: bizInfo.dealId,
            appKey: bizInfo.appKey,
            totalAmount: bizInfo.totalAmount,
            tpOrderId: bizInfo.tpOrderId,
            rsaSign: bizInfo.rsaSign,
            returnData: bizInfo.returnData,
            displayData: bizInfo.displayData
        },
        isSplit: bizInfo.isSplit,
        goodsInfo: bizInfo.goodsInfo,
        splitMoney: bizInfo.splitMoney,
        returnData: JSON.stringify(bizInfo.returnData),
        extInfos: userConf.extInfos // tips: 保留此处，请勿删除，手白收银台实验通路的参数
    };
    if (userConf.inlinePaySign) {
        orderInfo.inlinePaySign = userConf.inlinePaySign;
    }

    swan.requestPolymerPayment({
        orderInfo,
        success() {
            showToast({
                title: '支付成功',
                icon: 'success'
            });
            // 支付成功页面跳转(优先级从高到低)：
            // 1、conf.success
            // 2、conf.success.url;
            // 3、支付地址中payResultUrl+returnData
            // 4、后退
            if (typeof conf.success === 'function') {
                Taro.hideToast();
                conf.success();
            } else if (userConf.data.srt) {
                const success = JSON.parse(userConf.data.srt);
                navigate({
                    url: success.url,
                    openType: success.openType
                });
            } else if (bizInfo.payResultUrl) {
                httpRequest({
                    url: bizInfo.payResultUrl,
                    data: extend({}, bizInfo.returnData)
                })
                    .then(res => {
                        const [, resp] = res;
                        const data = resp?.data || {};
                        const {externalUrl, urlType} = data;
                        if (externalUrl && urlType) {
                            navigate({
                                url: externalUrl,
                                openType: 'navigate'
                                // urlType
                            });
                        } else {
                            // 处理登录成功跳转方式
                            let openType = '';
                            if (getCurrentPage().isOnlyPage) {
                                openType = 'relaunch';
                            } else {
                                openType = 'redirect';
                            }

                            navigate({
                                openType: openType as OpenType,
                                url: data.payResultUrl
                            });
                        }
                    })
                    .catch(e => {
                        // eslint-disable-next-line
                        console.error('e', e);
                    });
            } else {
                navigate({
                    url: '',
                    openType: 'navigateBack'
                });
            }
        },
        fail(err) {
            // @links https://smartprogram.baidu.com/docs/develop/api/open/payment_swan-requestPolymerPayment/
            // 判断环境变量 TARO_ENV 是否为 'swan'
            process.env.TARO_ENV === 'swan' &&
                getCuid().then(res => {
                    const sys = getSystemInfo();
                    swan.reportAnalytics('payment_err_record', {
                        sys: (sys.system || '').toLowerCase().replace(/[\d.]/g, ''),
                        code: err && err.errCode,
                        version: sys.version,
                        error: err && err.errMsg,
                        host: sys?.host,
                        cuid: res.cuid,
                        orderid: orderInfo.tpOrderId,
                        t: Date.now()
                    });
                });
            showToast({
                title: err.errMsg || '支付失败',
                icon: 'none'
            });
            if (typeof conf.fail === 'function') {
                conf.fail(err);
            } else if (userConf.data.ftr) {
                const fail = JSON.parse(userConf.data.ftr);
                navigate({
                    url: fail.url,
                    openType: fail.openType
                });
            }
        },
        complete() {
            payState = 'init';
            if (typeof conf.complete === 'function') {
                conf.complete();
            }
        }
    });
};

/**
 * 调起支付
 *
 * @param {Object} userConf 用户配置
 * @param {string} userConf.url 糯米支付地址
 * @param {Function} userConf.success 支付成功回调
 * @param {Function} userConf.fail 支付失败回调
 * @param {Function} userConf.complete 支付结束回调
 */
export const pay = userConf => {
    const q = getQuery(userConf.url);

    invokeSwanPay({
        data: q,
        extInfos: userConf.extInfos, // 请勿删除，手白收银台实验通路的参数
        inlinePaySign: userConf.inlinePaySign || '',
        externalUrl: userConf.externalUrl,
        urlType: userConf.urlType,
        success: userConf.success,
        fail: userConf.fail,
        complete: userConf.complete
    });
};

// 吊起手白收银台
export const hisPay = userConfig => {
    // eslint-disable-next-line
    console.info('暂不支持，请实现his对应平台支付逻辑', userConfig);
};

/**
 * 是否开启内联面板
 * 部分宿主暂不支持内联面板组件
 */
export const canIUseInlinePay = () => {
    let result = false;
    const unAbleList = [
        {
            host: 'bdmap',
            platform: 'ios'
        },
        {
            host: 'bdlite',
            platform: 'ios'
        },
        {
            host: 'tomas',
            platform: 'ios'
        }
    ];
    if (process.env.TARO_ENV === 'swan') {
        let {host = '', platform = ''} = getSystemInfo();
        host = host && host.toLowerCase();
        platform = platform && platform.toLowerCase();
        result = !unAbleList.some(box => host === box.host && platform === box.platform);
    }

    return result;
};
