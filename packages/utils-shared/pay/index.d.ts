// 百度小程序全局对象声明
declare const swan: {
    navigateToMiniProgram?: (options: {
        appId: string;
        path?: string;
        extraData?: Record<string, any>;
        success?: (res: any) => void;
        fail?: (err: any) => void;
        complete?: () => void;
    }) => void;
    exit?: () => void;
    [key: string]: any;
};

/**
 * 支付参数配置
 */
export interface PayProps {
    /**
     * 收银台支付地址
     * @note 用于百度小程序、H5支付
     */
    url?: string;

    /**
     * 微信支付配置
     */
    wxPayInfo?: {[key: string]: string};

    /**
     * 扩展信息，用于透传收银台 extInfos
     */
    extInfos?: {
        experimentId?: string; // 收银台接受的实验 sid
        showBaoZhang?: number; // 1为可以展示保障标 0 or 不传不展示
    };

    /**
     * 支付成功回调
     */
    success?: () => void;

    /**
     * 支付失败回调
     */
    fail?: (error) => void;
    complete?: () => void;
    wxPayConfig?: unknown;
    inlinePaySign?: string;
}
