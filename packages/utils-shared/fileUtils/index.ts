export function base64toFile(dataurl: string, type: string): File {
    // 获取到base64编码
    const arr = dataurl.split(',');
    // 将base64编码转为字符串
    const bstr = atob(arr[1]);
    let n = bstr?.length;
    const u8arr = new Uint8Array(n); // 创建初始化为0的，包含length个元素的无符号整型数组
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }

    return new File([u8arr], String(Date.now()), {
        type
    });
}

/**
 *
 * @description base64 转 blob
 * @param dataURI
 * @returns
 */
export function dataURItoBlob(dataURI) {
    // eslint-disable-next-line prefer-destructuring
    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0]; // mime类型
    const byteString = window.atob(dataURI.split(',')[1]); // base64 解码
    const arrayBuffer = new ArrayBuffer(byteString.length); // 创建缓冲数组
    const intArray = new Uint8Array(arrayBuffer); // 创建视图

    for (let i = 0; i < byteString.length; i++) {
        intArray[i] = byteString.charCodeAt(i);
    }

    return new Blob([intArray], {type: mimeString});
}

// 获取视频等比缩放宽高
export function getVideoSize(maxWidth, width, height) {
    if (maxWidth >= width) {
        return {
            width,
            height
        };
    }

    return {
        width: maxWidth,
        height: Math.floor((maxWidth / width) * height)
    };
}

/**
 *
 * @description url 转 base64
 * @param dataURI
 * @returns
 */
export function urlToBase64Async(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
        try {
            const image = new Image();

            image.setAttribute('crossOrigin', 'Anonymous');
            image.src = url;

            image.onload = function () {
                const canvas = document.createElement('canvas');
                const {width} = image;
                const {height} = image;

                canvas.width = width;
                canvas.height = height;
                const context = canvas.getContext('2d');
                context?.drawImage(image, 0, 0, width, height);

                const dataURL = canvas.toDataURL('image/png');
                // 释放内存中的canvas
                canvas.remove();
                resolve(dataURL);
            };
        } catch (e) {
            console.error('urlToBase64Async错误：', e);
            reject(e);
        }
    });
}
