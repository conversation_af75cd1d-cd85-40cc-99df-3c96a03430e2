import {init as initSwan} from '@baidu/weirwood-mp-sdk/lib/bundle.swan.esm';
import Taro from '@tarojs/taro';

import WeirwoodOptions from './weirwood.json';

export interface ReportFailedReqParams {
    url: string;
    status: number;
    params: unknown;
    response: unknown;
    method: 'POST' | 'GET';
    responseHeaders: unknown;
}

/**
 *
 * @description weirwood 初始化
 */
let reportExceptionFN;

/**
 *
 * @description 控制上报频率
 */
let errorTimeout: unknown = null;

export const weirwoodInit = () => {
    if (!reportExceptionFN) {
        const reportException = initSwan({
            common: {
                buildid: WeirwoodOptions.buildid,
                token: WeirwoodOptions.mpToken
            },
            perf: {
                sampleRate: 0.3
            }
        });
        reportExceptionFN = reportException.reportException;
    }
};

/**
 *
 * @description weirwood 请求错误上报
 */
export const weirwoodErrorReport = (errorParams: any) => {
    if (errorTimeout) return;
    errorTimeout = setTimeout(() => {
        errorTimeout = null;
    }, 1000);
    const page = Taro.getCurrentInstance().router?.path;
    reportExceptionFN({
        page,
        env: {},
        exception: {
            type: errorParams.type || errorParams.name || 'Error',
            value: errorParams.message || errorParams.error || 'errorMsg'
        },
        ...(errorParams instanceof Error ? {errorInstance: errorParams} : {})
    });
};

export const weirwoodReqFailedReport = (args: ReportFailedReqParams) => {
    const {url, params, response, status, method, responseHeaders} = args;
    const page = Taro.getCurrentInstance().router?.path;

    reportExceptionFN({
        page,
        env: {},
        exception: {
            url,
            status,
            method,
            type: 'APIError',
            params: JSON.stringify(params),
            response: JSON.stringify(response),
            responseHeaders: JSON.stringify(responseHeaders)
        }
    });
};
