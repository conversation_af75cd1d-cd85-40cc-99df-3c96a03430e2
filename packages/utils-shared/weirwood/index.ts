import * as Weirwood from '@baidu/weirwood-sdk';
import Taro from '@tarojs/taro';

import {isUCBrowser} from '../commonUtils';

import WeirwoodOptions from './weirwood.json';

export interface ReportFailedReqParams {
    url: string;
    status: number;
    params: unknown;
    response: unknown;
    method: 'POST' | 'GET';
    responseHeaders: unknown;
}

/**
 *
 * @description weirwood 初始化
 */
let reportExceptionFN;

/**
 *
 * @description 控制上报频率
 */
let errorTimeout: unknown = null;

export const weirwoodInit = () => {
    if (!reportExceptionFN) {
        const reportException = Weirwood.init({
            common: {
                buildid: WeirwoodOptions.buildid,
                token: WeirwoodOptions.token,
                ignoreUrls: [
                    // 本地开发屏蔽发送
                    'localhost',
                    '127.0.0.1',
                    'pc.baidu.com',
                    'jiankang-dev.baidu.com'
                ],
                // Tips: UC 浏览器场景下 beacon 请求未携带 cookie 导致网关 baiduId 二次生成覆盖；@wanghaoyu08
                disableSendBeacon: isUCBrowser()
            },
            error: {
                collectWindowErrors: true,
                // promise异常
                collectUnhandledRejections: true,
                // 静态资源加载异常
                collectResourceLoadErrors: true
            },
            perf: {
                // 性能数据PV日志会比较大，可以输入 sampleRate 进行采样，控制在 50 W左右
                sampleRate: 0.3,
                spa: true,
                history: true
            }
        });
        reportExceptionFN = reportException;
    }
};
/**
 *
 * @description weirwood 请求错误上报
 */
export const weirwoodErrorReport = (errorParams: any) => {
    if (errorTimeout) return;
    errorTimeout = setTimeout(() => {
        errorTimeout = null;
    }, 1000);
    reportExceptionFN.error.captureException(errorParams);
};

export const weirwoodReqFailedReport = (args: ReportFailedReqParams) => {
    const {url, params, response, status, method, responseHeaders} = args;
    const page = Taro.getCurrentInstance().router?.path;

    reportExceptionFN({
        page,
        env: {},
        exception: {
            url,
            status,
            method,
            type: 'APIError',
            params: JSON.stringify(params),
            response: JSON.stringify(response),
            responseHeaders: JSON.stringify(responseHeaders)
        }
    });
};
/**
 * @description weirwood 自定义性能上报
 * @param customkey - 性能指标的名称 需要在平台注册
 * @param customvalue - 性能指标的值，支持单个数字或数字数组
 * @example
 * 上报单个数值
 * weirwoodCustomReport('x_mysite_fp', 123);
 * 上报数组数据
 * weirwoodCustomReport('x_mysite_fp', [100, 200, 300]);
 * 参考文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/gv0WKdV3ZF/60incnF1Zd/ezxej_fkLp6L4U
 */
export const weirwoodCustomReport = (customkey: string, customvalue: number | number[]) => {
    if (reportExceptionFN?.perf?.addUserTiming) {
        reportExceptionFN.perf.addUserTiming({
            [customkey]: customvalue
        });
    }
};
