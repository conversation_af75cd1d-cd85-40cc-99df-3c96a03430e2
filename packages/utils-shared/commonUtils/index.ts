import dayjs from 'dayjs';
import Taro from '@tarojs/taro';
/**
 * @description: 判断sf_ref
 * @return {Boolean}
 */
export const getSfRef = () => {
    let sfRef: string | null = null;
    const pages: Taro.Page[] = Taro.getCurrentPages();
    const {options} = (pages && pages.length && pages[pages.length - 1]) || {};
    if (process.env.TARO_ENV === 'h5') {
        // sfRef = options.sf_ref || Taro.getApp().globalData.sf_ref || window.sessionStorage.getItem('sf_ref');
    } else if (process.env.TARO_ENV === 'swan') {
        sfRef = options.sf_ref || Taro.getApp().globalData.sf_ref;
    }

    return sfRef;
};

/**
 * @description: 判断是否在微信小程序webview环境中
 * @return {Boolean}
 */
export const isWxMiniProgramWebView = () => {
    let result = false;
    if (process.env.TARO_ENV === 'h5') {
        const ua = window.navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i)?.includes('micromessenger')) {
            // 微信环境
            if (ua.match(/miniProgram/i)?.includes('miniprogram')) {
                // 微信小程序
                result = true;
            }
        }
    }

    return result;
};

export function uuid(len = 8, radix = 16): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
    const value: string[] = [];
    let i = 0;
    // eslint-disable-next-line no-param-reassign
    radix = radix || chars.length;

    if (len) {
        for (i = 0; i < len; i++) {
            value[i] = chars[0 | (Math.random() * radix)];
        }
    } else {
        let r;

        value[8] = value[13] = value[18] = value[23] = '-';
        value[14] = '4';

        for (i = 0; i < 36; i++) {
            if (!value[i]) {
                r = 0 | (Math.random() * 16);
                value[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r];
            }
        }
    }

    return value.join('');
}

/**
 * 判断是否为对象
 *
 * @param {*} val 待校验的值
 * @return {boolean}
 */
export const isObject = (val: object): boolean =>
    Object.prototype.toString.call(val) === '[object Object]';

/**
 * 判断是否为空对象
 *
 * @param {*} val 待校验的值
 * @return {boolean}
 */
export const isEmptyObject = (val: object): boolean =>
    isObject(val) && Object.getOwnPropertyNames(val).length === 0;

/**
 * 判断是否为数组
 *
 * @param {*} val 待校验的值
 * @return {boolean}
 */
export const isArray = (val: object) => Object.prototype.toString.call(val) === '[object Array]';

/**
 * 比较版本号（版本号位数需要相等）
 *
 * @param v1 版本1
 * @param v2 版本2
 * @returns 0：相等 | 1：v1大于v2 | -1：v1小于v2
 */
export const versionCompare = (v1: string | number, v2: string | number) => {
    if (v1 === v2) {
        return 0;
    }

    const toNum = version => {
        // eslint-disable-next-line no-param-reassign
        version = version.toString();
        // const versionArr = version.split('.')
        const versionArr = version.split(/\D/);
        const NUM_FILL = ['0000', '000', '00', '0', ''];

        for (let i = 0; i < versionArr.length; i++) {
            const len = versionArr[i].length;
            versionArr[i] = NUM_FILL[len] + versionArr[i];
        }

        return parseInt(versionArr.join(''), 10);
    };

    // eslint-disable-next-line no-param-reassign
    v1 = toNum(v1);
    // eslint-disable-next-line no-param-reassign
    v2 = toNum(v2);

    if (v1 > v2) {
        return 1;
    }
    if (v1 < v2) {
        return -1;
    }

    return 0;
};

// 英文是否大写
export function isUpperCase(num) {
    const reg = /^[A-Z]+$/;

    return reg.test(num);
}

/**
 * 多行文字根据屏幕宽度截断返回截断后的字符串
 *
 * @param {number} boxWidth 填充文字盒子的宽度
 * @param {string} text 文字
 * @param {number} fontSize 字体大小
 * @param {number} lineNum n行截断
 * @param {string} ellipsis 省略字符
 * @param {string} moreText 更多字符
 * @param {string} slotWidth slot宽度
 * @return {object}
 */
// eslint-disable-next-line max-params
export const truncatedString = function (
    text,
    boxWidth,
    fontSize,
    lineNum,
    ellipsis,
    moreText,
    slotWidth
) {
    let isTruncated = false;
    const __getStrLen = function (str, num) {
        let count = 0;
        let flag = 0;
        // eslint-disable-next-line no-control-regex
        const reg = /^[^\x00-\xff]+$/; // 双字符正则
        for (let i = 0; i < str.length; i++) {
            if (reg.test(str.charAt(i))) {
                // 中文字符
                flag = flag + 1;
            } else {
                if (isUpperCase(str.charAt(i))) {
                    flag = flag + 0.65;
                } else {
                    flag = flag + 0.5;
                }
            }
            if (flag >= num) {
                count = i;
                break;
            }
        }

        return count;
    };
    let truncatedStr = '';
    const fontNumShort = Math.floor((boxWidth / fontSize) * lineNum);
    const slotWidthFontNum = slotWidth && slotWidth > 0 ? Math.floor(slotWidth / fontSize) : 0;
    const str = text + ellipsis + moreText;
    const realNum = __getStrLen(str, fontNumShort);
    if (realNum > 0 && text.length > realNum - moreText.length) {
        truncatedStr =
            text.substring(0, realNum - moreText.length - slotWidthFontNum - 2) + ellipsis;
        isTruncated = true;
    } else {
        truncatedStr = text;
    }

    return {
        truncatedStr,
        isTruncated
    };
};

export function genImMsgKey(length) {
    const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = length; i > 0; --i) {
        result += chars[Math.floor(Math.random() * chars.length)];
    }

    return result;
}

/**
 *  判断是否为有效日期格式
 * @param dateString 日期字符串
 * @returns 是否为有效日期
 */
export const isValidDate = dateString => {
    const date = dayjs(dateString);

    return !isNaN(date?.valueOf());
};

/**
 * 匹配是否有中文
 * */
export const isValidChineseCharacter = (str: string): boolean => {
    return /[\u4e00-\u9fa5]/.test(str);
};

/**
 * 判断当前浏览器是否是UC浏览器
 * @returns bolean
 */
export const isUCBrowser = () => {
    const ua = navigator.userAgent.toLowerCase();
    if (ua.indexOf('ucbrowser') > -1 || ua.indexOf('ucweb') > -1) {
        return true;
    }

    return false;
};
