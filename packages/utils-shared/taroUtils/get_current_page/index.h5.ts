import Taro from '@tarojs/taro';
import {getUrlParams} from '../../queryUtils';

import type {MallPageProps} from './index.d';

export const getCurrentPage = (): MallPageProps => {
    let result = {
        route: '',
        path: '',
        options: {},
        isOnlyPage: false
    };
    const currentPages = Taro.getCurrentPages();

    const queryData = getUrlParams(window.location.search);

    if (currentPages && currentPages.length > 0) {
        const currentPage = currentPages[currentPages.length - 1];

        let taroParams: {[key: string]: string} = currentPage.$taroParams;
        // 处理无关参数
        delete taroParams.$taroTimestamp;
        delete taroParams.stamp;

        Object.keys(taroParams).forEach(key => {
            if (Array.isArray(taroParams[key])) {
                taroParams[key] = taroParams[key][taroParams[key].length - 1];
            }
        });

        taroParams = {
            ...taroParams,
            ...queryData
        };

        const query = Object.keys(taroParams)
            .map(key => `${key}=${taroParams[key]}`)
            .join('&');

        // 处理route
        let {route = ''} = currentPage;
        const routes = route?.split('?');
        if (routes.length > 0) {
            route = routes[0] || '';
        }

        if (route.startsWith('/')) {
            route = route.replace('/', '') || '';
        }

        result = {
            route,
            path: `${route}?${query}`,
            options: taroParams,
            isOnlyPage: currentPages.length === 1
        };
    }

    return result;
};
