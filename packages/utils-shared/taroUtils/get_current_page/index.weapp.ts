import Taro from '@tarojs/taro';
import {MallPageProps} from './index.d';

export const getCurrentPage = (): MallPageProps => {
    let result = {
        route: '',
        path: '',
        options: {},
        isOnlyPage: false
    };
    const currentPages = Taro.getCurrentPages();
    if (currentPages && currentPages.length > 0) {
        const currentPage = currentPages[currentPages.length - 1];
        const params: {[key: string]: string} = currentPage.options;
        const query = Object.keys(params)
            .map(key => `${key}=${params[key]}`)
            .join('&');

        result = {
            route: currentPage.route || '',
            path: `${currentPage.route || ''}?${query}`,
            options: currentPage.options,
            isOnlyPage: currentPages.length === 1
        };
    }

    return result;
};
