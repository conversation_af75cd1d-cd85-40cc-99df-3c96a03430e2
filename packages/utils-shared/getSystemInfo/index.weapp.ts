import {getSystemInfoSync, getMenuButtonBoundingClientRect} from '@tarojs/taro';
import {SystemInfoProps} from './index.d';

export const getSystemInfo = (): SystemInfoProps => {
    const sysInfo = getSystemInfoSync() as any;
    const menuRect = getMenuButtonBoundingClientRect();
    const {safeArea = {}, screenHeight, statusBarHeight = 0} = sysInfo;
    const navigationBarHeight = (menuRect.top - statusBarHeight) * 2 + menuRect.height;
    const bottomSafeAreaHeight = screenHeight - safeArea.bottom;

    const info = {
        ...sysInfo,
        navigationBarHeight,
        bottomSafeAreaHeight,
        barHeight: navigationBarHeight + statusBarHeight
    };

    return info;
};
