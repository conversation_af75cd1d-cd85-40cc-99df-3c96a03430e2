import {getSystemInfoSync} from '@tarojs/taro';
import {SystemInfoProps} from './index.d';

export const getSystemInfo = (): SystemInfoProps => {
    const sysInfo = getSystemInfoSync() as any;
    const {statusBarHeight = 0, fontSizeSetting = 0, isElderMode} = sysInfo;
    const navigationBarHeight = sysInfo.navigationBarHeight || 44; // 百度小程序直接返回此数据
    const bottomSafeAreaHeight = sysInfo.bottomSafeHeight || 0; // 百度小程序直接返回此数据

    const info = {
        ...sysInfo,
        navigationBarHeight,
        bottomSafeAreaHeight,
        barHeight: navigationBarHeight + statusBarHeight,
        bigFontSizeClass: fontSizeSetting >= 4 ? 'big-fontsize' : '',
        isElderMode,
        host: 'swan'
    };

    return info;
};
