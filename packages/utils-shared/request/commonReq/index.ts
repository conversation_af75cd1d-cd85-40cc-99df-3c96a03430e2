import {request} from '@tarojs/taro';

import type {
    RequestConf,
    CommonResponseVal,
    HttpRequestInstance,
    RequestReturnValType,
    HttpRequestInstanceConf
} from './index.d';

/**
 * HttpRequestInstance的构造函数
 *
 * @description 请求实例的构造函数
 * @template T 请求发送成功返回的数据类型
 * @template R 请求发送失败返回的数据类型
 * @param args HttpRequestInstance的配置对象
 * @param args.unLoginStatus 未登录时的状态码
 * @param args.statusVariable 状态码变量名
 * @param args.successfulStatus 请求成功时的状态码
 * @param args.confDecorateFn 配置装饰器函数
 * @param args.unLoginCallback 未登录时的回调函数
 * @param args.requestFailedCallback 请求失败时的回调函数
 * @param args.requestSucceedCallback 请求成功时的回调函数
 * @param args.beforeRequestCallback 请求发送前的回调函数
 * @param args.afterRequestCallback 请求发送后的回调函数
 */
export class HttpRequest<T, R> implements HttpRequestInstance<T, R> {
    constructor(args: HttpRequestInstanceConf<T, R>) {
        const {
            timeout,
            enableHttp2 = true,
            unLoginStatus,
            statusVariable,
            successfulStatus,
            confDecorateFn,
            unLoginCallback,
            requestFailedCallback,
            requestSucceedCallback,
            beforeRequestCallback,
            afterRequestCallback
        } = args;

        this.timeout = timeout;
        this.enableHttp2 = enableHttp2;

        this.unLoginStatus = unLoginStatus;
        this.statusVariable = statusVariable;
        this.successfulStatus = successfulStatus;
        this.confDecorateFn = confDecorateFn;

        this.unLoginCallback = unLoginCallback;
        this.requestFailedCallback = requestFailedCallback;
        this.requestSucceedCallback = requestSucceedCallback;
        this.afterRequestCallback = afterRequestCallback;
        this.beforeRequestCallback = beforeRequestCallback;
    }
    private timeout: HttpRequestInstanceConf<T, R>['timeout'] = 10000;
    private enableHttp2: HttpRequestInstanceConf<T, R>['enableHttp2'] = true;

    private confDecorateFn: HttpRequestInstanceConf<T, R>['confDecorateFn'];
    private statusVariable: HttpRequestInstanceConf<T, R>['statusVariable'] = 'errno';

    private unLoginStatus: HttpRequestInstanceConf<T, R>['unLoginStatus'] = [];
    private successfulStatus: HttpRequestInstanceConf<T, R>['successfulStatus'] = [];

    private requestFailedCallback: HttpRequestInstanceConf<T, R>['requestFailedCallback'];
    private requestSucceedCallback: HttpRequestInstanceConf<T, R>['requestSucceedCallback'];

    private unLoginCallback: HttpRequestInstanceConf<T, R>['unLoginCallback'];
    private afterRequestCallback: HttpRequestInstanceConf<T, R>['afterRequestCallback'];
    private beforeRequestCallback: HttpRequestInstanceConf<T, R>['beforeRequestCallback'];

    /**
     *
     * @description 基础请求能力
     * @param conf
     * @returns
     */
    private baseRequest: <S, F>(
        args: RequestConf
    ) => Promise<[CommonResponseVal<S | F> | null, unknown]> = async (conf: RequestConf) => {
            return new Promise((resolve, reject) => {
                request({
                    timeout: this.timeout,
                    enableHttp2: this.enableHttp2,
                    ...conf,
                    success(res) {
                        if (res?.statusCode === 200) {
                            resolve([
                                {
                                    ...res?.data,
                                    header: res?.header
                                },
                                res
                            ]);
                        } else {
                            reject([
                                {
                                    header: res?.header,
                                    status: res?.statusCode
                                },
                                res
                            ]);
                        }
                    },
                    fail(error) {
                        reject(error);
                    }
                });
            });
        };

    /**
     *
     * @description 用来处理首屏请求超时控制
     * @param conf
     * @returns
     */
    private requestOfFirstScreen: <S, F>(
        args: RequestConf
    ) => Promise<[CommonResponseVal<S | F> | null, unknown]> = async <S, F>(conf: RequestConf) => {
        // @ts-expect-error 历史代码
            const __timeoutHttp = new Promise((res, rej) =>
                setTimeout(() => {
                    rej([
                        {
                            [this.statusVariable]: 408,
                            msg: '网络问题，请稍后再试'
                        },
                        null
                    ]);
                }, this.timeout || conf.timeout)
            );

            return (await Promise.race([this.baseRequest(conf), __timeoutHttp])) as Promise<
            [CommonResponseVal<S | F> | null, unknown]
        >;
        };

    /**
     *
     * @description 实例化后的请求方法
     * @param params
     * @returns
     */
    public request: (conf: RequestConf) => Promise<RequestReturnValType<T, R>> = async (
        params: RequestConf
    ): Promise<RequestReturnValType<T, R>> => {
        // eslint-disable-next-line
        return new Promise(async (resolve, reject) => {
            try {
                const startTime = new Date().getTime();
                const {isFirstScreen, isNeedLogin} = params;

                // 执行 conf 装饰函数
                const finalConf = this.confDecorateFn ? await this.confDecorateFn(params) : params;
                const requestFn = isFirstScreen ? this.requestOfFirstScreen : this.baseRequest;

                // 触发请求触发回调
                this.beforeRequestCallback && this.beforeRequestCallback(finalConf);

                const [response, fullResponse] = await requestFn<T, R>(finalConf);

                // 计算请求整体时间
                const endTime = new Date().getTime();
                const spentTime = endTime - startTime;

                this.afterRequestCallback &&
                    this.afterRequestCallback({conf: finalConf, response, spentTime, fullResponse});

                if (!response) {
                    const errRes = {
                        conf: params,
                        err: 'response 值不存在'
                    };
                    reject([errRes, null]);

                    return;
                }

                // 请求成功回调
                if (this.successfulStatus?.includes(response[this.statusVariable])) {
                    this.requestSucceedCallback?.({
                        conf: finalConf,
                        response,
                        spentTime,
                        fullResponse
                    });

                    resolve([null, response]);
                } else if (this.unLoginStatus?.includes(response[this.statusVariable])) {
                    if (isNeedLogin) {
                        this.unLoginCallback?.({conf: finalConf, response, fullResponse});

                        const errRes = {
                            conf: finalConf,
                            status: response?.[this.statusVariable],
                            statusCode: 200,
                            toast: response?.toast,
                            msg: response?.msg,
                            fullResponse
                        };
                        resolve([errRes, response]);
                    } else {
                        // 触发请求成功回调
                        this.requestSucceedCallback?.({
                            conf: finalConf,
                            response,
                            spentTime,
                            fullResponse
                        });
                        resolve([null, response]);
                    }
                } else {
                    this.requestFailedCallback?.({
                        response,
                        conf: finalConf,
                        status: response?.[this.statusVariable],
                        statusCode: 200,
                        toast: response?.toast,
                        msg: response?.msg,
                        fullResponse
                    });

                    const errRes = {
                        conf: finalConf,
                        status: response?.[this.statusVariable],
                        statusCode: 200,
                        toast: response?.toast,
                        msg: response?.msg,
                        data: response?.data,
                        fullResponse
                    };

                    reject([errRes, null]);
                }
            } catch (err) {
                const [resp] = err instanceof Array ? err : [err];

                this.requestFailedCallback?.({
                    err,
                    conf: params,
                    response: {
                        ...resp
                    },
                    fullResponse: {}
                });

                const errRes = {
                    conf: params,
                    err
                };
                reject([errRes, null]);
            }
        });
    };
}
