import {getCurrentPage} from '../taroUtils';

import {isObject} from '../commonUtils';

export interface Query {
    [propName: string]: string;
}

export interface IfObject {
    [propName: string]: any;
}

export type UrlParamsType = Record<string, string>;
export function updateQueryStringParameter(uri: string, key: string, value: string): string {
    if (!value) {
        return uri;
    }
    const re = new RegExp(`([?&])${key}=.*?(&|$)`, 'i');
    const separator = uri.indexOf('?') !== -1 ? '&' : '?';
    if (uri.match(re)) {
        return uri.replace(re, `$1${key}=${value}$2`);
    }

    return `${uri + separator + key}=${value}`;
}

export function urlAddQuery(uri: string, query: Query): string {
    const __query: string[] = Object.keys(query);
    let __uri = uri;
    while (__query.length) {
        const key: string = __query.shift() as string;
        __uri = updateQueryStringParameter(__uri, key, query[key]);
    }

    return __uri;
}

/**
 *
 * @description 是用URLSearchParams来获取url中的query，解决h5中decode对特殊字符失败的问题：https://github.com/NervJS/taro/issues/15694
 * @param {string} url 样本url
 * @return {UrlParamsType} query键值对
 */
export function getUrlParams(url: string) {
    const queryString = url.split('?')[1];
    const params = new URLSearchParams(queryString);
    const result = {};
    for (const [key, value] of params.entries()) {
        result[key] = value;
    }
    return result;
}

/**
 *
 * @description 参数中如果包含%，,解决白屏问题，直接白屏报错：https://github.com/NervJS/taro/issues/15717
 * @return {UrlParamsType} query 键值对
 */
export function resetErrorQuery() {
    const query = getCurrentUrlQuery();
    const result = {};
    Object.entries(query).forEach(([key, value]) => {
        try {
            if (typeof value === 'string' && decodeUntilFullyDecodedThrowError(value)) {
                result[key] = value;
            } else {
                delete result[key];
            }
        } catch (e) {
            delete result[key];
        }
    });
    return result;
}

/**
 *
 * @description 获取url中的query，生成键值对
 * @param {string} url 样本url
 * @return {UrlParamsType} query键值对
 */
export const getQueryObject = (url: string): UrlParamsType => {
    if (!url) {
        return {};
    }
    const search = url.substring(url.lastIndexOf('?') + 1);
    const obj = {};
    const reg = /([^?&=#]+)=([^?&#]*)/g;
    search.replace(reg, function (rs, $1, $2) {
        const name = $1;
        const val = $2;
        obj[name] = val || '';

        return rs;
    });

    return obj;
};

/**
 *
 * @description 获取当前 url中的query，生成键值对
 * @return {UrlParamsType} query键值对
 */
export const getCurrentUrlQuery = (): UrlParamsType => {
    return getCurrentPage().options;
};

/**
 * @description 获取url上需要透传的参数
 */
export const getNeedTransParams = () => {
    const query: any = getCurrentUrlQuery();
    const commonParams: any = {};
    query.ref && (commonParams.ref = query.ref);
    query.sf_ref && (commonParams.sf_ref = query.sf_ref);
    query.user_card_id && (commonParams.user_card_id = query.user_card_id);
    query.addrId && (commonParams.addrId = query.addrId);
    query.fromPush && (commonParams.fromPush = query.fromPush);
    query.bd_vid && (commonParams.bd_vid = query.bd_vid);
    query.from && (commonParams.from = query.from);

    if (process.env.TARO_ENV === 'h5') {
        // 获取url中?与#之间的参数，跳转时将bd_vid进行透传
        const searchObj = getQueryObject(window.location.search);
        searchObj.bd_vid && (commonParams.bd_vid = searchObj.bd_vid);
    }

    return commonParams;
};

/**
 * 将对象转化为query字符串
 * @param obj 对象
 * @returns 字符串
 */
export const getQueryStr = (obj: IfObject): string => {
    if (isObject(obj)) {
        return Object.getOwnPropertyNames(obj)
            .map((key: string) => `${key}=${obj[key]}`)
            .join('&');
    }

    return '';
};

/**
 * 扩展对象
 *
 * @param {...Object} args 扩展对象参数列表
 * @return {Object} 扩展后的对象
 */
export function extend(...args: IfObject[]) {
    const arr = Array.prototype.slice.call(args);
    const org = arr.shift();
    arr.forEach(function (obj: IfObject) {
        const o = obj || {};
        Object.getOwnPropertyNames(o).forEach(function (key: string) {
            if (isObject(o[key]) && isObject(org[key])) {
                extend(org[key], o[key]);
            } else {
                if (o[key] !== undefined) {
                    org[key] = o[key];
                }
            }
        });
    });

    return org;
}

export const mapChangeToQuery = (param, key: string | null = null, encode = false) => {
    if (param == null) {
        return '';
    }
    let paramStr = '';
    const t = typeof param;
    if (t === 'string' || t === 'number' || t === 'boolean') {
        paramStr += `&${key}=${encode == null || encode ? encodeURIComponent(param) : decodeURIComponent(param)}`;
    } else {
        // eslint-disable-next-line guard-for-in
        for (const i in param) {
            const k = key == null ? i : key + (param instanceof Array ? `[${i}]` : `.${i}`);
            paramStr += mapChangeToQuery(param[i], k, encode);
        }
    }

    return paramStr;
};

export const isUrlEncoded = str => {
    try {
        // 尝试对字符串进行解码
        return decodeURIComponent(str) !== str;
    } catch (error) {
        // 如果解码失败，则说明字符串没有被 URL 编码
        return false;
    }
};

/**
 *
 * @description 循环 decode 字符串，直到字符串中完全没有 encode 字符
 * @param input
 * @returns
 */
export const decodeUntilFullyDecoded = (input: string): string => {
    try {
        let decoded = decodeURIComponent(input);
        while (input !== decoded) {
            // eslint-disable-next-line no-param-reassign
            input = decoded;
            decoded = decodeURIComponent(input);
        }
        return decoded;
    } catch (err) {
        console.error('decodeUntilFullyDecoded 出错：', err);
        return input;
    }
};

/**
 *
 * @description 循环 decode 字符串，直到字符串中完全没有 encode 字符，会抛出异常
 * @param input
 * @returns
 */
export const decodeUntilFullyDecodedThrowError = (input: string): string => {
    try {
        let decoded = decodeURIComponent(input);
        while (input !== decoded) {
            // eslint-disable-next-line no-param-reassign
            input = decoded;
            decoded = decodeURIComponent(input);
        }
        return decoded;
    } catch (err) {
        console.error('decodeUntilFullyDecodedThrowError', err);
        throw err;
    }
};

/**
 *
 * @param str url query
 * @returns url query进行encodeURIComponent输出
 */
export const urlencodeChinese = str => {
    try {
        const _newList = str?.split('&')?.map(item => {
            const _keyArray = item?.split('=');
            // eslint-disable-next-line prefer-destructuring
            const _value = _keyArray[1];
            if (_value && !isUrlEncoded(_value)) {
                // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                return `${_keyArray[0]}=${encodeURIComponent(_value)}`;
            }

            return item;
        });

        return _newList?.join('&');
    } catch (error) {
        console.error(error);

        return str;
    }
};

export function isContainsChinese(str: string): boolean {
    return /[\u4e00-\u9fa5]/.test(str);
}

/**
 *
 * @description 逗号分隔参数
 * @param input
 * @returns
 */
export const getSceneParams = (paramStr: string): UrlParamsType => {
    try {
        const decodeParams = decodeUntilFullyDecoded(paramStr);
        const result = {};
        const pairs = decodeParams.split(',');

        pairs.forEach(pair => {
            const [key, value] = pair.split('=');
            result[key] = value;
        });

        return result;
    } catch (err) {
        console.error('decodeUntilFullyDecoded 出错：', err);
        return {};
    }
};
