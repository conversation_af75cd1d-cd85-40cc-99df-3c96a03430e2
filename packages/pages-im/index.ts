export {default as Test} from './src/pages/test';
export {default as Im} from './src/pages/im';

import {useGetLastMsgId, useGetSessionId} from './src/hooks/triageStream/pageDataController';
import {useConversationDataController} from './src/hooks/triageStream/useConversationDataController';
import {getDataForUbcAtom} from './src/store/triageStreamAtom/index';
import {useGetCardEventCallback} from './src/hooks/useGetCardEventCallback';
import {
    ubcCommonClkSend,
    ubcCommonViewSend,
    ubcCommonTimingSend,
    ubcFn
} from './src/utils/generalFunction/ubc';
import {versionControl} from './src/utils/generalFunction/version';
import {useCapsuleToolsController} from './src/hooks/useCapsuleTools';
import {
    useGetOngoingToast,
    useGetTBotEntranceCardStatus,
    useGetTitleInfo,
    useGetViewOrderInfo
} from './src/hooks/triageStream/dataController';
import {useUpload} from './src/components/ImInput/hook/useUpload';

import {showToast} from './src/utils/customShowToast';
import {navigate} from './src/utils/basicAbility/commonNavigate';
import httpRequest from './src/utils/basicAbility/comonRequest/cui';

export type {ICardProps, InteractionType, InteractionInfo} from './src/typings/index.d';

export type {MsgInstanceData} from './src/typings/im.d';

// todo: utils 后续再进行拆分
const utils = {
    showToast,
    ubcCommonClkSend,
    ubcCommonViewSend,
    ubcCommonTimingSend,
    ubcFn,
    navigate,
    httpRequest,
    versionControl
};

const hooks = {
    useGetLastMsgId,
    useGetSessionId,
    useConversationDataController,
    useGetCardEventCallback,
    useCapsuleToolsController,
    useGetOngoingToast,
    useGetTBotEntranceCardStatus,
    useGetTitleInfo,
    useGetViewOrderInfo,
    useUpload
};

const store = {
    getDataForUbcAtom
};

export {utils, hooks, store};
