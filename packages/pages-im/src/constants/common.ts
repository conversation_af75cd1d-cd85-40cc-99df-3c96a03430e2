import type {MSG_CARDID_ENUM_STREAM} from '../constants/msg';

// url 链接透传到后续链接中
export const PASSED_URL_PARAMS_FOR_HREF = [
    'sf_ref',
    'from',
    'from_sf',
    'scene',
    'lid',
    'referlid',
    'channel_code',
    'source',
    'disp',
    'clk_loc'
];

// url 链接透传到后续请求接口的参数
export const PASSED_URL_PARAMS = [
    'isDirected', // 诊前是否定向问诊
    'expert_id', // 医生 id
    'loId', // 履约 id
    'soId', // 订单 id
    'from_sf',
    'word', // 输入框文案
    'query', // 搜索词
    'scene',
    'content',
    'search',
    'sf_ref',
    'from',
    'channel',
    'entrance',
    'platform',
    'clk_loc',
    'flagext',
    'rs_id',
    'source_expert_id',
    'expert_cop',
    'phone_plan_id',
    's_type',
    'aid',
    'referlid',
    'lid',
    'reqtoken',
    'swanVersion',
    'swanID',
    'departName',
    'depCode',
    'diseaseCode',
    'hospitalName',
    'hosCode',
    'reportId',
    'recallfrom',
    'specDepartmentName',
    'specDepartmentCode',
    'specDiseaseCode',
    'specHospitalName',
    'specHospitalCode',
    'huYiRecordCode',
    'huYiPatientID',
    'huYiSwanID',
    'huYiStatistics',
    'huYiOriginChannel',
    'vipIDTaiKang',
    'vipUserCardIDTaiKang',
    'channelCodeTaiKang',
    'uidTaiKang',
    'service_type',
    'reportID',
    'vipid', // 泰康场景
    'channelcode', // 泰康场景
    'vipusercardiD', // 泰康场景
    'vipusercardid',
    'tkUid', // 泰康场景
    'record_code', // 互医 · 申请记录编
    'patient_id', // 互医 · patientId
    'swanid', // 互医 · 唯一ID
    'statistics', // 互医 · 统计字段
    'origin_channel', // 互医 · 来源渠道
    'phone_plan_id', // 定向电话问诊渠道
    'copy_qid', // 兼容老逻辑处理主诉
    'doc_id', // 标品id/医生id,对外页面，通过doc_id进行流转,跨业务线（问诊调用医生主页）调用
    'extra_data', // BI 数据统计
    'resource_id', // 用来获取医生 id
    'source_expert_id', // 用来获取医生 id
    'rs_id', // 用来获取医生 id
    'searchID', // 主诉表统计数据
    'nPageID', // 主诉表统计数据
    'apmId', // mcn 预下单id
    'preRecordBizType', // 预下单业务类型
    'preRecordBizId', // 预下单业务id
    'diseaseIntent', // 意向识别
    'benefitId', // o2o 场景权益 id
    'storeId', // o2o 场景门店 id
    'o2oDomain',
    'disp',
    'medQuSndDept',
    'medQuFstDept'
];

// 微信小程序appid
export const WX_APPID_MAP = {
    // 互联网医院公众号-正式
    netHospitalService: 'wxd40f0c8afa99bbf3',
    // 互联网医院公众号-测试
    // netHospitalService: 'wx0a0ea08654caf9d5'
    askAppId: 'wx8911e67a01f3eaea',
    jiayiAppId: 'wx407ee1c7ec3358bf',
    drugInstructions: 'wx3972b716558187a1'
};

// swan_name取值
export const SWAN_NAME_MAP = {
    wenzhen: {
        h5: '',
        swan: 'baidujiankang',
        weapp: 'wx_wenzhen'
    },
    jiayi: {
        h5: '',
        swan: 'baidujiankang',
        weapp: 'wx_jiayi'
    },
    njhn: {},
    njyc: {}
};

// appKey 取值 map
export const APP_KEY_MAP = {
    wenzhen: {
        h5: 'web_wenzhen',
        swan: 'bd_wenzhen',
        weapp: 'wx_wenzhen'
    },
    jiayi: {
        h5: 'web_jiayi',
        swan: 'bd_jiayi',
        weapp: 'wx_jiayi'
    },
    // 海南年检
    njhn: {
        h5: 'web_njhn',
        swan: 'bd_njhn',
        weapp: 'wx_njhn'
    },
    // 银川年检
    njyc: {
        h5: 'web_njyc',
        swan: 'bd_njyc',
        weapp: 'wx_njyc'
    }
};

// 诊前最大上传图片数量
export const TIRAGE_MAX_PIC_NUM = 12;

// 就诊人弹层上传图片单次最多上传个数
export const SINGLE_NUM = 6;

export const H5_UPLOAD_PIC_NUM_TIPS = `单次最多上传${SINGLE_NUM}张`;

// 默认超时时间
export const DEFAULT_TIME_OUT = 10 * 1000;

export const APP_ID_MAP = {
    wenzhen: 'wx8911e67a01f3eaea',
    jiayi: 'wx407ee1c7ec3358bf'
};

export const COMMON_CONF = {
    mode: 'cors',
    credentials: 'include'
};

// 请求成功的 status
// 挂号业务请求成功 status 不包含 1
export const SUCCESSFUL_STATUS = [
    0,
    '0',
    201,
    202,
    3552007, // 诊前 ImRot 超时重试
    3553002, // 泰康诊前场景, 如果校验到该用户没有泰康券，默认走非定向的主诉收集页
    300001 // MCN 预约二维码已使用
];

// 需要从缓存中获取后透传的query
export const COMMON_PARAMS = ['sf_ref', 'bd_vid', 'searchID', 'clk_loc', 'doc_id'];

export const pageNetworkStatus = {
    /**
     * 初始状态
     */
    init: 1,

    /**
     * 加载中
     */
    loading: 2,

    /**
     * 加载失败
     */
    error: 3,

    /**
     * 加载成功
     */
    success: 4
};

// 当前线上域名
export const ONLINE_HOST = [
    'http://jiankang.baidu.com',
    'https://jiankang.baidu.com',
    'https://expert.baidu.com',
    'https://jiankang-fenji.baidu.com'
];

// 家医网页客服页面QUERY
export const JIAYI_SERVICE_WEB_PAGE_QUERY =
    '?appKey=JIANKANGUFO&questionnaire=wenzhen&servicerGroupId=5&servicerGroupType=9&source=wenzhen';

// WX_APP_KEY 取值 map
export const WX_APP_KEY_MAP = {
    wx_wenzhen: 'netHospitalSwan',
    bd_wenzhen: '',
    web_wenzhen: '',
    wx_jiayi: 'maleSwan',
    bd_jiayi: '',
    web_jiayi: '',
    wx_aiAssistant: 'aiHealthAssistant',
    web_aiAssistant: '',
    bd_aiAssistant: 'rfYZ5wj5vvw5HyE4zuMBjqLng67MRrA5'
};

/**
 *
 * @description 流式卡片 cardId 值的集合
 */
export type MSG_CARDID_STREAM_TYPE =
    `${Extract<MSG_CARDID_ENUM_STREAM, number>}` extends `${infer N extends number}` ? N : never;

// 在微信小程序平台中，配置的实验参数，支持多个实验
// https://wedata.weixin.qq.com/mp2/abtest/board
export const WX_APP_SIDS = [
    'expt_1686799252',
    'expt_1686795603',
    'expt_1688376303',
    'expt_1693377421',
    'expt_1695377895',
    'expt_1698563081',
    'expt_1709714305',
    'expt_1710505179',
    'expt_1712560364', // vx2.4.8 非定向服务列表 @徐楠
    'expt_1712477470,', // 复购弹窗收起
    'expt_1714302174', // 挽留弹窗新通路_非定向
    'expt_1714302326', // 挽留弹窗新通路_定向
    'expt_1718938023', // 诊前悬浮球实验
    'expt_1718956631', // 医生列表锦旗标签实验 @崔家源
    'expt_1719561087', // 降投诉-专家义诊优化 @徐楠
    'expt_1721195915', // 微信-医生列表曾经问过、最近浏览 @翠家源
    'expt_1721636282', // 降投诉-优化流程 @徐楠 支付前置 & 快捷退款入口
    'expt_1722329076', // 定向列表丰富医生擅长及医院信息 @丁冉
    'expt_1721722882', // 挂号-填单页实验
    'expt_1725333660', // 定向排序锦旗样式及策略优化 @崔家源
    'expt_1729237327', // 定向问诊新增平台服务费 @丁冉
    'expt_1731058721', // 定向主诉弹窗 @徐楠
    'expt_1733737759', // 主诉收集页未输入状态下输入提示优化 @徐楠
    'expt_1737365897', //  诊前就诊人策略优化 @徐楠
    'expt_1736425324', // 处方详情显示赠送用药咨询文案 @张国胜
    'expt_1741678871', // 用户体验专项-降投诉 @徐楠
    'expt_1742269044' // 诊前接入 AI 智能体 @赖宇
];

export const USER_DEFAULT_PIC = 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/default-head.jpg';

// 微信分享黑名单
export const weappShareBlacklist = [
    '/pages/chat/index', // 问诊im
    '/pages/guahao/orderList/index', // 我的挂号
    '/pages/guahao/orderDetail/index', // 挂号订单详情
    '/pages/guahao/orderState/index', // 挂号订单状态页
    '/pages/guahao/patientInfoEdit/patientFill/index', // 挂号-填写挂号信息
    '/pages/imRecipeLlist/index', // 就诊人信息页
    '/pages/recipellist/index', // 我的处⽅
    '/pages/recipeldetail/index', // 处⽅详情
    '/pages/message/list/index', // 消息列表
    '/pages/personalCenter/index', // 个⼈中⼼
    '/pages/patientRegistration/detail/index', // 患者报道状态页
    '/pages/patientRegistration/patientInfo/index', // 患者报道填写信息页
    '/vas/pages/orderList/index', // 我的服务包
    '/vas/pages/mineEquity/index', // 服务包订单详情
    '/pages/serviceRecord/list/index', // 我的医⽣
    '/pages/serviceRecord/detail/index', // 我的医⽣-服务详情
    '/pages/order/list/index', // 我的问诊
    '/pages/order/detail/index', // 问诊订单详情
    '/pages/middlePage/index', // 中间页
    '/pages/reward/index', // 答谢医生页面
    '/pages/common/validateLogin/index', // 账号不一致的错误提示页面
    '/pages/common/loginMiddlePage/index', // 登录中间页面
    '/pages/common/error/index', // 错误页面
    '/pages/guahao/middlePage/index', // 挂号中间页
    '/pages/bdpass/index' // 登录页
];

// 商城页面地址
export const MALL_PATH = {
    home: 'pages/home/<USER>'
};

// 本地 appid map
export const LOCAL_APPID_MAP = {
    wx_shangcheng: 'wx4b70768c0088b57c',
    wx_jiayi: 'wx407ee1c7ec3358bf',
    wx_drugInstructions: 'wx3972b716558187a1',
    wx_wenzhen: 'wx8911e67a01f3eaea',
    healthTipsBag: 'wxe5937e34d797758d',
    wx_aiAssistant: 'wxd3391eabd22a7c3a'
};

export const BUCKET_NAME = {
    2: 'muzhi-pic',
    3: 'muzhi-audio',
    4: 'muzhi-video',
    6: 'muzhi-pic'
};

export const ServiceTypeMap = {
    11602: 'undirectService',
    11601: 'docService'
};

// IM页滚动动画禁用事件
export const SCROLL_ANIMATION_DISABLE_ONCE = 'scrollWithAnimation:disableOnce';
