/**
 * 支持的弹窗类型常量
 */

// 图片上传相关的控制类型
export const UPLOAD_IMG_CTRL_TYPES = ['uploadImgReport', 'uploadImgSkin'] as const;

// 类型定义
export type UploadImgCtrlType = (typeof UPLOAD_IMG_CTRL_TYPES)[number];

/**
 * 检查是否为图片上传相关的类型（包括控制类型和弹窗类型）
 * @param type 要检查的类型
 * @returns 是否为图片上传相关的类型
 */
export const isUploadImgType = (type?: string) => {
    return UPLOAD_IMG_CTRL_TYPES.includes(type as UploadImgCtrlType);
};
