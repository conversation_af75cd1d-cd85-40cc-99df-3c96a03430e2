// import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
// import { Action, Middleware, ThunkAction, configureStore } from '@reduxjs/toolkit';

// import logger from 'redux-logger';
// import rootReducer from './reducer';

// const middleware: Middleware[] = process.env.NODE_ENV === 'development' ? [] : [];

// export const store = configureStore({
//     middleware: getDefaultMiddleware => getDefaultMiddleware({}).concat(...middleware),
//     reducer: rootReducer,
//     devTools: process.env.NODE_ENV !== 'production'
// });

// export type RootState = ReturnType<typeof store.getState>;
// export type AppDispatch = typeof store.dispatch;
// export type AppThunk = ThunkAction<void, RootState, null, Action<string>>;

// export const useAppDispatch = () => useDispatch<AppDispatch>();
// export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
