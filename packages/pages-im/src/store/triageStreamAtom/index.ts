import {atom, createStore} from 'jotai';

import type {MsgId, Qid} from '../../typings';

import {
    imWelcomeMouleMsgIdsAtom,
    adjectiveDirectedSkuMsgIdAtom,
    imWelcomeMouleDisplayStatusAtom,
    adjectiveRecommendExpertMsgIdAtom,
    adjectiveRecommendUnDirectMsgIdAtom
} from './speCardAtom';
import {agreementInfoAtom} from './otherData';
import {serviceDeclareAtom} from './imTools';
import {curSessionMsgIdsAtom, resetTriageStreamMsgAtom, triageStreamMsgAtomFamily} from './msg';

import type {
    SessionId,
    CapsulesToolsType,
    UserDataType,
    TitleDataType,
    InputDataType,
    DataForUbcType
} from './index.type';

// AI 诊前页面的 Jotai Store
export const triageStreamAtomStore = createStore();

export const sessionAreaLoadingAtom = atom<boolean>(false);
sessionAreaLoadingAtom.debugLabel = 'sessionAreaLoadingAtom';

// 当前会话 Qid
export const curSessionQidAtom = atom<Qid>('');
curSessionQidAtom.debugLabel = 'curSessionQidAtom';

// 当前会话 ID，用于区分会话
export const curSessionIdAtom = atom<SessionId>();
curSessionIdAtom.debugLabel = 'curSessionIdAtom';

// 最后一条消息 ID
export const lastMsgIdAtom = atom<MsgId>('');
lastMsgIdAtom.debugLabel = 'lastMsgIdAtom';

// 最后一条 Conversion 返回的消息 ID
export const lastConversionMsgIdAtom = atom<MsgId>('');
lastConversionMsgIdAtom.debugLabel = 'lastConversionMsgIdAtom';

// 会话胶囊工具列表
export const sessionCapsulesToolsAtom = atom<CapsulesToolsType[]>([]);
sessionCapsulesToolsAtom.debugLabel = 'sessionCapsulesToolsAtom';
export const sessionCapsulesToolsMd5Atom = atom<string>();
sessionCapsulesToolsMd5Atom.debugLabel = 'sessionCapsulesToolsMd5Atom';

// 图片来源
export const imageSourceAtom = atom<string>('');
imageSourceAtom.debugLabel = 'imageSourceAtom';

// 输入框是否hold
export const textareaHoldAtom = atom<boolean>(true);
textareaHoldAtom.debugLabel = 'textareaHoldAtom';

// 输入框是否聚焦
export const textareaFocusAtom = atom<boolean>(false);
textareaFocusAtom.debugLabel = 'textareaFocusAtom';

// 会话生成状态，是否正在生成
export const isGeneratingAtom = atom<boolean>(false);
isGeneratingAtom.debugLabel = 'isGeneratingAtom';

// 是否由用户主动点击中断按钮终止对话
export const isUserInterruptedAtom = atom<boolean>(false);
isUserInterruptedAtom.debugLabel = 'isUserInterruptedAtom';

export const getUserInterruptedAtom = () => {
    return triageStreamAtomStore.get(isUserInterruptedAtom);
};

export const textareaPlaceholder = atom<string>('有什么健康问题尽管问我');
textareaPlaceholder.debugLabel = 'textareaPlaceholder';

// 登录用户信息数据
export const userDataAtom = atom<UserDataType>({
    isLogin: false,
    avatar: '',
    name: ''
});
userDataAtom.debugLabel = 'userDataAtom';
export const getUserDataAtom = () => {
    return triageStreamAtomStore.get(userDataAtom);
};

// @wuwei44 增加featureGuidanceBubble
// 登录title信息数据
export const titleDataAtom = atom<TitleDataType>({
    title: '',
    showHistoryEntry: false,
    featureGuidanceBubble: []
});
titleDataAtom.debugLabel = 'titleDataAtom';
export const getTitleDataAtom = () => {
    return triageStreamAtomStore.get(titleDataAtom);
};

// 登录用户信息数据
export const inputDataAtom = atom<InputDataType>({
    bottomTips: '',
    loadingTips: '',
    uploadImgInstruction: undefined,
    uploadImg: 0
});
inputDataAtom.debugLabel = 'inputDataAtom';

// 获取自动调起弹层
export const popDataAtom = atom<InputDataType>({
    type: '',
    uploadImgInstruction: undefined
});
popDataAtom.debugLabel = 'popDataAtom';

// UBC 数据埋点通用字段
export const dataForUbcAtom = atom<DataForUbcType>();
dataForUbcAtom.debugLabel = 'dataForUbcAtom';

// 更新 UBC 数据埋点通用字段
export const updateDataForUbcAtom = (data: DataForUbcType['product_info']) => {
    const state = triageStreamAtomStore.get(dataForUbcAtom);
    const mergeData: DataForUbcType = {
        ...state,
        // eslint-disable-next-line camelcase
        product_info: {
            ...state?.product_info,
            ...data
        }
    };

    triageStreamAtomStore.set(dataForUbcAtom, mergeData);

    return dataForUbcAtom;
};

export const getDataForUbcAtom = () => {
    return triageStreamAtomStore.get(dataForUbcAtom);
};

export const resetSessionIdAtom = () => {
    triageStreamAtomStore.set(curSessionIdAtom, undefined);
};

export const setIsGeneratingAtom = (value: boolean) => {
    triageStreamAtomStore.set(isGeneratingAtom, value);
};

export const setIsUserInterruptedAtom = (value: boolean) => {
    triageStreamAtomStore.set(isUserInterruptedAtom, value);
};

// 当前会话的消息原子 key 列表
export const sessionMsgAtomKeysAtom = atom<string[]>([]);

// tips弹框提示数据
export const tipsDataAtom = atom<CapsulesToolsType['instruction'] | undefined>();

// 重置消息原子
export const resetTriageStreamMsgIdsAtom = (msgIds?: string[], sessionId?: SessionId) => {
    msgIds?.length &&
        msgIds.forEach(item => {
            resetTriageStreamMsgAtom(`${sessionId}_${item}`);
        });
};

// 滚动到底部按钮是否可见
export const scrollToBottomBtnVisibleAtom = atom<boolean>(false);
scrollToBottomBtnVisibleAtom.debugLabel = 'scrollToBottomBtnVisibleAtom';

// 切换会话时的清理函数
export const switchSession = (
    oldSessionId: SessionId,
    newSessionId?: SessionId | undefined,
    needToClearSpecifiedMsgIds?: boolean
) => {
    if (!oldSessionId) throw new Error('oldSessionId is required');

    // 1. 先暂停输入，防止用户继续操作
    triageStreamAtomStore.set(textareaHoldAtom, true);
    triageStreamAtomStore.set(textareaFocusAtom, false);
    triageStreamAtomStore.set(textareaPlaceholder, '有什么健康问题尽管问我');

    // 2. 重置消息相关状态
    triageStreamAtomStore.set(lastMsgIdAtom, '');
    triageStreamAtomStore.set(lastConversionMsgIdAtom, '');

    triageStreamAtomStore.set(imWelcomeMouleMsgIdsAtom, []);
    triageStreamAtomStore.set(imWelcomeMouleDisplayStatusAtom, false);
    triageStreamAtomStore.set(serviceDeclareAtom, true);
    triageStreamAtomStore.set(adjectiveDirectedSkuMsgIdAtom, '');
    triageStreamAtomStore.set(adjectiveRecommendExpertMsgIdAtom, []);
    triageStreamAtomStore.set(adjectiveRecommendUnDirectMsgIdAtom, []);
    triageStreamAtomStore.set(agreementInfoAtom, undefined);
    triageStreamAtomStore.set(isGeneratingAtom, false);
    triageStreamAtomStore.set(isUserInterruptedAtom, false);

    triageStreamAtomStore.set(curSessionMsgIdsAtom, {
        type: 'delete',
        payload: triageStreamAtomStore.get(curSessionMsgIdsAtom)
    });

    // 3. 清理旧会话的消息原子
    !needToClearSpecifiedMsgIds &&
        triageStreamMsgAtomFamily.setShouldRemove((_createdAt: number, key: string) => {
            return !!key.toString().startsWith(`${oldSessionId}_`);
        });

    // 4. 重置其他 UI 状态
    triageStreamAtomStore.set(sessionCapsulesToolsAtom, []);
    triageStreamAtomStore.set(imageSourceAtom, '');
    triageStreamAtomStore.set(sessionCapsulesToolsMd5Atom, undefined);
    triageStreamAtomStore.set(inputDataAtom, {
        bottomTips: '',
        loadingTips: '',
        uploadImg: 0,
        uploadImgInstruction: undefined
    });
    triageStreamAtomStore.set(tipsDataAtom, undefined);
    triageStreamAtomStore.set(scrollToBottomBtnVisibleAtom, false);

    // 5. 最后更新会话 ID
    newSessionId && triageStreamAtomStore.set(curSessionIdAtom, newSessionId);
};
