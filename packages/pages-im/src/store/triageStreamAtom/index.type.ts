export type {ImFlowData} from '@baidu/vita-ui-cards-common';

import {MSG_CARDID_ENUM} from '../../constants/msg';
import type {InteractionType, MsgId} from '../../typings';
import type {InteractionInfo} from '../../typings/im.d';
import type {ICardProps, OwnerTypeValue, PosterInfo, ActionInfo} from '../../typings/msg.d';

export type SessionId = string;

export type MsgValueType = ICardProps<unknown> | undefined;

export type TriageStreamMsgAtomSymbolType = `${SessionId}_${MsgId}`;

export type MsgAtomEventAction =
    | {type: 'update'; payload: MsgItemType<unknown>}
    | {type: 'updateDataOfData'; payload: MsgItemType<unknown>['data']}
    | {type: 'append'; payload: MsgItemType<unknown>}
    | {type: 'end'; payload: MsgItemType<unknown>}
    | {type: 'endManually'; payload: null}
    | {type: 'reset'; payload: null};

export type TextareaActionType = {
    type: 'hold' | 'notHold';
    payload: boolean;
};

// TODO:MsgItemType 泛型传递待补充；@wanghaoyu08
export interface MsgItemType<T> {
    type: 'dynamic' | 'static';
    meta: MsgMetaData;
    data: MsgInstanceData<T>;
}

export const PosterRole = {
    RoleUser: 1,
    RoleDoctor: 2,
    RoleAssistantDoctor: 3,
    RoleRobot: 4,
    RoleBusiness: 5,
    RoleZhiGuDoctor: 6,
    RoleDocAgent: 7
} as const;

export const HideLoadingMsg = {
    // MSG_CARDID_ENUM['ImImage'] : 'ImImage'
    [MSG_CARDID_ENUM['ImImage']]: 'ImImage'
} as const;

export type PosterRoleType = (typeof PosterRole)[keyof typeof PosterRole];

export interface MsgMetaData {
    msgId: MsgId;
    rounds?: number;
    sendTime: string;
    createTime: string;
    sessionId: SessionId;
    posterInfo: PosterInfo;
    ownerType: OwnerTypeValue;
    showPosterRole: PosterRoleType;
    localMsgStatus?: 'pending' | 'rejected' | 'success' | 'aborted'; // 用于处理本地消息发送状态，非持久化字段；
    localExt?: {
        // 消息数据来源；mock 为本地 mock 数据，conversation 为 conversation sse 返回数据，history 为获取历史消息数据；
        dataSource: 'conversation' | 'history' | 'mock';
        insertType?: 'unshift' | 'push';
        needScrollToBottom?: boolean;
    };
}

export interface SearchReferences {
    title: string;
    content: string;
    isFinish: boolean;
}

// eslint-disable-next-line no-shadow
export enum AttributeStatus {
    unselect = 0,
    like = 1,
    disLike = 2
}

export interface MsgInstanceData<T> {
    action: 'append' | 'end';
    content: ICardProps<T>; // T 为具体的消息卡片协议；
    searchReferences?: SearchReferences;
    feature?: {
        attributeStatus: (typeof AttributeStatus)[keyof typeof AttributeStatus];
        // 功能数据；
        like: {
            icon: {
                imgUrl: string;
                selectedImgUrl: string;
            };
            actionInfo: InteractionInfo;
        };
        dislike: {
            icon: {
                imgUrl: string;
                selectedImgUrl: string;
            };
            actionInfo: InteractionInfo;
        };
        tts: {
            // 语音播报相关数据
            speaker: string;
        };
        copyInfo: {
            // 复制功能
            enable: boolean;
        };
        ext: [];
    };
}

type ContentListType<T extends string> = T extends 'img'
    ? {
          desc: string;
          small: string;
      }[]
    : string[];

type ContentItem<T extends 'text' | 'img' | 'title'> = {
    type: T;
    data: {
        value: string;
        list?: ContentListType<T>;
    };
};

export interface CapsulesToolsType {
    type: string;
    text: string;
    icon: string;
    // TODO: 通用 actionInfo 声明待收敛；@wanghaoyu08
    actionInfo: ActionInfo;
    instruction?: {
        title: string;
        content: (ContentItem<'text'> | ContentItem<'img'> | ContentItem<'title'>)[];
    };
    bubbleInfo?: {
        icon: string;
        text: string;
        showTime: number;
    };
}

export interface UserDataType {
    avatar: string;
    name: string;
    isLogin: boolean;
}

export interface FeatureGuidanceBubbleItem {
    content: string;
}

export interface TitleDataType {
    title: string;
    showHistoryEntry: boolean;
    featureGuidanceBubble?: FeatureGuidanceBubbleItem[];
}

export interface InputDataType {
    type?: string;
    bottomTips?: string;
    loadingTips?: string;
    uploadImg?: number;
    uploadImgInstruction?: CapsulesToolsType['instruction'] | undefined;
}

export interface DataForUbcType {
    product_info: {
        sessionId?: SessionId;
        msgId?: string;
        lastMsgId?: string;
        rounds?: number;
        cardId?: string;
        showPosterRole?: PosterRoleType;
        skuList?: string[];
    };
}

export interface SSEResponseType {
    data: {
        toolData?: {
            capsules: {
                list: CapsulesToolsType[];
                md5: string;
            };
        };
        message?: MsgItemType<unknown>[];
        ctrlData?: {
            convStatus?: 'start';
            toast?: string;
            riskControl?: {
                // 风控命中信息；
                hitRisk: boolean; // 是否命中风控；
                riskDesc: string; // 风控需要 update 到手百消息列表的文本；
            };
        };
        userData: {
            isLogin: boolean;
        };
    };
    status: number;
}

export interface OngoingToastType {
    actionInfo: {
        value: string;
        disabled: boolean;
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
    hasOngoing: boolean;
}
export interface TitleInfoType {
    avatar: string;
    title: string;
    actionInfo: {
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}

export interface ViewOrderInfoType {
    avatar: string;
    title: string;
    actionInfo: {
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
    entrys?: []; // TODO: 6月底冲刺项目后端viewOrderEntrys上线后可删除
}

export interface ViewOrderEntrysType {
    avatar: string;
    title: string;
    entrys: {
        value: string;
        disabled: boolean;
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    }[];
    actionInfo?: {
        // TODO: 6月底冲刺项目后端viewOrderEntrys上线后可删除
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}

export interface StatusDataType {
    topTips?: {
        progress: unknown;
        orderGuideTip: {
            hasOngoing: boolean;
            ongoingToast?: OngoingToastType;
            titleInfo?: TitleInfoType;
            viewOrderInfo?: ViewOrderInfoType;
            viewOrderEntrys?: ViewOrderEntrysType;
        };
    };
    popTips?: {
        isShowBotEntranceCard: string;
    };
}
