// Author: z<PERSON><PERSON>yu03
// Date: 2025-05-24 15:03:35
// Description: im工具栏展示原子信息

import {atom, useAtomValue, useSetAtom} from 'jotai';

export const serviceDeclareAtom = atom<boolean>(true);
serviceDeclareAtom.debugLabel = 'serviceDeclareAtom';
/**
 *
 * @returns 获取服务声明展示状态
 */
export const useGetServiceDeclareShow = () => useAtomValue(serviceDeclareAtom);

/**
 *
 * @returns 更新服务声明展示状态
 */
export const useUpdateServiceDeclareAtomShow = () => {
    const setServiceShow = useSetAtom(serviceDeclareAtom);
    return {
        setServiceShow
    };
};
