import {atom} from 'jotai';
import {atomFamily, atomWithReducer} from 'jotai/utils';

import type {MsgId} from '../../typings';
import type {CreateConversationArgs} from '../../hooks/triageStream/index.d';

import {triageStreamMsgAtomReducer} from './msgAtomReducer';

import type {
    MsgItemType,
    MsgAtomEventAction,
    TriageStreamMsgAtomSymbolType,
    SessionId,
    SSEResponseType
} from './index.type';

import {triageStreamAtomStore} from './';

type CurSessionMsgIdsReducerActionType =
    | 'push'
    | 'unshift'
    | 'delete'
    | 'insertBefore'
    | 'deleteAfter'
    | 'reset';
interface CurSessionMsgIdsReducerAction {
    type: CurSessionMsgIdsReducerActionType;
    payload: MsgId[];
    targetId?: MsgId; // 目标消息ID，在此消息之前插入或删除此消息之后的所有消息
}

/**
 * 消息ID数组的Reducer函数，用于处理当前会话的消息ID列表的变更。
 *
 * @param state 当前状态，即当前会话的消息ID数组。
 * @param action 包含操作类型和操作数据的动作对象。
 * @returns 返回更新后的消息ID数组。
 */
const curSessionMsgIdsReducer = (
    state: MsgId[],
    action: CurSessionMsgIdsReducerAction
): MsgId[] => {
    switch (action.type) {
        // Tips: 使用循环而非 Set 保证顺序；
        case 'push': {
            const existedSet = new Set(state);
            const nextState = [...state];

            for (const id of action.payload) {
                if (!existedSet.has(id)) {
                    existedSet.add(id);
                    nextState.push(id);
                }
            }

            return nextState;
        }
        case 'unshift': {
            const existedSet = new Set(state);
            const nextState = [...state];

            for (let i = action.payload.length - 1; i >= 0; i--) {
                const id = action.payload[i];
                if (!existedSet.has(id)) {
                    existedSet.add(id);
                    nextState.unshift(id);
                }
            }

            return nextState;
        }
        case 'insertBefore': {
            const existedSet = new Set(state);
            const nextState = [...state];

            // 如果没有指定 targetId 或找不到目标位置，则按照 push 处理
            if (!action.targetId || nextState.indexOf(action.targetId) === -1) {
                for (const id of action.payload) {
                    if (!existedSet.has(id)) {
                        existedSet.add(id);
                        nextState.push(id);
                    }
                }
                return nextState;
            }

            const targetIndex = nextState.indexOf(action.targetId);
            for (let i = action.payload.length - 1; i >= 0; i--) {
                const id = action.payload[i];
                if (!existedSet.has(id)) {
                    existedSet.add(id);
                    nextState.splice(targetIndex, 0, id);
                }
            }

            return nextState;
        }
        case 'delete': {
            const payloadSet = new Set(action.payload);

            return state.filter(id => !payloadSet.has(id));
        }
        case 'deleteAfter': {
            // 如果没有指定 targetId，则不进行任何操作
            if (!action.targetId) {
                return state;
            }

            const targetIndex = state.indexOf(action.targetId);
            // 如果找不到目标消息ID，则不进行任何操作
            if (targetIndex === -1) {
                return state;
            }

            // 删除目标消息ID之后的所有消息（不包括目标消息本身）
            return state.slice(0, targetIndex + 1);
        }
        default:
            return state;
    }
};

// 当前会话的消息 ID 列表, 用于渲染卡片列表;（有序）
export const curSessionMsgIdsAtom = atomWithReducer<MsgId[], CurSessionMsgIdsReducerAction>(
    [],
    curSessionMsgIdsReducer
);
curSessionMsgIdsAtom.debugLabel = 'curSessionMsgIdsAtom';

export const getCurSessionMsgIdsAtom = () => {
    return triageStreamAtomStore.get(curSessionMsgIdsAtom);
};

/**
 * 重置TriageStreamMsgAtom
 *
 * @param id TriageStreamMsgAtomSymbolType类型，标识需要重置的TriageStreamMsgAtom
 * @returns 重置后的TriageStreamMsgAtom实例
 */
export const resetTriageStreamMsgAtom = (id: TriageStreamMsgAtomSymbolType) => {
    const atomInstance = triageStreamMsgAtomFamily(id);
    triageStreamAtomStore.set(atomInstance, {
        type: 'reset',
        payload: null
    });

    return atomInstance;
};

// AI 诊前页面消息卡片 AtomFamily
export const triageStreamMsgAtomFamily = atomFamily((_id: TriageStreamMsgAtomSymbolType) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const atomInstance = atomWithReducer<MsgItemType<any> | undefined, MsgAtomEventAction>(
        // atom 初始状态
        undefined,
        // 对应 atom 的 Reducer 处理函数
        (state, action) => {
            if (!action) return state;

            switch (action.type) {
                case 'update': {
                    return action.payload;
                }
                // Tips: 更新消息数据的 data 字段，而非整个 data 对象；@wanghaoyu08
                case 'updateDataOfData': {
                    if (!state) return state;

                    return {
                        type: state?.type,
                        meta: state?.meta,
                        data: action.payload
                    };
                }
                // Tips: 处理流式消息动态追加内容逻辑；@wanghaoyu08
                case 'append':
                case 'end':
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-error
                    return state ? triageStreamMsgAtomReducer(state, action) : action.payload;
                case 'endManually':
                    return state ? triageStreamMsgAtomReducer(state, action) : undefined;
                case 'reset':
                    return undefined;
                default:
                    return state;
            }
        }
    );
    atomInstance.debugLabel = `triageStreamMsgAtomFamily_${_id}`;

    return atomInstance;
});

/**
 * 创建用于处理分流消息的状态管理原子
 *
 * @param id 分流消息原子符号类型
 * @returns 返回一个处理分流消息的状态管理原子
 */
export const createTriageStreamMsgAtom = (
    id: TriageStreamMsgAtomSymbolType,
    data: MsgItemType<unknown>
) => {
    // Tips: 暂不考虑 atom 初始化时是否存在已有 atom 的情况，即便出现该情况也不会重复创建；
    const atomInstance = triageStreamMsgAtomFamily(id);

    triageStreamAtomStore.set(atomInstance, {
        type: 'append',
        payload: data
    });

    return atomInstance;
};

/**
 * 结束分流消息原子函数
 *
 * @param id 分流消息原子符号类型
 * @returns 分流消息原子实例
 */
export const endTriageStreamMsgAtom = (id: TriageStreamMsgAtomSymbolType) => {
    const atomInstance = triageStreamMsgAtomFamily(id);

    triageStreamAtomStore.set(atomInstance, {
        type: 'endManually',
        payload: null
    });

    return atomInstance;
};

export const updateTriageStreamMsgAtom = (
    id: TriageStreamMsgAtomSymbolType,
    data: MsgItemType<unknown>,
    ops?: {
        _debugSymbol?: string;
        actionType?: 'update' | 'append' | 'end';
    }
) => {
    const atomInstance = triageStreamMsgAtomFamily(id);

    const action = ops?.actionType || data?.data?.action;

    triageStreamAtomStore.set(atomInstance, {
        type: action,
        payload: data
    });

    return atomInstance;
};

/**
 * 更新当前会话消息ID的Atom
 *
 * @param ids 需要更新的消息ID数组
 * @param ops 包含操作类型的对象，类型为 CurSessionMsgIdsReducerActionType
 */
export const updateCurSessionMsgIdsAtom = (
    ids: MsgId[],
    ops: {
        type: CurSessionMsgIdsReducerActionType;
        targetId?: MsgId;
    }
) => {
    triageStreamAtomStore.set(curSessionMsgIdsAtom, {
        type: ops.type,
        payload: ids,
        targetId: ops.targetId
    });
};

/**
 * 删除指定消息ID之后的所有消息
 *
 * @param targetId 目标消息ID，删除此消息之后的所有消息
 */
export const deleteMsgIdsAfter = (targetId: MsgId) => {
    triageStreamAtomStore.set(curSessionMsgIdsAtom, {
        type: 'deleteAfter',
        payload: [],
        targetId
    });
};

/**
 * 更新分流消息原子中指定 key 的值
 *
 * @param id 分流消息原子符号类型
 * @param key 需要更新的键
 * @param value 更新的值
 * @returns 分流消息原子实例
 */
export const updateTriageStreamMsgDataByKey = <T>(
    id: TriageStreamMsgAtomSymbolType,
    key: string,
    value: T
) => {
    const atomInstance = triageStreamMsgAtomFamily(id);
    const currentState = triageStreamAtomStore.get(atomInstance);

    if (!currentState) return atomInstance;

    const updatedData = {
        ...currentState,
        data: {
            ...currentState.data,
            [key]: value
        }
    };

    triageStreamAtomStore.set(atomInstance, {
        type: 'append',
        payload: updatedData
    });

    return atomInstance;
};

/**
 * 获取分流消息原子中指定 key 的值
 *
 * @param id 分流消息原子符号类型
 * @returns 分流消息原子数据
 */

export const getTriageStreamMsgDataByKey = (id: TriageStreamMsgAtomSymbolType) => {
    const atomInstance = triageStreamMsgAtomFamily(id);
    const currentState = triageStreamAtomStore.get(atomInstance);

    return currentState;
};

/**
 * 用于存储会话历史消息状态的Atom
 *
 * @param id 会话ID
 * @returns 返回存储会话历史消息状态的Atom
 */
export const triageSessionHasHistoryMsgAtom = atomFamily((id: SessionId) => {
    const sessionHistoryAtom = atom<boolean>(false);
    sessionHistoryAtom.debugLabel = `triageSessionHasHistoryMsgAtom_${id}`;
    return sessionHistoryAtom;
});

export const triageSessionResendMsgArgAtom = atomFamily((id: MsgId) => {
    const resendMsgArgAtom = atom<CreateConversationArgs>();
    resendMsgArgAtom.debugLabel = `triageSessionResendMsgArgAtom_${id}`;
    return resendMsgArgAtom;
});

/**
 * 创建用于存储会话重发消息参数的Atom
 *
 * @param id 消息ID
 * @param data 会话重发消息参数
 * @returns 返回一个用于存储会话重发消息参数的Atom
 */
export const createResendMsgArgAtom = (id: MsgId, data: CreateConversationArgs) => {
    // Tips: 暂不考虑 atom 初始化时是否存在已有 atom 的情况，即便出现该情况也不会重复创建；
    const atomInstance = triageSessionResendMsgArgAtom(id);

    triageStreamAtomStore.set(atomInstance, data);

    return atomInstance;
};

/**
 * 更新会话历史消息状态的Atom
 *
 * @param id 会话ID
 * @param hasHistoryMsg 是否存在历史消息
 */
export const updateTriageSessionHasHistoryMsgAtom = (id: SessionId, hasHistoryMsg: boolean) => {
    const atomInstance = triageSessionHasHistoryMsgAtom(id);
    triageStreamAtomStore.set(atomInstance, hasHistoryMsg);
};

export const waitingRenderingMsgIdAtom = atom<MsgId[]>([]);
waitingRenderingMsgIdAtom.debugLabel = 'waitingRenderingMsgIdAtom';

export const updateWaitingRenderingMsgIdAtom = (msgIds: MsgId[]) => {
    return triageStreamAtomStore.set(waitingRenderingMsgIdAtom, msgIds);
};

export const getWaitingRenderingMsgIdAtom = () => {
    return triageStreamAtomStore.get(waitingRenderingMsgIdAtom);
};

export const clearWaitingRenderingMsgIdAtom = () => {
    return triageStreamAtomStore.set(waitingRenderingMsgIdAtom, []);
};

/**
 * 消息风控命中信息；
 * @description 消息风控命中信息；
 * @returns
 */
export const msgRiskControlAtom =
    atom<NonNullable<NonNullable<SSEResponseType['data']>['ctrlData']>['riskControl']>();
msgRiskControlAtom.debugLabel = 'msgRiskControlAtom';

/**
 * 更新消息风控命中信息；
 * @description 消息风控命中信息；
 * @returns
 */
export const updateMsgRiskControlAtom = (
    data: NonNullable<NonNullable<SSEResponseType['data']>['ctrlData']>['riskControl']
) => {
    triageStreamAtomStore.set(msgRiskControlAtom, data);
};

export const getMsgRiskControlAtom = () => {
    return triageStreamAtomStore.get(msgRiskControlAtom);
};
