import {atom} from 'jotai';

import type {MsgId, IUpdataAjectiveMsgParams} from '../../typings';
import {triageStreamAtomStore} from './index';

// 有效的定向 sku 卡片 MsgId；无效需置灰展示；
export const adjectiveDirectedSkuMsgIdAtom = atom<MsgId>('');
adjectiveDirectedSkuMsgIdAtom.debugLabel = 'adjectiveDirectedSkuMsgIdAtom';

/**
 * 更新有效定向 SKU 消息 ID
 *
 * @param msgId - 消息 ID
 * @returns 返回更新后的有效定向 SKU 消息 ID
 */
export const updateAdjectiveDirectedSkuMsgIdAtom = (msgId: MsgId) => {
    return triageStreamAtomStore.set(adjectiveDirectedSkuMsgIdAtom, msgId);
};

export const adjectiveRecommendExpertMsgIdAtom = atom<MsgId[]>([]);
adjectiveRecommendExpertMsgIdAtom.debugLabel = 'adjectiveRecommendExpertMsgIdAtom';

/**
 * 更新 AI 推荐专家消息 ID
 *
 * @param msgId - 消息 ID
 * @returns 返回更新后的 AI 推荐专家消息 ID
 */
export const updateImAjectiveRecommendExpertMsgIdAtomAtom = ({
    msgId,
    type = 'push'
}: IUpdataAjectiveMsgParams) => {
    const arr = [...(triageStreamAtomStore.get(adjectiveRecommendExpertMsgIdAtom) || [])];
    if (type === 'unshift') {
        arr?.unshift(msgId);
    } else {
        arr?.push(msgId);
    }

    return triageStreamAtomStore.set(adjectiveRecommendExpertMsgIdAtom, arr);
};

export const guahaoRecommendExpertMsgIdAtom = atom<MsgId[]>([]);
guahaoRecommendExpertMsgIdAtom.debugLabel = 'guahaoRecommendExpertMsgIdAtom';

/**
 * 更新 挂号 推荐专家消息 ID
 *
 * @param msgId - 消息 ID
 * @returns 返回更新后的 挂号 推荐专家消息 ID
 */
export const updateImGuahaoRecommendExpertMsgIdAtomAtom = ({
    msgId,
    type = 'push'
}: IUpdataAjectiveMsgParams) => {
    const arr = [...(triageStreamAtomStore.get(guahaoRecommendExpertMsgIdAtom) || [])];
    if (type === 'unshift') {
        arr?.unshift(msgId);
    } else {
        arr?.push(msgId);
    }

    return triageStreamAtomStore.set(guahaoRecommendExpertMsgIdAtom, arr);
};

/**
 * 存储所有
 */
export const adjectiveRecommendUnDirectMsgIdArrAtom = atom<MsgId[]>([]);
adjectiveRecommendUnDirectMsgIdArrAtom.debugLabel = 'adjectiveRecommendUnDirectMsgIdArrAtom';

/**
 * 更新 AI 推荐未定向消息 ID
 */
export const adjectiveRecommendUnDirectMsgIdAtom = atom<MsgId[]>([]);
adjectiveRecommendUnDirectMsgIdAtom.debugLabel = 'adjectiveRecommendUnDirectMsgIdAtom';

/**
 * 更新 AI 推荐未定向消息 ID
 */
export const updateImAjectiveRecommendUnDirectMsgIdAtomAtom = ({
    msgId,
    type = 'push'
}: IUpdataAjectiveMsgParams) => {
    const arr = [...(triageStreamAtomStore.get(adjectiveRecommendUnDirectMsgIdAtom) || [])];
    if (type === 'unshift') {
        arr?.unshift(msgId);
    } else {
        arr?.push(msgId);
    }

    return triageStreamAtomStore.set(adjectiveRecommendUnDirectMsgIdAtom, arr);
};

/**
 * 获取特定消息类型的消息 ID
 *
 * @returns 返回特定消息类型的消息 ID
 */
export const getSpecialCardAdjectiveMsgIds = () => {
    return [
        triageStreamAtomStore.get(adjectiveDirectedSkuMsgIdAtom),
        triageStreamAtomStore.get(adjectiveRecommendExpertMsgIdAtom),
        triageStreamAtomStore.get(guahaoRecommendExpertMsgIdAtom),
        triageStreamAtomStore.get(adjectiveRecommendUnDirectMsgIdAtom)
    ];
};

/**
 * 获取有效定向 SKU 消息 ID
 *
 * @returns 返回有效定向 SKU 消息 ID
 */
export const getAdjectiveDirectedSkuMsgId = () => {
    return triageStreamAtomStore.get(adjectiveDirectedSkuMsgIdAtom);
};

/**
 * 获取 AI 推荐专家消息 ID
 *
 * @returns 返回 AI 推荐专家消息 ID
 */
export const getAdjectiveRecommendExpertMsgId = () => {
    return triageStreamAtomStore.get(adjectiveRecommendExpertMsgIdAtom);
};

/**
 * 获取 挂号 推荐专家消息 ID
 *
 * @returns 返回 挂号 推荐专家消息 ID
 */
export const getGuahaoRecommendExpertMsgId = () => {
    return triageStreamAtomStore.get(guahaoRecommendExpertMsgIdAtom);
};

/**
 * 获取 AI 推荐未定向消息 ID
 *
 * @returns 返回 AI 推荐未定向消息 ID
 */
export const getAdjectiveRecommendUnDirectMsgId = () => {
    return triageStreamAtomStore.get(adjectiveRecommendUnDirectMsgIdAtom);
};

/**
 * 更新欢迎模块展示状态
 */
export const imWelcomeMouleDisplayStatusAtom = atom<boolean>(false);
imWelcomeMouleDisplayStatusAtom.debugLabel = 'imWelcomeMouleDisplayStatusAtom';

export const updateImWelcomeMouleDisplayStatus = (status: boolean) => {
    triageStreamAtomStore.set(imWelcomeMouleDisplayStatusAtom, status);
};

export const imWelcomeMouleMsgIdsAtom = atom<MsgId[]>([]);
imWelcomeMouleMsgIdsAtom.debugLabel = 'imWelcomeMouleMsgIdsAtom';

export const updateImWelcomeMouleMsgIdsAtom = (msgId: MsgId, type: 'unshift' | 'push') => {
    const arr = [...(triageStreamAtomStore.get(imWelcomeMouleMsgIdsAtom) || [])];
    if (type === 'unshift') {
        arr?.unshift(msgId);
    } else {
        arr?.push(msgId);
    }

    return triageStreamAtomStore.set(imWelcomeMouleMsgIdsAtom, arr);
};

export const getImWelcomeMouleMsgIdsAtom = () => {
    return triageStreamAtomStore.get(imWelcomeMouleMsgIdsAtom);
};
