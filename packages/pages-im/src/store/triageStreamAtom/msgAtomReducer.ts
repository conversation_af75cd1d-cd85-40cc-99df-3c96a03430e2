import type {ImFlowData} from '@baidu/vita-ui-cards-common/ImFlow';

import {MSG_CARDID_ENUM_STREAM, type MSG_CARDID_ENUM_STREAM_TYPE} from '../../constants/msg';
import {mergeContentList, mergeItemProperties} from '../../utils/mergeImFlowContnent';

import type {MsgItemType, MsgAtomEventAction} from './index.type';

/**
 * 处理消息流动态更新卡片数据 Reducer
 *
 * @param {MsgItemType} state 当前状态，类型为 MsgItemType
 * @param {MsgAtomEventAction} action 触发状态变更的动作，类型为 MsgAtomEventAction
 * @returns {MsgItemType} 返回新的状态，类型为 MsgItemType
 */
export const triageStreamMsgAtomReducer = (
    state: MsgItemType<ImFlowData>,
    action: MsgAtomEventAction
): MsgItemType<unknown> => {
    try {
        const {payload, type} = action;

        // Tips: 冗余判断，用于 ts 推断联合类型的类型值；@wanghaoyu08
        if (type === 'append' || type === 'end') {
            const {type: msgType, meta, data} = payload;
            if (msgType === 'dynamic' && state?.data) {
                switch (state.data?.content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE) {
                    case MSG_CARDID_ENUM_STREAM.ImFlow:
                        return {
                            meta,
                            type: msgType,
                            data: appendImFlowCardData(
                                state.data as MsgItemType<ImFlowData>['data'],
                                data as MsgItemType<ImFlowData>['data']
                            )
                        };
                    default:
                        return state;
                }
            } else if (payload) {
                return payload;
            }
        }

        if (type === 'endManually' && state?.data && state.data.action !== 'end') {
            switch (state.data?.content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE) {
                case MSG_CARDID_ENUM_STREAM.ImFlow:
                    return {
                        ...state,
                        data: endImFlowCardData(state.data as MsgItemType<ImFlowData>['data'])
                    };
                default:
                    return state;
            }
        }

        return state;
    } catch (err) {
        console.error('triageStreamMsgAtomReducer 出错：', err);

        return state;
    }
};

/**
 * 针对 ImFlow 卡片，将新数据追加到当前消息数据中
 *
 * @param {MsgItemType['data']} original 当前消息数据
 * @param {MsgItemType['data']} appendData 要追加的新数据
 * @returns 合并后的消息数据
 */
const appendImFlowCardData = (
    original: MsgItemType<ImFlowData>['data'],
    appendData: MsgItemType<ImFlowData>['data']
): MsgItemType<ImFlowData>['data'] => {
    const isAppendHasList = !!appendData.content?.data?.content?.list;
    const mergedList = [...(original.content.data.content?.list || [])];

    if (isAppendHasList) {
        const appendList = appendData.content.data.content?.list || [];

        appendList.forEach(appendItem => {
            const index = mergedList.findIndex(item => item.sectionId === appendItem.sectionId);

            if (index !== -1) {
                mergedList[index] = mergeItemProperties(mergedList[index], appendItem);

                if (appendItem.type === 'markdownList' && appendItem.contentList) {
                    mergedList[index].contentList = mergeContentList(
                        mergedList[index].contentList,
                        appendItem.contentList
                    );
                }

                //todo 暂时保留，后边去掉
                // const originContent = mergedList[index].content;
                // const mergedContent = `${originContent || ''}${appendItem.content || ''}`;
                // mergedList[index].content = mergedContent;
                // appendItem.plan && (mergedList[index].plan = appendItem.plan);
                // appendItem.markdownBtn && (mergedList[index].markdownBtn = appendItem.markdownBtn);
                // appendItem.media && (mergedList[index].media = appendItem.media);
                // appendItem.tabList && (mergedList[index].tabList = appendItem.tabList);
                // appendItem.planIcon && (mergedList[index].planIcon = appendItem.planIcon);
                // appendItem.planProgressDesc &&
                //     (mergedList[index].planProgressDesc = appendItem.planProgressDesc);
            } else {
                // Tips：前端处理当出现新的 section 时，上一个 section 应该设置为已结束状态；@wanghaoyu08
                if (mergedList.length - 1 >= 0) {
                    mergedList[mergedList.length - 1].isFinish = true;
                }

                mergedList.push(appendItem);
            }
        });
    }

    const mergedQuickReply = [
        ...(original.content.data?.content?.quickReply || []),
        ...(appendData.content.data?.content?.quickReply || [])
    ];

    const mergedSourceInfo =
        appendData.content.data?.content?.sourceInfo || original.content.data?.content?.sourceInfo;

    const mergedContent = {
        list: mergedList,
        quickReply: mergedQuickReply,
        sourceInfo: mergedSourceInfo
    };

    let mergedFeature: MsgItemType<ImFlowData>['data']['feature'] | undefined;
    if (original.feature || appendData.feature) {
        mergedFeature = {
            ...(original.feature || {}),
            ...(appendData.feature || {})
        } as MsgItemType<ImFlowData>['data']['feature'];
    }

    const mergedData = {
        ...appendData,
        content: {
            ...appendData.content,
            data: {
                ...appendData.content.data,
                content: mergedContent
            }
        },
        ...(mergedFeature ? {feature: mergedFeature} : {})
    };

    return mergedData;
};

/**
 * 结束IM流程卡片数据函数
 *
 * @param original 原始消息项
 * @returns 处理后的消息项
 */
const endImFlowCardData = (
    original: MsgItemType<ImFlowData>['data']
): MsgItemType<ImFlowData>['data'] => {
    const list = original.content.data.content?.list || [];
    const mergedList = list.map(item => {
        return {
            ...item,
            isFinish: true
        };
    });

    const mergedData = {
        ...original,
        action: 'end' as MsgItemType<ImFlowData>['data']['action'],
        content: {
            ...original.content,
            data: {
                ...original.content.data,
                content: {
                    list: mergedList
                }
            }
        }
    };

    return mergedData;
};
