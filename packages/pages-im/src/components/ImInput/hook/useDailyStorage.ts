// Author: z<PERSON><PERSON>yu03
// Date: 2025-06-25 16:19:02
// Description: 自然日过期的本地存储 Hook
import {setStorageSync, getStorageSync, removeStorageSync} from '@tarojs/taro';

/**
 * 获取今天结束时间戳（毫秒）
 */
const getTodayEndTime = () => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999).getTime();
};

/**
 * 自然日过期的本地存储 Hook
 * @param key 存储键
 * @param initialValue 初始值
 */
const useDailyStorage = <T>(key: string, initialValue?: T) => {
    // 获取存储数据（带过期检查）
    const getStoredValue = () => {
        try {
            const stored = getStorageSync(key);
            if (!stored || Date.now() > stored.expireTime) {
                return initialValue; // 无数据或已过期，返回初始值
            }
            return stored.data;
        } catch (error) {
            return initialValue; // 出错时返回初始值
        }
    };

    // 设置存储值
    const setStoredValue = newValue => {
        try {
            // 保存到本地存储，附带过期时间
            setStorageSync(key, {
                data: newValue,
                expireTime: getTodayEndTime()
            });
        } catch (error) {
            console.error('保存失败:', error);
        }
    };

    // 删除存储
    const remove = () => {
        try {
            removeStorageSync(key);
        } catch (error) {
            console.error('删除失败:', error);
        }
    };

    return {
        getStoredValue,
        setValue: setStoredValue,
        remove
    };
};

export default useDailyStorage;
