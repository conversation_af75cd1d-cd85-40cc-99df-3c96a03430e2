import {type RefObject} from 'react';
export interface VoiceSwitch {
    handleChangeIcon: () => void;
    isShowVoice: boolean;
}

export interface VoiceModalRef {
    handleTouchEnd: (msg: unknown) => void;
    handleTouchStart: (msg: unknown) => void;
    handleTouchMove: (msg: unknown) => void;
}
export interface VoiceModalProps {
    open: boolean; // 语音弹层关闭
    handleClose: () => void;
    handleChangeIcon: () => void;
    ref: RefObject<VoiceModalRef>;
}

export interface VoiceComProps {
    handleChangeIcon: () => void;
}
