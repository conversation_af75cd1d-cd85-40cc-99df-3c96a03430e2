// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-21 11:13:20
// Description: 胶囊组件
import cx from 'classnames';
import {debounce} from 'lodash-es';
import {View, Text, Block} from '@tarojs/components';
import {memo, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {CLoginButton} from '@baidu/vita-ui-cards-common';
import {WImage} from '@baidu/wz-taro-tools-core';
import {WiseInquiry, WiseCalendarReserve, WiseOrder} from '@baidu/wz-taro-tools-icons';

import useDailyStorage from '../hook/useDailyStorage';
import {useCapsuleToolsController} from '../../../hooks/useCapsuleTools';
import {useHandleUserLoginBizAction} from '../../../hooks/triageStream/useHandleUserBizAction';
import {
    useGetCapsulesTools,
    useGetUserData,
    useSetImageSource
} from '../../../hooks/triageStream/pageDataController';
import {useGetSwanMsgListSceneStatus} from '../../../hooks/triageStream/useGetSwanMsgListSceneStatus';
import {useSetTipsData} from '../../../hooks/triageStream/useHandleTipsAction';

import type {CapsulesToolsType} from '../../../store/triageStreamAtom/index.type.ts';
import type {SceneTypeOfParams} from '../../../models/services/triageStream/sse/index.d';

import {navigate, type UrlParams} from '../../../utils/basicAbility/commonNavigate';
import {ubcCommonClkSend, ubcCommonViewSend} from '../../../utils/generalFunction/ubc';

import type {ActionInfo, Instruction} from '../../../typings';

// import MyOrder from './orderInfo';
import styles from './index.module.less';
import type {CapsuleToolsProps, BubbleInfo} from './index.d';

const CapsuleTools = memo((props: CapsuleToolsProps) => {
    const {openUploadPopup} = props;
    const hasRendered = useRef(false);
    // eslint-disable-next-line no-undef
    const timer = useRef<NodeJS.Timeout | null>(null);

    const {getStoredValue, setValue} = useDailyStorage('capsuleGuide', false);

    const [hideGuideCard, setHideGuideCard] = useState(getStoredValue());
    const [bubbleInfo, setBubbleInfo] = useState({} as BubbleInfo);

    const {userData} = useGetUserData();
    const {sessionCapsulesTools = []} = useGetCapsulesTools();
    const {handleLoginBizAction} = useHandleUserLoginBizAction();
    const {setImageSource} = useSetImageSource();
    const {setTipsData} = useSetTipsData();
    const {updateWzMockMessage, updateWzMessage} = useCapsuleToolsController();
    const {status} = useGetSwanMsgListSceneStatus();
    /**
     * @description 点击工具栏内容
     * @param actionInfo 跳转的链接
     */
    const handleClickItem = useCallback(
        (actionInfo: ActionInfo, type: CapsulesToolsType['type'], instruction?: Instruction) => {
            const {interaction, interactionInfo} = actionInfo;
            if (interaction === 'openLink') {
                navigate({
                    url: interactionInfo?.url,
                    params: interactionInfo?.params as UrlParams,
                    openType: 'navigate'
                });
            } else if (interaction === 'request') {
                updateWzMockMessage(interactionInfo);
            } else if (interaction === 'sendMsg') {
                // 新增发消息的逻辑
                updateWzMessage(interactionInfo, {
                    sceneType: interactionInfo?.sceneType as SceneTypeOfParams
                });
            } else if (interaction === 'login') {
                handleLoginBizAction();
            } else if (interaction === 'sendImg') {
                openUploadPopup(interactionInfo?.sceneType as SceneTypeOfParams);
                instruction && setTipsData(instruction as CapsulesToolsType['instruction']);
                setImageSource(`capsules_${type}`);
            }

            ubcCommonClkSend({
                value: `capsules_${type}`
            });
        },
        [
            handleLoginBizAction,
            openUploadPopup,
            setImageSource,
            setTipsData,
            updateWzMessage,
            updateWzMockMessage
        ]
    );

    // 渲染登录引导条
    const renderLogin = useCallback(
        (item, index) => {
            return (
                <CLoginButton
                    isLogin={userData?.isLogin}
                    closeShowNewUserTag={true}
                    useH5CodeLogin={true}
                    onLoginFail={error => {
                        console.error('error', error);
                    }}
                    onLoginSuccess={async () => {
                        await handleClickItem(item?.actionInfo, item?.type, item?.instruction);
                    }}
                    className={cx(styles.cLoginButton, 'wz-flex')}
                    key={index}
                >
                    <View
                        className={cx(
                            styles.loginItem,
                            'wz-mlr-51 wz-ptb-21 wz-plr-36 wz-fs-42 wz-flex wz-col-center wz-row-between'
                        )}
                    >
                        <View className={cx(styles.loginTips, 'wz-flex')}>
                            <View>登录AI健康管家，</View>
                            <View>获取更多服务</View>
                        </View>
                        <View className={cx(styles.loginBtn, ' wz-ptb-24 wz-plr-45')}>
                            {item?.btn?.value}
                        </View>
                    </View>
                </CLoginButton>
            );
        },
        [handleClickItem, userData?.isLogin]
    );

    // 胶囊工具根据是否是手百中心进入处理
    const transCapsuleClass = useMemo(() => {
        if (status) {
            return 'wz-ptb-21';
        }

        return 'wz-ptb-30';
    }, [status]);

    // 胶囊工具Icon根据是否是手百中心进入处理

    const transCapsuleIconClass = useMemo(() => {
        if (status) {
            return styles.swanMsgCapsuleIcon;
        }

        return styles.capsuleIcon;
    }, [status]);

    // 胶囊工具间距根据是否是手百中心进入处理
    const transCapsuleMarClass = useMemo(() => {
        if (status) {
            return 'wz-mr-36';
        }

        return 'wz-mr-24';
    }, [status]);

    // 胶囊工具文字根据是否是手百中心进入处理
    const transCapsuleTextClass = useMemo(() => {
        if (status) {
            return styles.swanMsgToolsText;
        }

        return styles.toolsText;
    }, [status]);

    const transCapsuleBrClass = useMemo(() => {
        if (status) {
            return styles.swanMsgBorderCapsule;
        }

        return styles.borderCapsule;
    }, [status]);

    const iconMap = useCallback(
        icon => {
            const iconMap = {
                'wise-inquiry': <WiseInquiry size={48} color='#00C8C8' />,
                'wise-calendarReserve': <WiseCalendarReserve size={48} color='#00B5F2' />,
                'wise-order': <WiseOrder size={48} color='#00c8c8' />
            };

            if (icon?.startsWith('https')) {
                return <WImage src={icon} className={transCapsuleIconClass} />;
            } else {
                return iconMap[icon];
            }
        },
        [transCapsuleIconClass]
    );

    const renderGuide = useCallback(() => {
        if (hideGuideCard) {
            return;
        }

        if (!bubbleInfo || !Object.keys(bubbleInfo)?.length) {
            return;
        }

        const {icon, showTime = 5000, text} = bubbleInfo;

        timer.current = setTimeout(() => {
            setHideGuideCard(true);
            setValue(true);
        }, showTime);

        return (
            <View
                className={cx(styles.capsuleGuide, 'wz-flex', 'wz-pr-45')}
                onClick={() => {
                    setHideGuideCard(true);
                    setValue(true);
                }}
            >
                <WImage src={icon} className={styles.capsuleGuideIcon} />
                <View className={styles.capsuleGuideText}>{text}</View>
            </View>
        );
    }, [bubbleInfo, hideGuideCard, setValue]);

    const genCon = useMemo(() => {
        if (!sessionCapsulesTools || !sessionCapsulesTools?.length) return null;

        if (!hasRendered.current) {
            hasRendered.current = true;

            sessionCapsulesTools.forEach(item => {
                ubcCommonViewSend({
                    value: `capsules_${item?.type}`
                });
            });
        }

        return sessionCapsulesTools?.map((item, index) => (
            <Block key={index}>
                {item?.type === 'login' ? (
                    renderLogin(item, index)
                ) : (
                    <View
                        className={cx(
                            styles.toolsItem,
                            transCapsuleMarClass,
                            transCapsuleBrClass,
                            'wz-flex',
                            'wz-mb-15',
                            'wz-plr-36',
                            // 'wz-ptb-36',
                            'c-click-status',
                            transCapsuleClass,
                            index === 0 ? 'wz-ml-36' : ''
                        )}
                        key={index}
                        onTouchMove={() => {
                            setHideGuideCard(true);
                            setValue(true);
                        }}
                        onClick={debounce(
                            () =>
                                handleClickItem(
                                    item?.actionInfo as ActionInfo,
                                    item?.type,
                                    item?.instruction
                                ),
                            500
                        )}
                    >
                        {iconMap(item.icon)}
                        <Text className={cx(transCapsuleTextClass, 'wz-ml-15')}>{item?.text}</Text>
                    </View>
                )}
            </Block>
        ));
    }, [
        handleClickItem,
        iconMap,
        renderLogin,
        setValue,
        sessionCapsulesTools,
        transCapsuleClass,
        transCapsuleTextClass,
        transCapsuleBrClass,
        transCapsuleMarClass
    ]);

    useEffect(() => {
        try {
            if (hasRendered.current) {
                const bubble = sessionCapsulesTools?.[0]?.bubbleInfo;
                if (bubble) {
                    setBubbleInfo(bubble);
                } else {
                    setHideGuideCard(true);
                }
            }
        } catch (error) {
            console.error(error, 'error');
        }
    }, [sessionCapsulesTools]);

    useEffect(() => {
        if (hideGuideCard) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-expect-error
            clearTimeout(timer.current);
        }
    }, [hideGuideCard]);

    return (
        <View className={cx(styles.capsuleToolsCom, 'wz-flex')}>
            {hideGuideCard ? <View className={styles.capsuleShadow} /> : ''}
            {renderGuide()}
            <View className={cx(styles.toolsItemList, 'wz-flex')}>{genCon}</View>
        </View>
    );
});

CapsuleTools.displayName = 'CapsuleTools';

export default CapsuleTools;
