// Author: z<PERSON><PERSON>yu03
// Date: 2025-06-17 21:26:26
// Description: 图片组件
import {useCallback} from 'react';
import {View} from '@tarojs/components';
import {previewImage} from '@tarojs/taro';
import {WImage, Loading} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';

import {getBdFileMapAtom} from '../../../../store/triageStreamAtom/bdFileMapAtom';

import styles from './index.module.less';

import type {ImageListProps} from './index.d';

const ImageListTools = (props: ImageListProps) => {
    const {imgList = [], statusList, setImgList, setStatusList} = props;
    // 预览图片
    const handleViewPic = useCallback(url => {
        if (process.env.TARO_ENV === 'swan') {
            const bdFileMap = getBdFileMapAtom();
            previewImage({
                current: bdFileMap[url],
                urls: [bdFileMap[url]]
            });
        } else {
            previewImage({
                current: url,
                urls: [url]
            });
        }
    }, []);

    const loadComponent = useCallback(
        item => {
            return (
                <View className={styles.loadingCom}>
                    <WImage
                        onClick={() => handleViewPic(item?.path)}
                        className={styles.loadingImg}
                        shape='rounded'
                        src={item?.path}
                    />
                    <View className={styles.coverView} />
                    <Loading className={styles.loadingIcon} color='#cdc6c1' />
                </View>
            );
        },
        [handleViewPic]
    );

    const successComponent = useCallback(
        item => {
            return (
                <WImage
                    onClick={() => handleViewPic(item?.path)}
                    className={styles.imageItem}
                    shape='rounded'
                    src={item?.path}
                />
            );
        },
        [handleViewPic]
    );

    const errorComponent = useCallback(item => {
        return (
            <View className={styles.errCom}>
                <WImage className={styles.errImg} shape='rounded' src={item?.path} />
                <View className={styles.coverView} />
                <View className={styles.errItext}>上传失败</View>
            </View>
        );
    }, []);

    const renderImg = useCallback(
        item => {
            const {uploadStatus} = item;

            switch (uploadStatus) {
                case 'pending':
                    return loadComponent(item);
                case 'success':
                    return successComponent(item);
                case 'failed':
                    return errorComponent(item);
                default:
                    return successComponent(item);
            }
        },
        [errorComponent, loadComponent, successComponent]
    );

    if (!imgList.length) return;

    return (
        <View className={cx(styles.imageMain, 'wz-flex', 'wz-mlr-36', 'wz-ptb-45', 'wz-plr-45')}>
            <View className={styles.capsuleShadow} />
            {imgList?.map((item, idx) => {
                return (
                    <View key={idx} className={styles.imageContainer}>
                        {renderImg(item)}
                        <WImage
                            src='https://med-fe.cdn.bcebos.com/vita/vita-close-icon.png'
                            className={styles.closeIcon}
                            onClick={() => {
                                setImgList(imgList?.filter(it => it?.path !== item?.path));
                                setStatusList(statusList?.filter((_it, s) => s !== idx));
                            }}
                        />
                    </View>
                );
            })}
        </View>
    );
};

ImageListTools.displayName = 'ImageListTools';

export default ImageListTools;
