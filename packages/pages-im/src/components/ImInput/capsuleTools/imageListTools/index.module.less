.imageMain {
    position: relative;
    background-color: #fff;
    border-top-right-radius: 63px;
    border-top-left-radius: 63px;
    border: 1px solid #e0e0e0;
    border-bottom: none;

    .capsuleShadow {
        position: absolute;
        width: 100%;
        height: 47px;
        background: linear-gradient(180deg, rgb(239 243 249 / 0%) 0%, #eff3f9 100%);
        z-index: 10;
        top: -47px;
        left: 0;
    }

    .imageContainer {
        position: relative;
        border: 0.5px solid #dcdde04d;
        width: 240px;
        height: 240px;
        border-radius: 36px;

        .imageItem {
            width: 100%;
            height: 100%;
            border-radius: 36px;
        }

        .loadingCom {
            position: relative;

            .coverView {
                width: 100%;
                height: 100%;
                position: absolute;
                border-radius: 36px;
                left: 0;
                top: 0;
                background-color: rgb(0 0 0 / 50%);
            }

            .loadingIcon {
                position: absolute;
                left: 30%;
                top: 30%;
            }

            .loadingImg {
                width: 240px;
                height: 240px;
                border-radius: 36px;
            }
        }

        .errCom {
            position: relative;

            .coverView {
                width: 100%;
                height: 100%;
                position: absolute;
                border-radius: 36px;
                left: 0;
                top: 0;
                background-color: rgb(0 0 0 / 50%);
            }

            .errItext {
                position: absolute;
                font-size: 42px;
                color: #fff;
                left: 15%;
                top: 40%;
            }

            .errImg {
                width: 240px;
                height: 240px;
                border-radius: 36px;
            }
        }

        .closeIcon {
            position: absolute;
            width: 60px;
            height: 60px;
            top: 10px;
            right: 10px;
        }
    }
}
