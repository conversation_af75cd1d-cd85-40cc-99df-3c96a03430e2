.viewOrderInfo {
    height: 108px;
    background-color: inherit;
    border-radius: 45px;

    .icon {
        width: 60px;
        height: 60px;
        vertical-align: middle;
        background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiMyOrderIcon.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .iconRight {
        width: 42px;
        height: 42px;
        vertical-align: middle;
        background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vitaOrderSelectIcon.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-left: 6px;
    }

    .iconShow {
        transform: rotate(180deg);
    }

    .text {
        display: inline-block;
        vertical-align: middle;
        color: #272933;
    }

    .orderPopover {
        position: absolute;
        white-space: nowrap;
        top: 120px;
        right: 36px;
        background: white;
        border: 1px solid #ededf0;
        border-radius: 30px;
        box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
        z-index: 31;

        .arrow {
            position: absolute;
            top: -18px;
            right: 69px;
            width: 51px;
            height: 30px;
            background: url('https://med-fe.cdn.bcebos.com/wz/vitaOrderPopArrow.png') no-repeat;
            background-size: 100% 100%;
        }

        .content {
            overflow: hidden;
            border-radius: 30px;
        }

        .item {
            text-align: center;
            font-size: 45px;
            line-height: 1;
            color: #272933;
            background: white;
            padding: 45px 69px;
            position: relative;
            border-bottom: 1px solid #e0e0e0;
        }

        .noBorder {
            border: 0;
        }
    }
}
