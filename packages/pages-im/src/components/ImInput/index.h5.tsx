// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-12 14:58:50
// Description: 输入框h5
import cx from 'classnames';
import {nextTick, pxTransform} from '@tarojs/taro';
import {useAtomValue} from 'jotai';
import {Button, SafeArea, WImage} from '@baidu/wz-taro-tools-core';
import {View, Textarea} from '@tarojs/components';
import {
    memo,
    useCallback,
    useEffect,
    useMemo,
    useState,
    forwardRef,
    useImperativeHandle
} from 'react';

// import { useGetTextareaFocusType } from '@hooks/triageStream/dataController';
import {ubcCommonClkSend, ubcCommonViewSend} from '../../utils/generalFunction/ubc';
import {useConversationDataController} from '../../hooks/triageStream/useConversationDataController';
import {
    useGetImageSource,
    useGetInputData,
    useSetImageSource
} from '../../hooks/triageStream/pageDataController';
import {
    useGetTextareaPlaceholder,
    useSetTextareaPlaceholder
} from '../../hooks/triageStream/dataController';
import {showToast} from '../../utils/customShowToast';
import {useSetTipsData} from '../../hooks/triageStream/useHandleTipsAction';
import {useScrollControl} from '../../hooks/triageStream/useScrollControl';
import {useUpdateServiceDeclareAtomShow} from '../../store/triageStreamAtom/imTools';
import {isGeneratingAtom, setIsUserInterruptedAtom} from '../../store/triageStreamAtom';
import {isIos} from '../../utils/jumpWx';

import type {IPicProps} from '../../typings/upload';
import UploadImage from './uploadImgTools';
import BottomAiText from './bottomAiText';
import {useTextareaSendFocus} from './hook/useTextareaSendFocus';

import type {ImInputProps} from './index.d';
import styles from './index.module.less';

const ImInput = memo(
    forwardRef((props: ImInputProps, ref) => {
        const {
            maxLine = 4,
            // placeholder = '请输入想咨询的内容',
            isDisabled = false,
            // needHold = false,
            confirmHold = true,
            hasImg = false,
            isFocus = false,
            imgList = [],
            setImgList,
            statusList,
            maxLength = 500,
            onClickCallBack,
            openUploadPopup
        } = props;

        const [isAutoHeight, setIsAutoHeight] = useState<boolean>(true); // 是否支持自动增高
        const [lineCount, setLineCount] = useState<number>(1);
        const [lineHeight, setLineHeight] = useState<number>(66);
        const [val, setVal] = useState<string>('');
        const [isShowImageTools, setIsShowImageTools] = useState<boolean>(true);

        const isGenerating = useAtomValue(isGeneratingAtom);
        const {createConversation, cancelPreSSE} = useConversationDataController();

        const {inputData} = useGetInputData() || {};
        const {setTipsData} = useSetTipsData();
        const {imageSource} = useGetImageSource();
        const {setImageSource} = useSetImageSource();
        const {setServiceShow} = useUpdateServiceDeclareAtomShow();
        const {scrollToBottom} = useScrollControl();
        const {placeholder} = useGetTextareaPlaceholder();
        const {setPlaceholder} = useSetTextareaPlaceholder();
        const {handleFocusBlur} = useTextareaSendFocus();

        const {uploadImg, uploadImgInstruction} = inputData || {};

        /**
         * @description 输入框行数&是否自动增高
         */
        const handleChangeLine = useCallback(
            e => {
                setLineCount(e.detail.lineCount);
                setIsAutoHeight(!(e.detail.lineCount >= maxLine));
            },
            [maxLine]
        );

        const handleOpenTips = useCallback(() => {
            setTipsData(uploadImgInstruction);
            openUploadPopup && openUploadPopup('inputImg');
            ubcCommonClkSend({
                value: 'inputUpdateImg'
            });
        }, [setTipsData, uploadImgInstruction, openUploadPopup]);

        // 是否上传成功
        const isUploadSuccess = useMemo(() => {
            return !statusList?.find(item => item === 'pending' || item === 'failed');
        }, [statusList]);

        // 上传图片的状态
        const uploadStatus = useMemo(() => {
            if (statusList?.find(item => item === 'pending')) {
                return 'pending';
            } else if (statusList?.every(item => item === 'success')) {
                return 'success';
            } else if (statusList?.find(item => item === 'failed')) {
                return 'failed';
            }
        }, [statusList]);

        // 发送按钮是否禁用
        const sendDisabled = useMemo(() => {
            if (isDisabled) {
                return true;
            }
            if (hasImg) {
                return !isUploadSuccess;
            } else {
                return !val?.trim();
            }
        }, [isDisabled, hasImg, isUploadSuccess, val]);

        const showErrorToast = useCallback(() => {
            if (uploadStatus === 'pending') {
                showToast({
                    title: '图片正在上传中，请稍后尝试',
                    icon: 'none'
                });
            } else {
                showToast({
                    title: '图片上传失败，请删除后重新尝试',
                    icon: 'none'
                });
            }
            const isDisabled = sendDisabled ? 1 : 0;
            ubcCommonClkSend({
                value: 'imSendMsgBtn',
                ext: {
                    product_info: {
                        source: `${imageSource}_upload`,
                        isDisabled,
                        picUpdateStatus: uploadStatus
                    }
                }
            });
        }, [imageSource, sendDisabled, uploadStatus]);

        // 发送图片
        const handleSendImg = useCallback(
            (preData: IPicProps[]) => {
                try {
                    preData?.[0]?.path &&
                        createConversation({
                            msg: {
                                type: 'image',
                                content: preData?.[0]?.path,
                                origin: preData?.[0]?.path,
                                sceneType: 'inputImg',
                                preData
                            }
                        });
                } catch (error) {
                    console.error(error);
                }
            },
            [createConversation]
        );

        const handleSendImageText = useCallback(
            msgContent => {
                try {
                    createConversation({
                        msg: {
                            type: 'richText',
                            content: msgContent,
                            sceneType: 'inputImg'
                        }
                    });
                } catch (error) {
                    console.error(error);
                }
            },
            [createConversation]
        );

        const clearInputOrImg = useCallback(() => {
            const dom = document.querySelector(
                `.${styles.placeholderContain}`
            ) as HTMLTextAreaElement;
            dom.value = '';
            setVal('');
            setImgList([]);
            setImageSource('');
            // 设置行数为1
            setLineHeight(66);
        }, [setImageSource, setImgList]);

        const handleSend = useCallback(() => {
            const dom = document.querySelector(
                `.${styles.placeholderContain}`
            ) as HTMLTextAreaElement;
            if (!dom) return;

            const sendText = dom?.value?.trim();

            if (hasImg) {
                if (!sendText) {
                    clearInputOrImg();
                    handleSendImg(imgList);
                } else {
                    // TODO: 发送图片+文字消息
                    const msgContent = {
                        images: imgList,
                        text: sendText
                    };
                    clearInputOrImg();
                    handleSendImageText(msgContent);
                }
                // 埋点
                const isDisabled = sendDisabled ? 1 : 0;
                ubcCommonClkSend({
                    value: 'imSendMsgBtn',
                    ext: {
                        product_info: {
                            source: `${imageSource}_upload`,
                            isDisabled,
                            picUpdateStatus: uploadStatus
                        }
                    }
                });
            } else {
                if (!sendText) return;
                clearInputOrImg();
                // 执行发送逻辑
                createConversation({
                    msg: {
                        type: 'text',
                        content: sendText,
                        sceneType: 'wzBotConversation'
                    }
                });
            }
        }, [
            hasImg,
            sendDisabled,
            imageSource,
            uploadStatus,
            clearInputOrImg,
            handleSendImg,
            imgList,
            handleSendImageText,
            createConversation
        ]);

        const handleInput = useCallback(e => {
            setVal(e.detail.value || '');
        }, []);

        const handleStopSSE = useCallback(() => {
            cancelPreSSE('用户点击终止按钮，主动停止');
            setIsUserInterruptedAtom(true);
            ubcCommonClkSend({
                value: 'imStopBtn'
            });
        }, [cancelPreSSE]);

        const showImgTools = useMemo(() => {
            if (!uploadImg) {
                return false;
            }

            if (!isShowImageTools || val?.trim()?.length) {
                return false;
            }

            return true;
        }, [isShowImageTools, uploadImg, val]);

        /**
         * @description 渲染停止生成按钮
         */
        const stopBtn = useMemo(() => {
            return (
                <View className={cx(styles.stopGenerateImage, 'wz-flex')}>
                    <WImage
                        src='https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/stop-generating.png'
                        className={styles.stopGenerateIcon}
                        onClick={handleStopSSE}
                    />
                </View>
            );
        }, [handleStopSSE]);

        /**
         * @description 渲染发送按钮
         */
        const genBtn = useMemo(() => {
            if (hasImg && !isUploadSuccess) {
                return (
                    <Button
                        dataset-id='sendBtn'
                        id='sendBtn'
                        color='primary'
                        shape='round'
                        // disabled={sendDisabled}
                        className={cx(styles.sendBtn, styles.disableBtn, 'wz-flex', 'wz-fs-42')}
                        onClick={showErrorToast}
                    >
                        发送
                    </Button>
                );
            }

            return (
                <Button
                    dataset-id='sendBtn'
                    id='sendBtn'
                    color='primary'
                    shape='round'
                    disabled={sendDisabled}
                    className={cx(styles.sendBtn, 'wz-flex', 'wz-fs-42')}
                    onClick={handleSend}
                >
                    发送
                </Button>
            );
        }, [handleSend, hasImg, isUploadSuccess, sendDisabled, showErrorToast]);

        /**
         * @description 渲染操作按钮区域
         */
        const renderActionButtons = useMemo(() => {
            if (isGenerating && !val?.trim() && !hasImg) {
                return stopBtn;
            }

            if (isFocus || !!val?.trim() || hasImg) {
                return genBtn;
            }

            return (
                showImgTools &&
                !hasImg && (
                    <View className={cx('wz-mb-27', 'wz-ml-18')}>
                        <UploadImage openUploadPopup={handleOpenTips} />
                    </View>
                )
            );
        }, [handleOpenTips, genBtn, hasImg, isFocus, isGenerating, stopBtn, val, showImgTools]);

        /**
         * @description 渲染输入框
         */
        const genInput = useMemo(() => {
            return (
                <Textarea
                    // {...(needHold ? { focus: h5Focus } : {})}
                    className={cx(styles.textarea, 'wz-pl-48', 'wz-fs-48', 'wz-flex')}
                    placeholderClass={styles.placeholderContain}
                    maxlength={maxLength}
                    confirmHold={confirmHold}
                    autoHeight={isAutoHeight}
                    placeholder={placeholder}
                    onFocus={e => {
                        handleFocusBlur(e);
                        setIsShowImageTools(false);
                        // setIsInputFocus(true);
                        setServiceShow(false); // 隐藏服务声明
                    }}
                    onBlur={e => {
                        nextTick(() => {
                            handleFocusBlur(e);
                            setIsShowImageTools(true);
                            // setIsInputFocus(false);
                            setServiceShow(true); // 展示服务声明
                        });

                        if (!isIos()) {
                            // 部分安卓机型，失焦后因页面未滚动，导致无法收起键盘（华为P50、one+ 9R）
                            scrollToBottom('textarea-send-blur');
                        }
                    }}
                    onInput={handleInput}
                    onLineChange={handleChangeLine}
                    adjustPosition={false}
                    showConfirmBar={false}
                    nativeProps={{
                        className: styles.placeholderContain,
                        style: {
                            height: pxTransform(lineHeight),
                            resize: 'none'
                        }
                    }}
                />
            );
        }, [
            maxLength,
            confirmHold,
            isAutoHeight,
            lineHeight,
            placeholder,
            scrollToBottom,
            handleInput,
            handleChangeLine,
            handleFocusBlur,
            setServiceShow
        ]);

        useEffect(() => {
            if (lineCount === 1) {
                setLineHeight(66);
            } else if (lineCount >= +maxLine) {
                setLineHeight(+maxLine * 66);
            } else {
                setLineHeight(lineCount * 66);
            }
        }, [lineCount, maxLine]);

        useImperativeHandle(ref, () => ({
            getValue: () => {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-expect-error
                return document?.querySelector(`.${styles.placeholderContain}`)?.value || '';
            }
        }));

        useEffect(() => {
            if (isGenerating && !val?.trim() && !hasImg) {
                ubcCommonViewSend({
                    value: 'imStopBtn'
                });
            }
        }, [isGenerating, val, hasImg]);

        useEffect(() => {
            if (!isIos()) {
                const originalHeight =
                    document.documentElement.clientHeight || document.body.clientHeight;
                window.onresize = () => {
                    const resizeHeight =
                        document.documentElement.clientHeight || document.body.clientHeight;
                    if (resizeHeight < originalHeight) {
                        //当软键盘弹起，在此处操作
                    } else {
                        //当软键盘收起，在此处操作
                        // 此处为了解决安卓机型，在输入法上面点击收起输入框，底部留白未清除
                        scrollToBottom('textarea-onresize');
                    }
                };
            }
        }, [scrollToBottom]);

        useEffect(() => {
            if (imgList && imgList?.length) {
                isUploadSuccess && setPlaceholder('可以补充您的健康情况~');
            } else {
                setPlaceholder('有什么健康问题尽管问我');
            }
        }, [imgList, isUploadSuccess, setPlaceholder]);

        return (
            <View
                id='inputContainer'
                className={cx(styles.ImInputWebContainer)}
                onClick={onClickCallBack}
            >
                {/* 交互区 */}
                <View
                    className={cx(
                        styles.featureCom,
                        'wz-flex',
                        'wz-plr-36',
                        hasImg ? 'wz-pb-30' : 'wz-ptb-30'
                    )}
                >
                    <View
                        className={cx(
                            styles.imInputMain,
                            'wz-flex',
                            'wz-pr-30',
                            hasImg ? styles.hasImgBorder : ''
                        )}
                    >
                        {genInput}
                        {renderActionButtons}
                    </View>
                </View>
                {!isFocus && <BottomAiText />}
                {/* 底部安全区 */}
                <SafeArea position='bottom' style={{backgroundColor: '#fff'}} />
            </View>
        );
    })
);

ImInput.displayName = 'ImInput';

export default ImInput;
