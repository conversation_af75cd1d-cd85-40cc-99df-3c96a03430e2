/*
 * @Author: banxiaoke
 * @Description: 非定向服务列表头部组件
 */
import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import {memo, useState, useEffect, useCallback, useMemo} from 'react';

import type {TopArea} from '../../index.d';

import styles from './ServiceTitle.module.less';

interface ServiceTitleProps {
    subText: string | undefined;
    topArea: TopArea | undefined;
    titleImg?: string;
    displayType?: string;
    serviceTip?: string;
}
const fallback =
    'https://ss1.baidu.com/6ONXsjip0QIZ8tyhnq/it/u=343652663,2298673057&fm=179&app=35&f=PNG?w=144&h=144&s=C8011F7047775A21364C71D9030050A1';
const width = 20;
let timer;
const setTimeoutTime = 2500;

const ServiceTitle = (props: ServiceTitleProps) => {
    const {subText = '', topArea, titleImg, serviceTip} = props;
    const {
        doctorDisplayArea: {avatars = [], words, isCarousel}
    } = topArea || {doctorDisplayArea: {}};

    const [showTrans, setShowTrans] = useState(true);
    const [curIndex, setCurIndex] = useState(8);
    const [showList, setShowList] = useState<string[]>([]);

    const carouselLoop = useCallback(
        newVal => {
            timer = setTimeout(() => {
                const isBoundry = newVal === avatars.length;
                setCurIndex(isBoundry ? 0 : curIndex + 1);
                setShowTrans(!isBoundry);
            }, setTimeoutTime);
        },
        [curIndex, avatars.length]
    );

    useEffect(() => {
        // 清除不可点击卡片的轮播，对新卡进行轮播处理
        timer && clearTimeout(timer);
        isCarousel && carouselLoop(curIndex);

        return () => {
            timer && clearTimeout(timer);
        };
    }, [carouselLoop, curIndex, isCarousel]);

    useEffect(() => {
        if (avatars.length >= 5 && isCarousel) {
            const mergeList = avatars.concat(avatars.slice(0, 5));
            setShowList(mergeList);
        } else {
            avatars.length > 0 && setShowList(avatars.slice(0, 4));
        }
    }, [avatars, isCarousel]);

    const loopStyle = useMemo(() => {
        return {
            transform: `translateX(${-curIndex * width * (2 / 3)}px)`,
            transition: showTrans ? 'all .5s cubic-bezier(0.2, 0, 0.2, 1)' : 'none'
        };
    }, [curIndex, showTrans]);

    return (
        <>
            <View className={cx(styles.textColor)}>
                <View className={cx(styles.title, 'wz-flex wz-fs-42 wz-taro-ellipsis wz-mb-33')}>
                    {titleImg ? (
                        <WImage className={cx(styles.titleImg, 'wz-mr-27')} src={titleImg} />
                    ) : null}
                    <View className={styles.title}>
                        <Text className='wz-pr-18'>{subText}</Text>
                    </View>
                </View>
                {showList && showList.length > 0 && (
                    <View className={cx(styles.textColor, 'wz-fs-42 wz-flex wz-mb-18')}>
                        <View className={styles.rotation}>
                            <View
                                className={styles.rotationContainer}
                                style={isCarousel ? loopStyle : {}}
                            >
                                {showList &&
                                    showList.length > 0 &&
                                    showList.map((item, index) => {
                                        return (
                                            <View
                                                key={index}
                                                className={styles.expertAvatar}
                                                style={
                                                    isCarousel
                                                        ? {
                                                            opacity:
                                                                  index >= curIndex &&
                                                                  index - curIndex < 5
                                                                      ? 1
                                                                      : 0,
                                                            transition: showTrans
                                                                ? 'all .5s cubic-bezier(0.2, 0, 0.2, 1)'
                                                                : 'none',
                                                            width: `${width}px`,
                                                            display:
                                                                  index >= curIndex &&
                                                                  index - curIndex === 4
                                                                      ? 'none'
                                                                      : 'block',
                                                            marginLeft: `${-width * (1 / 3)}px`
                                                        }
                                                        : {
                                                            width: `${width}px`,
                                                            marginLeft: `${-width * (1 / 3)}px`,
                                                            display: 'block'
                                                        }
                                                }
                                            >
                                                <WImage
                                                    key={index}
                                                    round
                                                    className={styles.expertAvatar}
                                                    src={item || fallback}
                                                    fallback={<WImage src={fallback} />}
                                                />
                                            </View>
                                        );
                                    })}
                            </View>
                        </View>
                        <Text className={cx('wz-ml-21', styles.onlineDoc)}>{words}</Text>
                    </View>
                )}

                {serviceTip && <View className='wz-pb-18 wz-fs-42 wz-pt-21'>{serviceTip}</View>}
            </View>
        </>
    );
};
export default memo(ServiceTitle);
