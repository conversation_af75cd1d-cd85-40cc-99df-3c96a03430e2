import {ImUndirectServicetCon, undirectService} from '../index.d';
import {MsgId} from '../../../typings';

export interface UndirectServiceDataV2Props {
    skuData: ImUndirectServicetCon;
    updateCollectedInfoAndSku: (type: string, selectSkuData?: undirectService) => void;
    lineText?: string;
    skuDisabledToastText?: string;
    isLogin?: boolean;
    updateSkuDetailData: (selectSkuData: undirectService) => void;
    isSkuDetailPopupShow: boolean;
    setIsSkuDetailPopupShow: (value: boolean) => void;
    isSkuDisabled?: boolean;
    hideTopSkuArea?: boolean;
    adjectiveRecommendUnDirectMsgId?: MsgId | undefined;
}
