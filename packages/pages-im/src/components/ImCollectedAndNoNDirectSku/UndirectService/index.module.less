@import '../../../style/variable.less';

.undirectService {
    width: 100%;
    background: #fff;
    padding: 48px 45px 0;
    border-radius: 45px;
    overflow: hidden;
    box-sizing: border-box;
    position: relative;

    .isDisabledContainer {
        position: absolute;
        background: #fff;
        inset: 0;
        opacity: 0.4;
        z-index: @zIndex-doctorMask;

        .isDisabledIcon {
            position: absolute;
            right: 0;
        }

        .ineffectiveIcon {
            width: 198px;
            height: 156px;
            position: absolute;
            top: 0;
            right: 0;
        }
    }

    .horizontal-line-container {
        width: 100%;
        position: relative;
        display: block;
    }

    .horizontal-line {
        position: absolute;
        left: 0;
        right: 0;
        top: 36px;
        height: 0.33Px;
        background: #e6e6e6;
    }

    .horizontal-line-tip {
        position: relative;
        background: #fff;
        z-index: 2;
    }

    .titleWrapper {
        flex-direction: column;

        .mainTextWrapper {
            padding-top: 45px;
            color: #525252;
            letter-spacing: 0;
            text-align: center;
            line-height: 48px;

            .line {
                border: 1px #f1f1f1 solid;
                height: 0;
                width: 160px;
            }

            .point {
                width: 9px;
                height: 9px;
                background: #f1f1f1;
                border-radius: 4.5px;
            }
        }

        .subTextWrapper {
            color: #858585;
            line-height: 42px;
        }
    }

    .skuWrapper {
        background-color: #fff;

        .skuItem {
            box-sizing: border-box;
            position: relative;
            padding: 53px 0;
            align-items: center;

            .left {
                flex: 1;

                .consult {
                    color: #1f1f1f;
                    line-height: 57px;

                    .tag {
                        padding: 0 15px;
                        padding-top: 3px;
                        color: #fd503e;
                        height: 41px;
                        line-height: 41px;
                        background: rgb(253 80 62 / 10%);
                        border: 2px solid rgb(253 80 62 / 50%);
                        border-radius: 24px;
                        font-weight: 400;
                        vertical-align: middle;
                    }
                }

                .tagWrapper {
                    font-weight: 700;

                    .tagTxt {
                        margin-top: 2px;
                        line-height: 33px;
                    }
                }

                .skus {
                    color: #858585;
                    line-height: 42px;
                }

                .reasons {
                    color: #b86a37;
                    margin-bottom: 10px;
                    font-weight: 700;

                    .reasonsText {
                        margin: 0 3px;
                    }

                    .reasonsIcon {
                        display: inline-block;
                        width: 22px;
                        height: 36px;
                        flex-shrink: 0;
                    }
                }

                .goodAts {
                    margin-bottom: 10px;
                    color: #858585;
                    font-weight: 700;

                    .colon {
                        color: #00bdbd;
                    }

                    .goodAtIcon {
                        width: 72px;
                        height: 33px;
                        flex-shrink: 0;
                    }

                    .goodAtHighLightLine {
                        position: relative;

                        &::after {
                            content: '';
                            position: absolute;
                            display: inline-block;
                            height: 15px;
                            width: 100%;
                            background: rgb(0 200 200 / 15%);
                            bottom: 5px;
                            left: 0;
                        }
                    }
                }
            }

            .center {
                flex: 1;
                height: 145px;
            }

            .right {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                flex-shrink: 0;

                .priceArea {
                    align-items: baseline;

                    .originPrice {
                        color: #b8b8b8;
                        line-height: 42px;
                        text-decoration: line-through;
                    }

                    .curPrice {
                        color: #fd503e;
                        text-align: right;
                        line-height: 48px;
                        font-weight: 600;
                    }
                }

                .toConsult {
                    width: 216px;
                    height: 84px;
                    background: #00c8c8;
                    border-radius: 48px;
                    color: #fff;
                    letter-spacing: 0;
                }
            }

            &::before {
                content: '';
                position: absolute;
                top: 0;
                width: 1050px;
                left: 0;
                height: 0.33Px;
                background-color: #f1f1f1;
            }

            &:nth-of-type(1) {
                &::before {
                    content: '';
                    display: none;
                }
            }
        }
    }

    .popTitle {
        border-bottom: none;
    }
}

.line1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.btn {
    background: linear-gradient(271.36deg, #00D3EA 0%, #00CFA3 100%);
}