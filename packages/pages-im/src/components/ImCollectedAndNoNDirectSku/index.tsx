import {View} from '@tarojs/components';
import {showLoading, hideLoading} from '@tarojs/taro';
import {memo, type FC, useCallback, useState, useReducer, useRef, useEffect} from 'react';
import {Portal} from '@baidu/vita-ui-cards-common';
import {Dialog, Button} from '@baidu/wz-taro-tools-core';

import {
    useGetUserData,
    useGetSessionId,
    useUpdateUserData,
    useGetAdjectiveRecommendUnDirectMsgId,
    useGetAdjectiveRecommendExpertMsgId
} from '../../hooks/triageStream/pageDataController';
import {useMsgDataGetController} from '../../hooks/triageStream/dataController';
import {getUserBizActionReq} from '../../models/services/triageStream';
import type {userInfoItem} from '../../models/services/triageStream/index.d';
import {showToast} from '../../utils/customShowToast';
import {updateTriageStreamMsgAtom} from '../../store/triageStreamAtom/msg';
import {MsgItemType} from '../../store/triageStreamAtom/index.type';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../utils/generalFunction/ubc';
import {useConversationDataController} from '../../hooks/triageStream/useConversationDataController';
import {useScrollControl} from '../../hooks/triageStream/useScrollControl';

import {ServiceTypeMap} from '../../constants/common';

import PatientCard from './PatientCard';
import ConsultForm from './ConsultForm';
import {formReducer} from './ConsultForm/form-reducer';
import {useGetMakeOrderRelatedInfo} from './hooks/useGetMakeOrderRelatedInfo';

import UndirectService from './UndirectService';
import type {PatientInfo} from './ConsultForm/index.d';
import type {
    ImCollectedAndNoNDirectSkuProps,
    ImNoNDirectSkuPData,
    OpenPatientType,
    undirectService
} from './index.d';
/**
 *
 * @description AI智能体定向服务卡
 * @returns
 */
const ImCollectedAndNoNDirectSku: FC<ImCollectedAndNoNDirectSkuProps> = props => {
    const {content, ext = {}} = props?.data || {};
    const {scene = ''} = ext || {};
    const {collectedInfo, skuData, requireDesc, lineText, isExpired, hideTopSkuArea} = content;
    const {qid} = collectedInfo || '';
    const openPatientPopTypeRef = useRef<boolean>(false); // 存储点开来源类型，用于判断是否发生了变化
    // eslint-disable-next-line max-len
    const [showConsultForm, setShowConsultForm] = useState(false); // 是否展示编辑态表单弹层
    const [showConsultList, setShowConsultList] = useState(false); // 是否展示选择就诊人列表弹层
    const [openPatientPopType, setOpenPatientPopType] = useState<OpenPatientType>('patient'); // 点开打开弹窗的来源类型
    const [selectedSkuItemData, setSelectedSkuItemData] = useState<undirectService>(
        (skuData?.list || [])[0]
    );

    const [isSkuDisabled, setIsSkuDisabled] = useState(false);
    const [updateCollectedInfoAndSkuType, setUpdateCollectedInfoAndExpert] =
        useState<OpenPatientType>('patient');
    const [selectPatientData, setSelectPatientData] = useState<PatientInfo | undefined>(
        collectedInfo?.curPatient
    );

    const {userData} = useGetUserData();

    const prevIsLoginRef = useRef<boolean>(userData?.isLogin || false); // 存储之前的值，用于判断是否发生了变化

    const sessionId = useGetSessionId();
    const {updateUserData} = useUpdateUserData();
    const {adjectiveRecommendUnDirectMsgId} = useGetAdjectiveRecommendUnDirectMsgId();
    const {adjectiveRecommendExpertMsgId} = useGetAdjectiveRecommendExpertMsgId();
    const {data: docServiceData} = useMsgDataGetController({msgId: adjectiveRecommendExpertMsgId});
    const {data: undirectServiceData} = useMsgDataGetController({
        msgId: adjectiveRecommendUnDirectMsgId
    });
    const [skuDisabledToastText, setSkuDisabledToastText] =
        useState('当前推荐已失效，请点击新的服务卡');

    const [isSkuDetailPopupShow, setIsSkuDetailPopupShow] = useState(false); // 是否展示 sku 详情弹窗
    const {updateSessionCapsulesTools} = useConversationDataController();

    const [isDialogOpen, setIsDialogOpen] = useState(false); // 就诊人科室性别冲突弹窗
    const [dialogConfig, setDialogConfig] = useState({
        content: '',
        confirmText: '确认',
        cancelText: '取消',
        onConfirm: () => {}
    });

    const {scrollToMessage} = useScrollControl();

    useEffect(() => {
        // isExpired接口端判断无效卡,直接禁用当前卡
        if (isExpired || adjectiveRecommendUnDirectMsgId !== props?.msgId) {
            setIsSkuDisabled(true);
        }
        isExpired && setSkuDisabledToastText('服务卡已失效，你可向健康管家重新咨询');
    }, [adjectiveRecommendUnDirectMsgId, isExpired, props?.msgId]);

    const [state, dispatch] = useReducer(formReducer, {
        ...collectedInfo?.curPatient,
        canSubmit: true,
        zhusu: collectedInfo?.clinicalDesc || '',
        images: collectedInfo?.images
    });

    // 登录 & 下单部分逻辑
    const {loginSuccessfulCallback} = useGetMakeOrderRelatedInfo({
        msgId: props?.msgId,
        skuList: skuData?.list,
        qid
    });

    /**
     *
     * @description 更新选中的 skuId，并触发相关计算
     */
    const updateSkuDetailData = useCallback((selectSkuData: undirectService) => {
        setSelectedSkuItemData(selectSkuData); // 更新选中的 sku 数据
    }, []);

    // 就诊人不存在时，直接展示表单编辑态
    const openEditPatientPop = useCallback(() => {
        setShowConsultList(false);
        setShowConsultForm(true);
        dispatch({
            payload: {
                ...collectedInfo,
                ...collectedInfo?.curPatient,
                zhusu: collectedInfo?.clinicalDesc,
                images: collectedInfo?.images
            }
        });
    }, [collectedInfo]);

    // 就诊人存在时，打开就诊人选择弹层
    const openEditPatient = useCallback(() => {
        if (collectedInfo?.curPatient?.contactId) {
            setShowConsultList(true);
            setSelectPatientData(
                collectedInfo?.curPatient?.contactId ? collectedInfo?.curPatient : undefined
            );
            dispatch({
                payload: {
                    ...collectedInfo?.curPatient,
                    zhusu: collectedInfo?.clinicalDesc || '',
                    images: collectedInfo?.images
                }
            });
        }
    }, [collectedInfo]);

    const handleAddPatient = useCallback(() => {
        setShowConsultForm(true);
    }, []);

    // 处理弹层关闭逻辑
    const handleCloseConsultForm = useCallback(
        (type: string) => {
            if (type === 'consult') {
                setShowConsultForm(false);
                dispatch({
                    payload: {
                        ...collectedInfo?.curPatient,
                        zhusu: state?.zhusu || '',
                        images: state?.images || []
                    }
                });
            }
            if (type === 'consultList') {
                setSelectPatientData(undefined);
                setShowConsultList(false);
            }

            ubcCommonClkSend({
                value: `ImAIRecommendUnDirect_${scene}_${type}_close`
            });
        },
        [collectedInfo?.curPatient, state?.images, state?.zhusu, scene]
    );

    // 选择就诊人
    const handleSelectPatient = useCallback(
        (selectPatient: PatientInfo) => {
            setSelectPatientData(selectPatient);
            dispatch({
                payload: {
                    ...selectPatientData,
                    zhusu: state?.zhusu || '',
                    images: state?.images || []
                }
            });
        },
        [selectPatientData, state?.images, state?.zhusu]
    );

    // 根据当前选中的skuId查找最新的sku数据
    const findSelectedSku = useCallback(
        (skuId: string) => {
            return skuData.list?.find(skuItem => skuItem.skuId === skuId);
        },
        [skuData]
    );

    const addPatientToast = useCallback(() => {
        showToast({
            title: '请完善您的提问信息',
            icon: 'none'
        });
    }, []);

    // 无就诊人或无主诉时，自动弹起弹层并进行toast提示
    // useEffect(() => {
    //     // 无就诊人id时，无性别、年龄、主诉时，弹起就诊人编辑弹层，并提示完善信息,有就诊人id时，无主诉时，弹起就诊人编辑弹层，并提示完善信息
    //     const {contactId, gender, age} = collectedInfo?.curPatient || {};
    //     const {clinicalDesc} = collectedInfo;
    //     if (!clinicalDesc && contactId) {
    //         setShowConsultList(true);
    //         addPatientToast();
    //     } else if (!contactId && (!gender || !age || !clinicalDesc)) {
    //         setShowConsultForm(true);
    //         addPatientToast();
    //     }
    //     // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, []);

    // 更新选中sku逻辑
    const handleSelectSku = useCallback(() => {
        if (!selectedSkuItemData?.skuId) {
            setSelectedSkuItemData({btn: skuData?.btnInfo || {}});
        } else {
            const selectSku = findSelectedSku(selectedSkuItemData?.skuId || '');
            selectSku && setSelectedSkuItemData(selectSku);
        }
    }, [findSelectedSku, selectedSkuItemData?.skuId, skuData?.btnInfo]);

    const handleInteractionWithConfirm = useCallback(
        (options, callbackParams) => {
            if (options?.interactionInfo?.confirm) {
                setDialogConfig({
                    content: options?.interactionInfo?.confirm?.title,
                    confirmText: options?.interactionInfo?.confirm?.confirmText || '确认下单',
                    cancelText: options?.interactionInfo?.confirm?.cancelText || '返回修改',
                    onConfirm: () => callbackParams && loginSuccessfulCallback(callbackParams)
                });
                setIsDialogOpen(true);
            } else {
                callbackParams && loginSuccessfulCallback(callbackParams);
            }
        },
        [loginSuccessfulCallback]
    );

    const handleConfirm = () => {
        dialogConfig?.onConfirm?.();
        setIsDialogOpen(false);
    };

    const handleCancel = () => {
        setIsDialogOpen(false);
    };

    // 监听登录发生变化后的数据处理
    useEffect(() => {
        if (openPatientPopTypeRef.current) {
            // 无就诊人时，直接提示完善信息，并弹起就诊人编辑弹层
            if (!collectedInfo?.curPatient?.contactId) {
                openPatientPopType === 'sku' && addPatientToast();
                openEditPatientPop();
                setIsSkuDetailPopupShow(false);
                // 更新当前选中的sku信息
                handleSelectSku();
                return;
            }
            // 未登录变为登录在状态后，存在就诊人时，点击的为就诊人模块时弹窗就诊人选择列表，否则直接走下单逻辑
            if (updateCollectedInfoAndSkuType === 'patient' || !collectedInfo?.clinicalDesc) {
                openEditPatient(); // 存在就诊人，弹就诊人切换弹层
            } else {
                // 直接走下单逻辑(免费服务无skuId时，直接走下单逻辑)
                if (!selectedSkuItemData?.skuId && !skuData?.btnInfo?.disabled) {
                    if (skuData?.btnInfo?.interactionInfo?.confirm) {
                        // 展示科室冲突确认弹窗
                        handleInteractionWithConfirm(skuData?.btnInfo, {
                            btn: skuData?.btnInfo || {}
                        });
                    }
                } else {
                    const selectSku = findSelectedSku(selectedSkuItemData?.skuId || '');
                    handleInteractionWithConfirm(selectSku?.btn, selectSku || {});
                }
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [openPatientPopTypeRef.current]);

    // 更新卡片数据：未登录转登录后，更新两类服务卡
    const handleMessageUpdate = useCallback(
        messageList => {
            messageList.forEach(messageItem => {
                updateTriageStreamMsgAtom(`${sessionId}_${messageItem?.meta?.msgId}`, messageItem, {
                    _debugSymbol: 'loginUpdate'
                });
            });
        },
        [sessionId]
    );

    // 用户行为接口数据参数
    const handleParams = useCallback(() => {
        const userLoginInfoListData: userInfoItem[] = [];
        // 合并两个消息的数据
        [undirectServiceData, docServiceData].forEach(item => {
            if (item && item?.data && item?.data?.content) {
                const {cardId} = item?.data?.content || '';
                const {msgId} = item?.meta || '';
                const {collectedInfo} = item?.data?.content?.data?.content || {};
                const compParam = {
                    msgId,
                    patientInfo: collectedInfo?.curPatient,
                    serviceType: ServiceTypeMap[cardId],
                    zhusu: collectedInfo?.clinicalDesc || '',
                    qid: collectedInfo?.qid
                };
                userLoginInfoListData.push(compParam);
            }
        });
        const params = {
            bizActionType: 'userLogin' as const,
            chatData: {
                sessionId
            },
            bizActionData: {
                userLoginInfoList: {
                    msgList: userLoginInfoListData
                }
            }
        };
        return params;
    }, [docServiceData, sessionId, undirectServiceData]);

    const showFreeSkuTipToast = useCallback((message: MsgItemType<unknown>[]) => {
        const msgData = message?.find(item => item?.meta?.msgId === props?.msgId);
        const {skuData} = msgData?.data?.content?.data?.content as ImNoNDirectSkuPData;
        const {btnInfo, toastTip} = skuData;

        if (btnInfo?.disabled && toastTip) {
            showToast({
                title: toastTip,
                icon: 'none'
            });
        }
    }, []);

    // 更新定向服务卡数据，重新渲染卡片组件
    const updateCollectedInfoAndSku = useCallback(
        // eslint-disable-next-line complexity
        async (type: OpenPatientType, selectSku?: undirectService) => {
            if (isSkuDisabled) {
                showToast({
                    title: skuDisabledToastText,
                    icon: 'none'
                });
                sessionId &&
                    adjectiveRecommendUnDirectMsgId &&
                    scrollToMessage(adjectiveRecommendUnDirectMsgId, 'updateCollectedInfoAndSku');

                ubcCommonClkSend({
                    value: `ImAIRecommendUnDirect_${scene}_disable`,
                    ext: {
                        product_info: {
                            ...ext
                        }
                    }
                });

                return;
            }

            selectSku && setSelectedSkuItemData(selectSku);
            setOpenPatientPopType(type);

            // 未登录
            if (sessionId && !prevIsLoginRef.current && !userData?.isLogin) {
                showLoading({
                    title: '登录中...',
                    mask: true
                });
                const params = handleParams();
                const [err, data] = await getUserBizActionReq<'userLogin'>(params);
                if (!err) {
                    data?.data?.message && handleMessageUpdate(data?.data?.message || []);
                    data?.data?.userData && updateUserData(data?.data?.userData);
                    // 更新胶囊工具数据
                    data?.data?.toolData?.capsules &&
                        updateSessionCapsulesTools(data?.data?.toolData?.capsules);
                    prevIsLoginRef.current = data?.data?.userData?.isLogin || false;
                    setUpdateCollectedInfoAndExpert(type);
                    openPatientPopTypeRef.current = true;
                    hideLoading({noConflict: true});
                    // 业务需求：仅点击免费咨询时，才展示无券提示
                    !selectSku?.skuId &&
                        data?.data?.message &&
                        showFreeSkuTipToast(data?.data?.message || []);
                }
            } else {
                // 已登录--无就诊人
                if (!collectedInfo?.curPatient?.contactId) {
                    // 点击【去咨询】时，需要先toast后再出弹层
                    type === 'sku' && addPatientToast();
                    openEditPatientPop();

                    return;
                }
                // 已登录--有就诊人--就诊人选择模块
                if (type === 'patient' || !collectedInfo?.clinicalDesc) {
                    openEditPatient();
                } else {
                    // 已登录--有就诊人--服务卡去咨询
                    // 直接走下单逻辑
                    handleInteractionWithConfirm(selectSku?.btn, selectSku || {});
                }
            }
            if (type === 'sku') {
                ubcCommonClkSend({
                    value: 'ImAIRecommendUnDirect',
                    ext: {
                        value_type: 'sku',
                        value_id: selectSku?.skuId || 'free',
                        product_info: {
                            msgId: props?.msgId || '',
                            ...ext
                        }
                    }
                });
            } else {
                ubcCommonClkSend({
                    value: 'ImAIRecommendUnDirect_edit',
                    ext: {
                        product_info: {
                            msgId: props?.msgId || '',
                            ...ext
                        }
                    }
                });
            }
        },
        [
            isSkuDisabled,
            sessionId,
            userData?.isLogin,
            ext,
            adjectiveRecommendUnDirectMsgId,
            scene,
            skuDisabledToastText,
            props?.msgId,
            handleParams,
            handleMessageUpdate,
            handleInteractionWithConfirm,
            showFreeSkuTipToast,
            updateUserData,
            updateSessionCapsulesTools,
            collectedInfo?.curPatient?.contactId,
            collectedInfo?.clinicalDesc,
            addPatientToast,
            openEditPatientPop,
            openEditPatient
        ]
    );

    useEffect(() => {
        ubcCommonViewSend({
            value: 'ImAIRecommendUnDirect',
            ext: {
                product_info: {
                    msgId: props?.msgId || '',
                    ...ext
                }
            }
        });
    }, [ext, props?.msgId]);

    return (
        <View style={{width: '100%'}}>
            {/* AI重构-就诊人 */}
            <PatientCard
                isLogin={userData?.isLogin || false}
                collectedInfo={collectedInfo}
                updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                isSkuDisabled={isSkuDisabled}
            />
            <UndirectService
                skuData={skuData}
                updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                lineText={lineText}
                isLogin={userData?.isLogin || false}
                updateSkuDetailData={updateSkuDetailData}
                isSkuDetailPopupShow={isSkuDetailPopupShow}
                setIsSkuDetailPopupShow={setIsSkuDetailPopupShow}
                isSkuDisabled={isSkuDisabled}
                hideTopSkuArea={hideTopSkuArea}
                skuDisabledToastText={skuDisabledToastText}
                adjectiveRecommendUnDirectMsgId={adjectiveRecommendUnDirectMsgId}
            />
            <Portal>
                {collectedInfo && (showConsultForm || showConsultList) && (
                    <ConsultForm
                        showConsultForm={showConsultForm}
                        showConsultList={showConsultList}
                        collectedInfo={collectedInfo}
                        requireDesc={requireDesc}
                        handleAddPatient={handleAddPatient}
                        handleCloseConsultForm={handleCloseConsultForm}
                        openEditPatient={openEditPatient}
                        state={state}
                        dispatch={dispatch}
                        openPatientPopType={openPatientPopType}
                        handleSelectPatient={handleSelectPatient}
                        selectPatientData={selectPatientData}
                        selectedSkuItemData={selectedSkuItemData}
                        loginSuccessfulCallback={loginSuccessfulCallback}
                        skuList={skuData?.list}
                        toastTip={skuData?.toastTip}
                        msgId={props?.msgId || ''}
                        sessionId={sessionId || ''}
                        qid={qid}
                        ext={ext}
                        setSelectPatientData={setSelectPatientData}
                    />
                )}
            </Portal>
            <Portal>
                <Dialog open={isDialogOpen} onClose={handleCancel}>
                    <Dialog.Content>{dialogConfig?.content}</Dialog.Content>
                    <Dialog.Actions>
                        <Button onClick={handleCancel}>{dialogConfig?.cancelText}</Button>
                        <Button onClick={handleConfirm} style={{color: '#00c8c8'}}>
                            {dialogConfig?.confirmText}
                        </Button>
                    </Dialog.Actions>
                </Dialog>
            </Portal>
        </View>
    );
};

export default memo(ImCollectedAndNoNDirectSku);
