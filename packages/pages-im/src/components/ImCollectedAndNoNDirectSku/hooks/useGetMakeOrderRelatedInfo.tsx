import {useCallback} from 'react';

import {showToast} from '../../../utils/customShowToast';
import {useCommonPay} from '../../../../../utils-shared/pay/useCommonPay';
import {useGetSessionId} from '../../../hooks/triageStream/pageDataController';
import {getUserBizActionReq} from '../../../models/services/triageStream';
import {ubcCommonClkSend} from '../../../utils/generalFunction/ubc';
import {parseURL, assembleUrl} from '../../../utils/generalFunction/urlRelated';
import {useMsgDataSetController} from '../../../hooks/triageStream/dataController';
import type {MsgId, Qid} from '../../../typings';
import type {undirectService} from '../index.d';

interface IGetMakeOrderRelatedInfoType {
    msgId: MsgId;
    skuList?: undirectService[];
    qid?: Qid;
}

export const useGetMakeOrderRelatedInfo = (args: IGetMakeOrderRelatedInfoType) => {
    const {msgId, skuList, qid} = args;

    const {confirmPay} = useCommonPay();
    const sessionId = useGetSessionId();
    const {updateMsgData} = useMsgDataSetController({msgId});


    // 用户支付取消时，刷新 sku 数据
    const onCancelPay = useCallback(async () => {
        const params = {
            bizActionType: 'switchCoupon' as const,
            chatData: {
                sessionId: sessionId || '',
            },
            bizActionData: {
                switchCouponInfo: {
                    msgId,
                    couponId: 0,
                    serviceType: 'undirectService',
                    qid
                }
            }
        };
        const [err, data] = await getUserBizActionReq<'switchCoupon'>(params);
        if (!err) {
            data?.data?.message && updateMsgData(data?.data?.message[0]);
        }
    }, [msgId, qid, sessionId, updateMsgData]);

    /**
     *
     * @description 确认支付
     */
    const makeOrder = useCallback(
        (skuDataToOrder: undirectService) => {
            return new Promise(async (resolve, reject) => {
                try {
                    const url = skuDataToOrder?.btn?.interactionInfo?.url || '';

                    if (!url) {
                        reject(new Error('makeOrder 下单 url 参数错误'));
                    }

                    const {params: paramsOfUrl, pathname} = parseURL(url);

                    const reqParams = {
                        ...paramsOfUrl
                    };
                    const resUrl = assembleUrl(pathname, reqParams);

                    const data = await confirmPay({
                        interactionInfo: {
                            url: resUrl
                        },
                        onCancelPay,
                        // 免费服务时，不展示正在支付中的文案，改为展示正在前往候诊页
                        loadingText: !skuDataToOrder?.skuId ? '正在前往候诊页' : '正在支付中'
                    });

                    if (data?.isExpertUnavailable) {
                        showToast({
                            title: '名额已抢完, 您可继续选择该医生的其他服务',
                            icon: 'none'
                        });
                    }

                    if (data?.isUserUnavailable) {
                        showToast({
                            title: '本周名额已用尽, 您可继续选择该医生的其他服务',
                            icon: 'none'
                        });
                    }

                    resolve(null);

                    skuDataToOrder?.skuId && ubcCommonClkSend({
                        value: `streamFocusService_${skuDataToOrder?.skuId}`,
                        ext: {
                            product_info: {
                                skuList: skuList?.map(skuItem => {return skuItem?.skuId })
                            }
                        }
                    });
                } catch (err) {
                    reject(err);
                }
            });
        },
        [confirmPay, onCancelPay, skuList]
    );

    /**
     *
     * @description 登录成功后，进行下单操作; 二次校验登录状态
     */
    const loginSuccessfulCallback = useCallback(
        (skuForOrder: undirectService) => {
            return new Promise(async (resolve, reject) => {
                try {
                    await makeOrder(skuForOrder);
                    resolve(null);
                } catch (err) {
                    reject(err);
                }
            });
        },
        [makeOrder]
    );

    return {
        loginSuccessfulCallback
    };
};
