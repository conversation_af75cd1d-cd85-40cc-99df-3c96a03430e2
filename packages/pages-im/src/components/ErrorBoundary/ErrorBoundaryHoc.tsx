import React, { ComponentType, PropsWithChildren } from 'react';
// import CMiddlePage from '@components/base/CMiddlePage';

import { ErrorBoundary, ErrorBoundaryProps } from './ErrorBoundaryClass';

/**
 * errorBoundaryHooks
 * @param PageComponent 业务组件
 * @param errorBoundaryProps 扩展参数；包含错误监听和重置监听；异常自定义加载组件钩子
 * @returns 返回一个 ErrorBoundary 组件
 * * @example
 *
 * // 引入 createErrorBoundary 组件
 * import { createErrorBoundary } from '@components/ErrorBoundaryHoc';
 *
 * // 使用 createErrorBoundary 组件
 * createErrorBoundary(memo(DemoComponent), {
 *      FallbackComponent: <CMiddlePage/>, // 自定义 FallbackComponent 兜底的组件
 *      onError?: (error: Error, info: string) => void; // 错误监听
 *      onReset?: () => void; // 重置逻辑
 * })
 */

export function createErrorBoundary<P>(
    PageComponent: ComponentType<P>,
    errorBoundaryProps?: ErrorBoundaryProps
): ComponentType<P> {
    const Wrapped: ComponentType<P> = (props: PropsWithChildren<P>) => {
        const _errorBoundaryProps = {
            // fallbackRender: fallbackProps => (
            //     <CMiddlePage {...{ ...fallbackProps, onButtonClicked: fallbackProps.resetErrorBoundary }} />
            // ),
            ...errorBoundaryProps
        };

        return (
            <ErrorBoundary {..._errorBoundaryProps}>
                <PageComponent {...props} />
            </ErrorBoundary>
        );
    };

    // DevTools 显示的组件名
    const name = PageComponent.displayName || PageComponent.name || 'Unknown';
    Wrapped.displayName = `withErrorBoundary(${name})`;

    return Wrapped;
}
