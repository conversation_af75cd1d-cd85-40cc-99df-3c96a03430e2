import {MsgId} from '../../typings';

import type {MsgMetaData} from '../../store/triageStreamAtom/index.type';
import type {CollectedInfoProps} from './PatientCard/index.d';
import type {RequireDescProps} from './ConsultForm/index.d';

export interface ImCollectedAndReExpertProps {
    cardId: string;
    cardName: string;
    version: number;
    data: {
        content: ImCollectedAndReExpertPData;
        ext?: {
            scene?: string;
            descLimit?: DescLimit;
        };
    };
    msgId: MsgId;
    localExt?: MsgMetaData['localExt'];
}

export interface DescLimit {
    limitLength?: number;
    limitToast?: string;
}

// 就诊卡片和sku数据结构
export interface ImCollectedAndReExpertPData {
    requireDesc: RequireDescProps;
    collectedInfo: CollectedInfoProps;
    expertData: expertDataProps;
    lineText?: string;
    isExpired?: boolean;
}

export type OpenPatientType = 'expert' | 'patient';

export type CONSULT_TYPE = 'consultList' | 'consult'; // consultList是就诊人选择弹层，consult是就诊人编辑弹层

export interface IDetailInfo {
    title?: string;
    doctorTitle?: string;
    discount?: string;
    features?: string[];
    explainTitle?: string;
    explains?: string[];
    oldPrice?: string; // 服务价格
    finalPrice?: string; // 合计价格
    btn?: string;
    promotionReductionPrice?: string; // 平台立减 活动优惠(减价)
    couponReductionPrice?: string; // 用券优惠(减价)
}

export interface IBtn {
    interaction?: string;
    interactionInfo?: {
        url?: string;
    };
    value?: string;
}

export interface typeValObjData {
    type: string;
    value: string;
}

export interface attributeTagItem {
    text: string;
    key: string;
    color: string;
    borderColor: string;
}

export interface topLabelItem {
    text: string;
    key: string;
    bgColor: string;
    borderColor: string;
}

export interface indicatorItem extends typeValObjData {
    highLightColor?: string;
}

export interface expertItem {
    expertId: string;
    coreId: string;
    docID: string;
    expertName: string;
    expertPic: string;
    expertLevel?: string;
    expertHospital?: string;
    expertDepartment?: string;
    freeTag?: string;
    expertGoodAt?: typeValObjData[];
    price?: string;
    isOnline?: number;
    showPortraitTag?: typeValObjData[];
    attributeTag?: attributeTagItem[];
    topLabels?: topLabelItem[];
    indicatorList?: indicatorItem[];
    actionInfo?: IBtn;
    btnInfo?: IBtn;
    recommendationReasons?: string;
    // 位置
    pos?: number;
}

export interface expertDataProps {
    title?: string;
    leftIcon?: string;
    list?: expertItem[];
}
