@import url('../../../style/variable.less');

.expertRecommendationWrapper {
    position: relative;
}

.directPad {
    padding: 0 45px 15px;
}

.combPad {
    padding: 72px 45px 15px;
}

.noUndirectServicePad {
    padding: 0 45px 15px;
}

.expertRecommendation {
    width: 100%;
    background: #fff;
    border-radius: 45px;
    overflow: hidden;
    box-sizing: border-box;
    position: relative;

    .titleSkeleton {
        width: 222px;
        height: 51px;
        border-radius: 12px;
        background: #eff3f9;
        margin-bottom: 63px;
    }

    .skeletonBg {
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
        background-size: 400% 100%;
        animation: skeleton-loading 1.4s ease infinite;
    }

    @keyframes skeleton-loading {
        0% {
            background-position: 100% 50%;
        }

        100% {
            background-position: 0 50%;
        }
    }
}

.isDisabledContainer {
    position: absolute;
    background: #fff;
    inset: 0;
    opacity: 0.4;
    z-index: @zIndex-doctorMask;

    .isDisabledIcon {
        position: absolute;
        right: 0;
    }

    .ineffectiveIcon {
        width: 198px;
        height: 156px;
        position: absolute;
        top: 0;
        right: 0;
    }
}

.isCombDisableContainer {
    position: absolute;
    inset: 0;
    background: #fff;
    opacity: 0.5;
    z-index: 30;
    border-radius: 0 0 45px 45px;

    .isDisabledIcon {
        position: absolute;
        right: 0;
    }

    .ineffectiveIcon {
        width: 198px;
        height: 156px;
        position: absolute;
        top: 0;
        right: 0;
    }
}

.doctorCard {
    overflow: hidden;
}

// 主要是用于改last-child，没用公共样式，清晰一点
.combDocMargin {
    margin-bottom: 30px;
}

.combDocMargin:last-child {
    margin-bottom: 60px;
}

.line {
    width: 100%;
    height: 1px;
    background: #e6e6e6;
    overflow: hidden;
}

.recommendationIcon {
    margin-bottom: 63px;
}

.recommendationTitle {
    font-weight: 600;
}

.moreBtn {
    color: #858585;
    bottom: 0;
    height: 145px;
    position: absolute;
    background: #fff;
    width: 100%;
    z-index: 3;
    border-radius: 45px;
}

.directStyle {
    color: #858585;
    bottom: 0;
    height: 145px;
    position: absolute;
    background: #fff;
    width: 100%;
    z-index: 3;
    border-radius: 45px;
}

.combinationStyle {
    color: #858585;
    background: #fff;
    width: 100%;
    z-index: 3;
    border-radius: 45px;
    margin-top: -1px;

    /* 修复遮罩无法完全盖住底部, 这里需要使用padding */
    padding-bottom: 60px;
}

.lineMarginLeft {
    margin: 75px 0 45px 144px;
}

.bottomSpace {
    width: 100%;
    height: 45px;
}
