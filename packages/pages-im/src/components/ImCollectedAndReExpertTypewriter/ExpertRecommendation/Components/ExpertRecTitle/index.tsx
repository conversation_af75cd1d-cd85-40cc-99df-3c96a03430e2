import {View} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import {FC, memo} from 'react';
import cx from 'classnames';

import type {ITitleProps} from './index.d';
import styles from './index.module.less';

const ExpertRecTitle: FC<ITitleProps> = ({content, iconUrl, show = true}) => {
    const showIcon = iconUrl && iconUrl.length > 0;

    return (
        <>
            {show ? (
                <View className={cx(styles.expertRecTitle, 'wz-flex')}>
                    {showIcon && <WImage className={cx(styles.icon, 'wz-mr-24')} src={iconUrl} />}
                    <View>{content}</View>
                </View>
            ) : (
                <></>
            )}
        </>
    );
};

export default memo(ExpertRecTitle);
