import { BaseComponent } from '../Base.d';

export interface LoginButtonProps extends BaseComponent {

    /**
     * 当前是否登录 isLogin === true登录成功; isLogin === false登录失败
     */
    isLogin?: boolean;

    /**
     * 是否启用手百和好看的一键登录（如果启用则需要配合传isLogin，否则已登录他还弹登录框，目前大部分页面没有传isLogin，所以需要这个开关）
     */
    useH5QuickLogin?: boolean | number;

    /**
     * 登陆成功之后跳转地址
     */
    callbackUrl?: string;

    /**
     * 使用H5 验证码登录
     */
    useH5CodeLogin?: boolean;

    /**
     * 关闭H5登录弹窗 新人专享优惠标签
     */
    closeShowNewUserTag?: boolean;

    /**
     * 上报打点值
     */
    ubcValue?: string;

    /**
     * 登录成功回调
     */
    onLoginSuccess?: (e?: any) => void;

    /**
     * 登录失败回调
     */
    onLoginFail?: (e?: any) => void;

    /**
     * 点击回调事件
     */
    onCallBack?: (e?: any) => void;

    /**
     * 是否为弹窗登录触发
     */
    isLoginPopup?: boolean;

    /**
     * 微信登录交互方式
     */
    wxLoginInteractionType?: 'popup' | 'page';

}
