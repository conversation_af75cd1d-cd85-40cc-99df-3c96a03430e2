import cx from 'classnames';
import { <PERSON>, Button } from '@tarojs/components';
import React, { FC, memo, useCallback, useState, useMemo } from 'react';

import { versionCompare } from '../../utils/core';
import { toLoginWithApi } from '../../utils/generalFunction/login';
import { ubcCommonViewSend, ubcCommonClkSend } from '../../utils/generalFunction/ubc';

import styles from './index.module.less';
import { LoginButtonProps } from './index.d';

const canIUseLoginButton = process.env.TARO_ENV === 'swan' && swan.canIUse('button.open-type.login');
// 放全局是因为login组件之间公用数据
let __isLogin: boolean | string | number = false;
const LoginButton: FC<LoginButtonProps> = (props: LoginButtonProps) => {
    const {
        isLogin = false,
        useH5QuickLogin,
        useH5CodeLogin,
        className,
        children,
        callbackUrl,
        ubcValue,
        closeShowNewUserTag,
        onLoginSuccess,
        onLoginFail
    } = props;
    __isLogin = isLogin;
    const [loginOpen, setLoginOpen] = useState(false);
    const code = '';

    /**
     * 登录成功回调
     * @event onLoginSuccess
     */
    const loginSuccess = useCallback(
        (event = {}) => {
            __isLogin = true;
            onLoginSuccess
                && onLoginSuccess({
                    ...event,
                    loginStatus: true
                });
            if (ubcValue) {
                ubcCommonClkSend({
                    value: ubcValue
                });
            }
            setLoginOpen(false);
        },
        [onLoginSuccess, ubcValue]
    );

    /**
     * 登录失败回调
     * @event onLoginSuccess
     */
    const loginFail = useCallback(
        (event = {}) => {
            onLoginFail
                && onLoginFail({
                    ...event,
                    loginStatus: false
                });
        },
        [onLoginFail]
    );

    // 百度小程序专用, 吊起小程序内联登陆面板
    const onSwanButtonLogin = useCallback(
        e => {
            if (e && e.detail && e.detail.errMsg === 'login:ok') {

                /**
                 * 登录成功回调
                 * @event onLoginSuccess
                 */
                loginSuccess(e);
            } else {

                /**
                 * 登录失败回调（取消登录 + 登录失败）
                 * @event onLoginFail
                 */
                loginFail(e);
            }

            if (ubcValue) {
                ubcCommonViewSend({
                    value: ubcValue
                });
            }
        },
        [loginFail, loginSuccess, ubcValue]
    );

    const ViewLoginSuccess = useCallback(() => {
        onLoginSuccess && onLoginSuccess();
    }, [onLoginSuccess]);

    /**
     * 手百一键登录
     */
    const onBDquickLogin = useCallback(() => {
        // 因为挂载在全局的数据无法绑定dom，所以再判断一下
        if (__isLogin) {
            onLoginSuccess && onLoginSuccess({});

            return;
        }
        // const boxx = require('@baidu/boxx').default || {};
        // boxx.account
        //     && boxx.canIUse('account.login')
        //     && boxx.account.login({
        //         loginType: 'fast',
        //         showThirdLogin: '1',
        //         loginSource: 'se_008000',
        //         normalizeAccount: '0',
        //         success: e => {
        //             __isLogin = true;
        //             loginSuccess(e);
        //         },
        //         fail: e => {
        //             loginFail(e);
        //         }
        //     });
    }, [loginFail, loginSuccess, onLoginSuccess]);

    /**
     * 好看视频一键登录
     */
    const onHKquickLogin = useCallback(() => {
        window.loginresult = res => {
            try {
                // eslint-disable-next-line no-param-reassign
                res = JSON.parse(res);
                if (+res?.status === 0) {
                    loginSuccess(res);
                    __isLogin = true;
                } else {
                    loginFail(res);
                }
            } catch (error) {
                loginFail(error);
            }
        };
        try {
            window.location.href = 'baiduhaokan://action/login?from=tuoguan&callback=loginresult';
        } catch (error) {
            loginFail(error);
        }
    }, [loginFail, loginSuccess]);

    /**
     * 调用登录能力
     */
    const navToLoginPage = useCallback(
        e => {
            e.stopPropagation();
            if (ubcValue) {
                ubcCommonViewSend({
                    value: ubcValue
                });
            }
            // 半弹窗H5登录
            if (useH5CodeLogin && process.env.TARO_ENV === 'h5') {
                setLoginOpen(true);

                return;
            }
            toLoginWithApi({
                href: callbackUrl,
                params: { ...e, wxCode: code }
            });
        },
        [callbackUrl, useH5CodeLogin, ubcValue, code]
    );

    const renderLoginedStatus = useCallback(() => {
        // 小程序
        if (canIUseLoginButton) {
            return (
                <Button
                    className={styles.mall_login_button_loginButton}
                    type='primary'
                    open-type='login'
                    onLogin={onSwanButtonLogin} // 有警告, 但是可以成功回调
                />
            );
        }

        if (process.env.TARO_ENV === 'h5' && useH5QuickLogin) {
            const getPlatform = require('../../utils/basicAbility/adWebView/getPlatform.ts').default || {};
            const isHaokanApp = getPlatform.isHaokanApp();
            const isBaiduApp = getPlatform.isBaiduApp();
            if (isBaiduApp) {
                // 未登录且启用才用一键登录的逻辑，因为大部分页面没有传isLogin，且因为端能力比较sd，不给判断丢给业务自己处理。。。
                // 手百一键登录 http://es.baidu-int.com/index#/apidetail/15/base
                // const boxx = require('@baidu/boxx').default || {};
                // try {

                //     /**
                //      * 可以支持的端能力描述表
                //      * 手百下小视频弹层应该是端上没下发，业务补发一下。wtf！！！
                //      */
                //     if (boxx.canIUse('setDefaultDescription')) {
                //         const { Descriptions } = require('@common/utils/basicAbility/adWebView/boxxDescription');
                //         boxx.setDefaultDescription(Descriptions);
                //     }
                // } catch (error) {
                //     console.error(error);
                // }
                // if (boxx.canIUse('account.login')) {
                //     return <View className={styles.mall_login_button_loginButton} onClick={onBDquickLogin} />;
                // }
            }
            // 好看内嵌
            if (isHaokanApp) {
                const haokanAppVersion = getPlatform.haokanAppVersion();
                const versionResult = versionCompare(haokanAppVersion, '********') || -1;
                if (isHaokanApp && versionResult >= 0) {
                    return <View className={styles.mall_login_button_loginButton} onClick={onHKquickLogin} />;
                }
            }
        }

        return <View className={styles.mall_login_button_loginButton} onClick={navToLoginPage} />;
    }, [navToLoginPage, onBDquickLogin, onHKquickLogin, onSwanButtonLogin, useH5QuickLogin]);

    const isH5Triage = useMemo(() => window.location.pathname.includes('/wenzhen/pages/triage/index'), []);

    return (
        <>
            <View className={cx(styles.mall_login_button, className)}>
                {/* 如果未登录仅渲染children, 这样保证children上的event事件可以执行 */}
                {/* 如果已经登录 渲染children和登录元素（renderLoginedStatus）；
                 * 根据event冒泡原理，children上挂载的onClick会阻塞外层登录元素的执行，
                 * 为了保证登录事件执行，使用css手段，登录元素（renderLoginedStatus）绝对定位覆盖在children
                 * */}
                {__isLogin ? (
                    <View className={styles.mall_login_button_loginButton} onClick={ViewLoginSuccess} />
                ) : (
                    renderLoginedStatus()
                )}
                {children}
            </View>
            {/* <ImLoginPopup
                isH5Triage={isH5Triage}
                unShowNewUserTag={closeShowNewUserTag}
                open={loginOpen}
                setOpen={setLoginOpen}
                loginSuccess={loginSuccess}
            /> */}
        </>
    );
};

LoginButton.defaultProps = {
    isLogin: false,
    callbackUrl: '',
    useH5QuickLogin: false
};

export default memo(LoginButton);
