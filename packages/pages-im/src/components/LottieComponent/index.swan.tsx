import Taro from '@tarojs/taro';
import React, {useCallback, useState, useEffect} from 'react';
import {AnimationView} from '@tarojs/components';

import type {LottieComponentProps} from './index.d';

// 小程序需要在真机扫码预览效果

const LottieComponent: React.FC<LottieComponentProps> = (props: LottieComponentProps) => {
    const {customStyle, bosPath, autoplay, loop, hidden} = props;
    const [swanPath, setSwanPath] = useState('');

    // 端上暂不支持解析远程路径的 JSON 文件, 需要将json 文件下载到本地使用
    const getLottieTemFile = useCallback(lottieUrl => {
        Taro.downloadFile({
            url: lottieUrl,
            success: res => {
                setSwanPath(res.tempFilePath);
            },
            fail(res) {
                console.log('error', res);
            }
        });
    }, []);

    useEffect(() => {
        getLottieTemFile(bosPath);
    }, [getLottieTemFile, bosPath]);

    return (
        <AnimationView
            style={{...customStyle}}
            path={swanPath}
            autoplay={autoplay}
            loop={loop}
            hidden={hidden}
        />
    );
};

export default LottieComponent;
