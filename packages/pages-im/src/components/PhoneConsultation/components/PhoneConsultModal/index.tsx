import cx from 'classnames';
import { View } from '@tarojs/components';
import { memo, useCallback, useState } from 'react';
import { showLoading, hideLoading } from '@tarojs/taro';
import { Button, Backdrop, Input } from '@baidu/wz-taro-tools-core';
import { CloseCircle, WiseCloseSolid } from '@baidu/wz-taro-tools-icons';

import RichRender from '../../../../components/RichRender';
import { API_HOST } from '../../../../models/apis/host';
import { phoneNumberReg } from '../../../../constants/reg';

import { isEmpty } from '../../../../utils';
import { showToast } from '../../../../utils/customShowToast';
import httpRequest from '../../../../utils/basicAbility/comonRequest/common';
import { useCommonPay } from '../../../../../../utils-shared/pay/useCommonPay';

import type { richItem } from '../../../../typings/cui.d';
import type { InteractionType, InteractionInfo } from '../../../../typings';

import styles from './index.module.less';

interface PhoneData {
    payUrl?: string;
    wxPayInfo?: object;
    phone?: string;
    successJumpUrl?: string;
}

interface FullPhoneData {
    phone?: string;
}

interface IProps {
    confirm?: (phone: string) => void;
    onClosePopup: () => void;
    onFreshSku?: () => void;
    onCancelPay?: () => void;
    title: string;
    defaultPhoneNumber: string;
    submitUrl: {
        interaction: string;
        interactionInfo: {
            url: string;
            params: {
                [k in string]: string;
            };
        };
    };
    desc?: string;
    submitText?: string;
    type?: string;
    payWay?: string; // 支付方式，new是跳转到订单详情再调起支付，原是直接调起支付
    descRich?: richItem[]; // 描述的富文本格式
    seePhone?: {
        // 手机号显示全文功能
        value: string;
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}

export default memo((props: IProps) => {
    const {
        onClosePopup,
        onFreshSku,
        onCancelPay,
        defaultPhoneNumber,
        submitText = '提交并支付',
        submitUrl: { interactionInfo },
        desc = '到达预期时间医生将向该手机号回拨',
        title = '请您输入手机号',
        confirm,
        type = 'pay',
        payWay,
        descRich,
        seePhone
    } = props;
    const { url, params } = { ...interactionInfo };

    const [phoneNumber, setPhoneNumber] = useState<string>(defaultPhoneNumber || '');
    const [fullPhoneDisable, setFullPhoneDisable] = useState<boolean>(false);
    const { confirmPay } = useCommonPay();

    const onPhoneChange = useCallback(e => {
        setPhoneNumber(e?.detail?.value);
        setFullPhoneDisable(true);
    }, []);

    const close = useCallback(() => {
        onClosePopup && onClosePopup();
    }, [onClosePopup]);

    /**
     *
     * @description 提交手机号验证码
     */
    const showFullPhone = useCallback(async () => {
        if (seePhone && !fullPhoneDisable) {
            const { interaction, interactionInfo: phoneInfo } = seePhone;
            if (interaction === 'request') {
                showLoading({
                    title: '获取中...'
                });
                const [err, res] = await httpRequest<FullPhoneData>({
                    url: `${API_HOST}${phoneInfo?.url || ''}`,
                    method: phoneInfo?.method,
                    data: phoneInfo?.params
                });
                if (res?.data?.phone) {
                    setPhoneNumber(res?.data?.phone);
                } else {
                    console.error('获取手机号出错：', err);
                    showToast({
                        title: '获取手机号出错',
                        icon: 'error'
                    });
                }
                setFullPhoneDisable(true);
                hideLoading();
            }
        }
    }, [fullPhoneDisable, seePhone]);

    /**
     *
     * @description 提交手机号验证码
     */
    const submit = useCallback(async () => {
        try {
            if (phoneNumber !== defaultPhoneNumber && (!phoneNumber || !phoneNumberReg.test(phoneNumber))) {
                showToast({
                    title: phoneNumber ? '手机号格式填写错误' : '请填写手机号',
                    icon: 'none'
                });

                return;
            }
            switch (payWay) {
                case 'new':
                    await confirmPay({
                        interactionInfo: {
                            url,
                            params: {
                                ...params,
                                phone: phoneNumber
                            }
                        },
                        onFreshSku,
                        onCancelPay
                    });
                    break;
                default:
                    break;
            }
        } catch (err) {
            console.error('submit 出错：', err);
            showToast({
                title: '提交出错',
                icon: 'error'
            });
        } finally {
            hideLoading({
                noConflict: true
            });
        }
    }, [phoneNumber, defaultPhoneNumber, payWay, confirmPay, url, params, onFreshSku, onCancelPay]);

    return (
        <Backdrop
            open
            style={{
                zIndex: 9999
            }}
        >
            <View className={cx(styles.modalContainer, 'wz-flex wz-col-center wz-row-center')}>
                <View className={cx(styles.modalContent)}>
                    <View className={cx(styles.title, 'wz-fs-57 wz-fw-700 wz-mb-36')}>{title}</View>
                    {isEmpty(descRich) ? (
                        <View className={cx(styles.desc, 'wz-fs-48')}>{desc}</View>
                    ) : (
                        <View className={cx(styles.descRich)}>
                            <RichRender richData={descRich || []} textStyle={{ wordBreak: 'break-all' }} />
                        </View>
                    )}
                    <View className={cx(styles.phoneWrap, 'wz-mt-36')}>
                        <View
                            className={cx(
                                styles.phoneNumber,
                                'wz-flex wz-row-between',
                                !isEmpty(seePhone) ? styles.seePhoneFix : ''
                            )}
                        >
                            <Input
                                clearable
                                value={phoneNumber}
                                clearTrigger='always'
                                placeholder='请输入手机号'
                                onChange={onPhoneChange}
                                clearIcon={<WiseCloseSolid />}
                            />
                        </View>
                        {!isEmpty(seePhone) && (
                            <View
                                className={cx(
                                    'wz-fs-42 wz-pl-36',
                                    styles.seePhoneBtn,
                                    fullPhoneDisable ? styles.disableBtn : ''
                                )}
                                onClick={showFullPhone}
                            >
                                {seePhone?.value || '显示全文'}
                            </View>
                        )}
                    </View>
                    <Button color='primary' shape='round' className={cx(styles.btn)} onClick={submit}>
                        {submitText}
                    </Button>
                </View>
                <CloseCircle className={styles.close} size={120} color='#fff' onClick={close} />
            </View>
        </Backdrop>
    );
});
