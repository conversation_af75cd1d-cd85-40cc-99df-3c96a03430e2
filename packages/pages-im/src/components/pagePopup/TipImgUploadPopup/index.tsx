/*
 * @Author: lijing106
 * @Description: 图片上传引导弹窗组件
 */
import cx from 'classnames';
import {View} from '@tarojs/components';
import {type FC, memo, useCallback, useEffect} from 'react';
import {pxTransform, previewImage} from '@tarojs/taro';
import {Popup, WImage} from '@baidu/wz-taro-tools-core';

import Portal from '../../../../../ui-cards-common/Portal'; // monorepo 内部引入暂时使用相对路径，避免与 taro 分端构建冲突；@wanghaoyu08

import {API_HOST} from '../../../models/apis/host';
import {BUCKET_NAME, ONLINE_HOST} from '../../../constants/common';

import {previewPic} from '../../../utils/basicAbility/upload';
import {ubcCommonClkSend, ubcCommonViewSend} from '../../../utils/generalFunction/ubc';

import type {TipImgUploadPopupProps} from './index.d';

import styles from './index.module.less';

const UPLOADTYPEMAP = {
    album: '本地图片',
    camera: '相机拍照'
};

/**
 *
 * @description 发图小贴士弹窗
 * @param props
 * @returns
 */

const bucketConfName =
    API_HOST && ONLINE_HOST.indexOf(API_HOST) > -1 ? BUCKET_NAME[2] : `${BUCKET_NAME[2]}-test`;
const TipImgUploadPopup: FC<TipImgUploadPopupProps> = (props: TipImgUploadPopupProps) => {
    const {
        open,
        tipsData,
        sceneType,
        uploadOps = ['album', 'camera'],
        onSelectedPics,
        closeUploadPopup
    } = props;

    // 预览图片
    const handleViewPic = useCallback(
        url => {
            const urls =
                tipsData?.content
                    ?.filter(item => item?.type === 'img')
                    ?.reduce((prev: string[], cur) => {
                        const imgs = cur?.data?.list?.map(imgItem => imgItem?.small) || [];
                        return [...prev, ...imgs];
                    }, []) || [];
            previewImage({
                current: url,
                urls: [...urls]
            });
        },
        [tipsData?.content]
    );

    // 渲染内容
    const renderContent = useCallback(() => {
        return (
            <>
                {tipsData?.content?.map(item => {
                    if (item?.type === 'text') {
                        return (
                            <View
                                className={cx(styles.desc, 'wz-fs-48')}
                                key={item?.data?.value?.toString()}
                            >
                                <View className={cx(styles.desc, 'wz-fs-48')}>
                                    {item?.data?.value}
                                </View>
                            </View>
                        );
                    }
                    if (item?.type === 'title') {
                        return (
                            <View
                                className={cx(styles.title, 'wz-fs-54')}
                                key={item?.data?.value?.toString()}
                            >
                                {item?.data?.value}
                            </View>
                        );
                    }
                    if (item?.type === 'img') {
                        return (
                            <View
                                className={cx(styles.imgContainer, 'wz-flex wz-mtb-45')}
                                key={item?.data?.list?.toString()}
                            >
                                {Array.isArray(item?.data?.list) &&
                                    item?.data?.list?.length > 0 &&
                                    item?.data?.list?.map((imgItem, index) => (
                                        <View key={`${imgItem?.small}_${index}`}>
                                            <View className={cx(styles.imgItem, 'wz-mr-30')}>
                                                <WImage
                                                    className={cx(styles.imgTips)}
                                                    src={imgItem?.small}
                                                    mode='aspectFill'
                                                    onClick={() => handleViewPic(imgItem?.small)}
                                                />
                                            </View>
                                            <View
                                                className={cx(
                                                    styles.imgDesc,
                                                    'wz-mt-30 wz-text-center wz-mr-30'
                                                )}
                                            >
                                                {imgItem?.desc || ''}
                                            </View>
                                        </View>
                                    ))}
                            </View>
                        );
                    }
                    return <></>;
                })}
            </>
        );
    }, [handleViewPic, tipsData]);

    const handleUpload = useCallback(
        async type => {
            try {
                if (!sceneType) {
                    console.error('[TipImgUploadPopup] sceneType is undefined');
                    return;
                }
                ubcCommonClkSend({
                    value: `imgUpdatePopup_${type}`,
                    ext: {
                        product_info: {
                            scene: sceneType,
                            uploadType: uploadOps
                        }
                    }
                });

                closeUploadPopup();
                const preData = await previewPic({count: 1, bucketConfName, btnType: type});
                if (!preData || !Object.keys(preData)?.length) return;
                onSelectedPics && onSelectedPics(preData, {sceneType});

                ubcCommonClkSend({
                    value: `imgUpdatePopup_${type}_completed`,
                    ext: {
                        product_info: {
                            scene: sceneType,
                            uploadType: uploadOps
                        }
                    }
                });
            } catch (error) {
                console.error(error);
            }
        },
        [sceneType, closeUploadPopup, onSelectedPics, uploadOps]
    );

    useEffect(() => {
        open &&
            ubcCommonViewSend({
                value: 'imgUpdatePopup',
                ext: {
                    product_info: {
                        scene: sceneType,
                        uploadType: uploadOps
                    }
                }
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open]);

    return (
        <Portal>
            <Popup
                open={open}
                rounded
                placement='bottom'
                style={{
                    maxHeight: '100%',
                    background: 'transparent',
                    zIndex: 1050,
                    borderRadius: `${pxTransform(45)} ${pxTransform(45)} 0 0`
                }}
            >
                <View
                    className={cx(
                        styles.container,
                        process.env.TARO_ENV === 'h5' ? styles.containerOfH5 : '',
                        'wz-plr-36'
                    )}
                >
                    <View className={cx(styles.tipsContainer, 'wz-mb-30 wz-plr-45 wz-ptb-51')}>
                        <View className={cx(styles.mainTitle, 'wz-fs-54 wz-text-center wz-mb-51')}>
                            {tipsData?.title || '发图小贴士'}
                        </View>
                        <View className={cx(styles.imgInfoMain, 'wz-pb-90')}>
                            {renderContent()}
                        </View>
                        {/* <View className={styles.whiteMask} /> */}
                    </View>

                    <View className={styles.uploadSection}>
                        {uploadOps &&
                            uploadOps?.length > 0 &&
                            uploadOps.map((uploadType, index) => {
                                const lastIndex = uploadOps.length - 1;
                                return (
                                    <View key={uploadType}>
                                        <View
                                            className={cx(
                                                styles.footerBtn,
                                                'wz-flex wz-col-center wz-row-center'
                                            )}
                                            onClick={() => {
                                                handleUpload(uploadType);
                                            }}
                                        >
                                            {UPLOADTYPEMAP[uploadType]}
                                        </View>
                                        {index !== lastIndex && <View className={styles.segment} />}
                                    </View>
                                );
                            })}
                    </View>
                    <View
                        className={cx(
                            styles.operationSection,
                            'wz-flex wz-col-center wz-row-center wz-mt-30'
                        )}
                        onClick={closeUploadPopup}
                    >
                        取消
                    </View>
                </View>
            </Popup>
        </Portal>
    );
};

TipImgUploadPopup.displayName = 'TipImgUploadPopup';

export default memo(TipImgUploadPopup);
