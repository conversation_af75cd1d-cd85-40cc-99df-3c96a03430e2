import type {IPicProps} from '../../../typings/upload';
import type {CapsulesToolsType} from '../../../store/triageStreamAtom/index.type';
import type {SceneTypeOfParams} from '../../../models/services/triageStream/sse/index.d';
export interface TipItem {
    type: 'text' | 'title' | 'img';
    value: string[];
}

type uploadType = 'album' | 'camera';

export interface TipImgUploadPopupProps {
    uploadOps?: uploadType[];
    onSelectedPics?: (preData: IPicProps[], {sceneType}: {sceneType: SceneTypeOfParams}) => void;
    tipsData: CapsulesToolsType['instruction'];
    sceneType: SceneTypeOfParams | undefined;
    open?: boolean;
    closeUploadPopup: () => void;
}
