.couponPopupContainer {
    width: 100%;
    box-sizing: border-box;
    background: #fff;
    border-radius: 63px 63px 0 0;
    overflow: hidden;

    .couponPopupCon {
        width: 100%;
        padding: 33px 51px;
        box-sizing: border-box;

        .couponPopupListTitle {
            color: #1f1f1f;
            font-family: PingFangSC-Medium;
            font-weight: bold;
        }

        .couponContainer {
            box-sizing: border-box;
            line-height: 1;
            border-radius: 27px 27px 0 0;
            overflow: hidden;

            .couponPrice {
                width: 246px;
                height: 246px;
                background: #fd503e;
                color: #fff;
                box-sizing: border-box;
                text-align: center;
                padding-top: 74px;

                .price {
                    font-size: 81px;
                    font-family: PingFangSC-Medium;
                    font-weight: bold;
                }

                .priceValue {
                    font-family: PingFangSC-Medium;
                    font-weight: bold;
                }
            }

            .couponCon {
                flex: 1;
                height: 246px;
                padding-left: 25px;
                padding-top: 68px;
                color: #fd503e;
                box-sizing: border-box;
                background: #fff1f0;

                .couponName {
                    font-family: PingFangSC-Medium;
                    font-weight: bold;
                }
            }

            .couponStatus {
                position: relative;
                width: 312px;
                height: 246px;
                font-size: 39px;
                background: #fff1f0;
                border-radius: -27px 0 0 -27px;
                color: #fd503e;
                font-family: PingFangSC-Medium;
                font-weight: bold;
                display: flex;
                align-items: center;
                justify-content: center;

                .dashed {
                    position: absolute;
                    left: 0;
                    top: 0px;
                    bottom: 0px;
                    border-left: 3px dashed rgba(227, 47, 27, 0.3);
                    z-index: 1;

                    &.no_bottom_gap {
                        bottom: -30px;
                    }

                    &::before {
                        content: ' ';
                        width: 30px !important;
                        height: 30px !important;
                        position: absolute;
                        top: -15px;
                        left: -15px;
                        border-radius: 100%;
                        background: #fff;
                        z-index: 2;
                    }

                    &::after {
                        content: ' ';
                        width: 8px !important;
                        height: 8px !important;
                        position: absolute;
                        top: -5px;
                        left: -5px;
                        border-radius: 100%;
                        background: #fff;
                        z-index: 2;
                    }
                }
            }
        }

        .couponDescCon {
            background: #fff9f9;
            border-radius: 0 0 27px 27px;
            box-sizing: border-box;

            .couponDescConItem {
                flex: 1;
                color: #858585;
                letter-spacing: 0;
            }

            .couponDescConIcon {
                height: 38px;
                width: 38px;
                margin-left: 87px;
            }
        }

        .usableCouponsContainer {
            margin-bottom: 72px;
        }
    }
}

.bottomInfo {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    height: 162px;
    background: #fff;
    padding: 23px 51px 25px 51px;
    box-sizing: border-box;

    .couponPrice {
        align-items: flex-end;
        font-family: PingFangSC-Medium;
        font-weight: bold;
        color: #1f1f1f;

        .couponPriceValue {
            color: #fd503e;
        }
    }

    .couponConfirmBtn {
        width: 312px;
        height: 114px;
        font-family: PingFangSC-Medium;
        font-weight: bold;
        text-align: center;
        line-height: 114px;
        color: #fff;
        background: #fd503e;
        border-radius: 66px;
        align-items: center;
        justify-content: center;

        .couponConfirmBtnLoading {
            width: 78px;
            height: 78px;
            background-image: url(https://med-fe.cdn.bcebos.com/wz-mini/ImFocusServiceV2/couponPopupLoading.png);
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            transform-origin: center center;
            animation: spin 2s linear infinite;
        }
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.couponListPop {
    .couponDialogWrapper {
        max-height: 1134px;
        z-index: 1011;
        display: flex;
        flex-direction: column;

        .dialogScroll {
            ::-webkit-scrollbar {
                width: 0;
                height: 0;
                color: transparent;
            }
        }

        .dialogBtn {
            font-family: PingFangSC-Medium;
        }
    }

    .couponListContent {
        .popItem {
            text-align: left;

            .title {
                font-family: PingFangSC-Medium;
                color: #1f1f1f;
                letter-spacing: 0;
                line-height: 69px;
            }

            .content {
                color: #1f1f1f;
                letter-spacing: 0;
                line-height: 69px;
            }
        }
    }
}
