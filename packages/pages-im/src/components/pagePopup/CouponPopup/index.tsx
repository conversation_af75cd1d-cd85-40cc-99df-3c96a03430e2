// import dayjs from 'dayjs';
import cx from 'classnames';
import { Popup } from '@baidu/wz-taro-tools-core';
import { View, Text } from '@tarojs/components';
import { WiseDownArrow, WiseCheckSelectedSolid, WiseSelected } from '@baidu/wz-taro-tools-icons';
import React, { FC, memo, useMemo, useCallback, ReactNode, useState, useEffect } from 'react';

import type { ICouponList } from '../../../typings/service';
import { formatPrice } from '../../../utils/generalFunction/price';
import { ubcCommonViewSend, ubcCommonClkSend } from '../../../utils/generalFunction/ubc';

import styles from './index.module.less';

interface IProps {
    calingLoading: boolean;
    defaultSelectCouponData: ICouponList | null;
    enableCouponList: ICouponList[];
    disableCouponList: ICouponList[];
    showCouponExplanation: (arg: ICouponList['description']) => void;
    changeSelectedCouponCallback: (couponData: ICouponList | null) => void;
    closeCallback?: () => void;
    sourceVal: string; // 页面来源
}

/**
 *
 * @description 优惠券选择弹窗
 * @param props
 * @returns
 */
const CouponPopup: FC<IProps> = props => {
    const {
        calingLoading,
        closeCallback,
        enableCouponList,
        disableCouponList,
        defaultSelectCouponData,
        showCouponExplanation,
        changeSelectedCouponCallback,
        sourceVal
    } = props;

    const [selectedCouponData, setSelectedCouponData] = useState<ICouponList | null>(null); // 选中的优惠券数据

    const genEndTime = useCallback((t: number) => {
        try {
            // return dayjs(t * 1000).format('YYYY年MM月DD日');
        } catch (err) {
            console.error('genEndTime 出错：', err);

            return '';
        }
    }, []);

    /**
     *
     * @description 打开优惠劵弹窗
     */
    const openExplanation = useCallback(
        (d: ICouponList) => {
            d?.usable === 1 && showCouponExplanation?.(d.description);
        },
        [showCouponExplanation]
    );

    /**
     *
     * @description 生成优惠券
     */
    const genCouponItem = useCallback(
        (itemData: ICouponList, rightNode: ReactNode) => {
            return (
                <View key={itemData.id}>
                    <View className={cx(styles.couponContainer, 'wz-flex wz-mt-24')}>
                        <View className={cx(styles.couponPrice, 'wz-fs-36')}>
                            <View className={cx(styles.price)}>
                                <Text className={cx(styles.priceUnit, 'wz-fs-48')}>￥</Text>
                                {itemData.salePrice && (
                                    <Text className={cx(styles.priceValue)}>{formatPrice(itemData.salePrice)}</Text>
                                )}
                            </View>
                            {itemData.fullPrice && (
                                <View className={cx(styles.couponLimit, 'wz-mt-18')}>
                                    满{formatPrice(itemData.fullPrice)}可用
                                </View>
                            )}
                        </View>
                        <View className={cx(styles.couponCon)}>
                            <View className={cx(styles.couponName, 'wz-fs-48')}>{itemData.name}</View>
                            {itemData.endTime && (
                                <View className={cx(styles.couponDesc, 'wz-fs-36 wz-mt-27')}>
                                    有效期至{genEndTime(itemData.endTime)}
                                </View>
                            )}
                        </View>
                        <View className={cx(styles.couponStatus)}>{rightNode}</View>
                    </View>
                    <View
                        className={cx(styles.couponDescCon, 'wz-fs-36 wz-ptb-27 wz-plr-36 wz-flex')}
                        onClick={() => openExplanation(itemData)}
                    >
                        <View className={cx(styles.couponDescConItem)}>本券仅线上咨询使用，一个订单限用一张</View>
                        {itemData?.usable === 1 ? (
                            <WiseDownArrow color='red' size={23} className={styles.icon} />
                        ) : null}
                    </View>
                </View>
            );
        },
        [genEndTime, openExplanation]
    );

    /**
     *
     * @description 点击可用优惠券回调
     */
    const clickEnableCoupon = useCallback(
        (item: ICouponList) => {
            if (item.id === selectedCouponData?.id) {
                setSelectedCouponData(null);
            } else {
                setSelectedCouponData(item);
            }
        },
        [selectedCouponData?.id]
    );

    /**
     *
     * @description 确认选择优惠券 & 关闭弹窗
     */
    const confirmSeletedCoupon = useCallback(() => {
        changeSelectedCouponCallback?.(selectedCouponData);
        // 选择优惠劵后，执行动态算价，算价成功后由父组件进行关闭弹窗；
        // closeCallback?.();

        ubcCommonClkSend({
            value: `ImFocusService_v2_couponPopup_confirm_${sourceVal}`
        });
    }, [changeSelectedCouponCallback, selectedCouponData, sourceVal]);

    const closePopup = useCallback(() => {
        closeCallback?.();
    }, [closeCallback]);

    /**
     *
     * @description 生成可用优惠券列表
     */
    const genEnableCouponList = useMemo(() => {
        if (!enableCouponList.length) {
            return <></>;
        }

        return (
            <View className={styles.usableCouponsContainer}>
                <View className={cx(styles.couponPopupListTitle, 'wz-fs-45 wz-mb-54')}>
                    可用优惠券({enableCouponList.length})
                </View>
                {enableCouponList.map(i => {
                    return genCouponItem(
                        i,
                        <View
                            onClick={() => clickEnableCoupon(i)}
                            style={{
                                display: 'inherit'
                            }}
                        >
                            <View className={styles.dashed} />
                            {selectedCouponData?.id === i.id && <WiseCheckSelectedSolid color='#fd503e' size={84} />}
                            {selectedCouponData?.id !== i.id && <WiseSelected color='#fd503e' size={84} />}
                        </View>
                    );
                })}
            </View>
        );
    }, [clickEnableCoupon, enableCouponList, genCouponItem, selectedCouponData]);

    /**
     *
     * @description 生成不可用优惠券列表
     */
    const genDisableCouponList = useMemo(() => {
        // TODO 暂时不展示不可用优惠券
        if (!disableCouponList.length) {
            return <></>;
        }

        return (
            <>
                <View className={cx(styles.couponPopupListTitle, 'wz-fs-45 wz-mb-54')}>
                    不可用优惠券({disableCouponList.length})
                </View>
                {disableCouponList.map(i => {
                    return genCouponItem(
                        i,
                        <>
                            <View className={styles.dashed} key={i.id} />
                            暂不可用
                        </>
                    );
                })}
            </>
        );
    }, [genCouponItem, disableCouponList]);

    /**
     *
     * @description 默认选择优惠券
     */
    useEffect(() => {
        if (defaultSelectCouponData) {
            enableCouponList.some(i => {
                if (i.id === defaultSelectCouponData?.id) {
                    setSelectedCouponData(i);

                    return true;
                }

                return false;
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [defaultSelectCouponData, enableCouponList]);

    useEffect(() => {
        ubcCommonViewSend({
            value: `ImFocusService_v2_couponPopup_${sourceVal}`
        });
    }, [sourceVal]);

    return (
        <Popup
            open
            rounded
            placement='bottom'
            title='选择优惠'
            titleStyle={{
                backgroundColor: '#FFF',
                border: 'none',
                borderBottom: 'none'
            }}
            style={{ height: '75%', zIndex: 999 }}
            onClose={closePopup}
        >
            <Popup.Close />
            <Popup.Backdrop
                open
                style={{
                    zIndex: 10,
                    backgroundColor: 'rgba(0, 0, 0, .6)'
                }}
            />
            <View className={styles.couponPopupContainer}>
                <View className={styles.couponPopupCon}>
                    {genEnableCouponList}
                    {genDisableCouponList}
                </View>
            </View>
            <Popup.Button>
                <View className={styles.bottomInfo}>
                    <View className={cx(styles.couponPrice, 'wz-fs-45 wz-flex')}>
                        <View className={cx(styles.couponPriceDes, 'wz-mr-27')}>优惠可减</View>
                        <View className={cx(styles.couponPriceValue)}>
                            <Text className='wz-fs-42'>￥</Text>
                            <Text className='wz-fs-57'>
                                {selectedCouponData?.salePrice ? formatPrice(selectedCouponData?.salePrice) : '0'}
                            </Text>
                        </View>
                    </View>
                    <View
                        className={cx(styles.couponConfirmBtn, 'wz-fs-48 wz-ml-24 wz-flex')}
                        onClick={confirmSeletedCoupon}
                    >
                        {calingLoading ? <View className={cx(styles.couponConfirmBtnLoading)} /> : <>确认</>}
                    </View>
                </View>
            </Popup.Button>
        </Popup>
    );
};

export default memo(CouponPopup);
