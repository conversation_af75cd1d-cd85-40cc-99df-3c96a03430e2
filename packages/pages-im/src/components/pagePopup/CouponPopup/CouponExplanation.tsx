import cx from 'classnames';
import {pxTransform} from '@tarojs/taro';
import React, {memo, FC, useMemo, useState, useEffect, useCallback} from 'react';
import {isArray} from '@baidu/vita-utils-shared';
import {Dialog, Button} from '@baidu/wz-taro-tools-core';
import {View, ScrollView} from '@tarojs/components';

import type {ICouponList} from '@typings/service';
import type {ModalContentProps, contentItem} from '../../../typings/im';

import styles from './index.module.less';

interface IProps {
    open: boolean;
    data: ICouponList['description'] | ModalContentProps | null;
    onClose: () => void;
}

/**
 *
 * @description 优惠劵信息解释弹层
 * @param props
 * @returns
 */
const CouponExplanation: FC<IProps> = props => {
    const {open: decPopShow, onClose, data} = props;
    const [modalContentData, setModalContentData] = useState([]);

    // 原来券规则的数据是数组，扩展性不强，数据改为可扩展性强的数组数据，里面的文案使用对象type和value的形式
    useEffect(() => {
        // 兼容老数据格式
        if (data && isArray(data)) {
            const compArrData: contentItem[] = [];
            data.map(descItem => {
                compArrData.push({
                    type: 'title',
                    value: descItem.title
                });
                descItem.list.map(item => {
                    compArrData.push({
                        type: 'text',
                        value: item
                    });
                });
            });
            setModalContentData(compArrData);
        } else {
            // 新数据格式
            setModalContentData(data?.content || []);
        }
    }, [data]);

    const handleClose = useCallback(() => {
        if (
            (data?.buttonList && data?.buttonList[0]?.interaction === 'close') ||
            !data?.buttonList
        ) {
            onClose?.();
        }
    }, [onClose, data?.buttonList]);

    const renderDialogContent = useMemo(() => {
        if (modalContentData) {
            return modalContentData.map((item, index) => {
                const {type, value} = item;

                return (
                    <View className={cx(styles.popItem)} key={index}>
                        {type === 'title' ? (
                            <View className={cx('wz-fs-42', 'wz-mb-27', styles.title)}>
                                {value}
                            </View>
                        ) : (
                            <View className={cx('wz-fs-42', 'wz-mb-27', styles.content)}>
                                {value}
                            </View>
                        )}
                    </View>
                );
            });
        }

        return <></>;
    }, [modalContentData]);

    return (
        <Dialog
            open={decPopShow}
            className={styles.couponListPop}
            onClose={onClose}
            style={{width: '87%', borderRadius: pxTransform(63)}}
        >
            <Dialog.Header>{data?.title || '优惠券使用规则'}</Dialog.Header>
            <Dialog.Content>
                <View className={styles.couponDialogWrapper}>
                    <ScrollView
                        scrollY
                        style={{height: pxTransform(1134)}}
                        className={styles.dialogScroll}
                    >
                        <View className={styles.couponListContent}>{renderDialogContent}</View>
                    </ScrollView>
                </View>
            </Dialog.Content>
            <Dialog.Actions variant='rounded'>
                <Button
                    onClick={() => {
                        handleClose?.();
                    }}
                    className={cx('wz-fs-48 wz-fw-500', styles.dialogBtn)}
                >
                    {(data?.buttonList && data?.buttonList[0]?.value) || '我知道了'}
                </Button>
            </Dialog.Actions>
        </Dialog>
    );
};

export default memo(CouponExplanation);
