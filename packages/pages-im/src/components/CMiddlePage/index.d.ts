import {CSSProperties, ReactNode} from 'react';

export interface BaseComponent {
    className?: string;
    customStyle?: string | CSSProperties;
    children?: ReactNode | ReactNode[];
}

export interface CMiddlePageProps extends BaseComponent {
    /**
     * 背景图
     */
    image?: string;

    /**
     * 图片自定义样式
     */
    imageStyle?: CSSProperties;

    /**
     * 文字
     */
    text?: string;

    /**
     * 文字列表
     */
    textList?: string[];

    /**
     * 是否展示按钮
     */
    showButton?: boolean;

    /**
     * 按钮文案
     */
    buttonText?: string;

    /**
     * 按钮点击
     */
    onButtonClicked?: (event) => void;
}
