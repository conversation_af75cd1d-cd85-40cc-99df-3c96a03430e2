import {FC, memo} from 'react';
import {View, Image} from '@tarojs/components';
import {CMiddlePageProps} from './index.d';
import './index.less';

const CMiddlePage: FC<CMiddlePageProps> = (props: CMiddlePageProps) => {
    const {
        image = 'https://med-fe.cdn.bcebos.com/common/complaint/net-error.png',
        imageStyle = {},
        text = '网络不给力，请稍后重新尝试',
        textList = [],
        showButton = true,
        buttonText = '重新加载',
        onButtonClicked,
        customStyle
    } = props;

    const handleTap = event => {
        onButtonClicked && onButtonClicked(event);
    };

    return (
        <View className='middle-page' style={customStyle}>
            <Image className='middle-page__image' src={image} style={imageStyle} />
            {text && !textList.length ? <View className='middle-page__text'>{text}</View> : null}
            {textList && textList.length ? (
                <View className='middle-page__text'>
                    {textList.map((item, idx) => {
                        return (
                            <View className='c-tac' key={idx}>
                                {item}
                            </View>
                        );
                    })}
                </View>
            ) : null}

            {showButton ? (
                <View className='middle-page__btn' onClick={e => handleTap(e)}>
                    {buttonText}
                </View>
            ) : null}
        </View>
    );
};

export default memo(CMiddlePage);
