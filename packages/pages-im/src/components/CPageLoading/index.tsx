import {FC, memo} from 'react';
import {View, Image} from '@tarojs/components';
import {CPageLoadingProps} from './index.d';

import styles from './index.module.less';

const CPageLoading: FC<CPageLoadingProps> = (props: CPageLoadingProps) => {
    const {backgroundColor, zIndex, children} = props;

    const wrapStyle = {
        backgroundColor,
        zIndex
    };

    return (
        <View className={styles.wrap} style={wrapStyle}>
            <View className={styles.content}>
                <View className={styles.imageWrap}>
                    <Image
                        className={styles.image}
                        src='https://ss1.baidu.com/6ONXsjip0QIZ8tyhnq/it/u=2669382790,1249805892&fm=179&app=35&f=GIF?w=100&h=50&s=FF8838624B8367744EFDF0CA0000E0B1'
                    />
                </View>
                {children}
            </View>
        </View>
    );
};

CPageLoading.defaultProps = {
    backgroundColor: '#fff',
    zIndex: 99
};

export default memo(CPageLoading);
