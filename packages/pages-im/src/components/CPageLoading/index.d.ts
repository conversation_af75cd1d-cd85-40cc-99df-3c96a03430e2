import type { ComponentClass, ReactNode, CSSProperties } from 'react';

export interface BaseComponent {
    className?: string;
    customStyle?: string | CSSProperties;
    children?: ReactNode | ReactNode[];
}

export interface CPageLoadingProps extends BaseComponent {

    /**
     * 背景色
     */
    backgroundColor?: string;

    /**
     * zIndex
     */
    zIndex?: string | number;
}

declare const CPageLoading: ComponentClass<CPageLoadingProps>;

export default CPageLoading;
