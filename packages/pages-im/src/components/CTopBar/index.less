.top-bar-wrapper {
    &.green {
        .top-bar,
        .blank {
            background: linear-gradient(to right, #00cfa3, #05cfcd, #00d3ea);
        }
    }

    &.greentj {
        .top-bar,
        .blank {
            background: linear-gradient(134deg, #489af6 0%, #82cbff 100%);
        }
    }

    &.pinkhpv {
        .top-bar,
        .blank {
            background: linear-gradient(134deg, #ff6d8a 0%, #fc7967 100%);
        }
    }

    &.grey {
        .top-bar,
        .blank {
            background: #f5f5f5;
        }
    }

    &.vacc {
        .top-bar,
        .blank {
            background: #fd503e;
        }
    }

    &.exam {
        .top-bar,
        .blank {
            background: linear-gradient(134deg, #489af6 0%, #82cbff 100%);
        }
    }

    &.red {
        .top-bar,
        .fixedbg,
        .blank {
            background-image: linear-gradient(90deg, #fd503e 0%, #ff8372 100%);
        }
    }

    &.yellow {
        .top-bar,
        .fixedbg,
        .blank {
            background-image: linear-gradient(90deg, #fe7a4b 0%, #ffa471 54%, #fdca7c 100%);
        }
    }

    &.pink {
        .top-bar,
        .fixedbg,
        .blank {
            background-image: linear-gradient(90deg, #ff88a1 0%, #ffbaca 100%);
        }
    }

    &.white {
        .top-bar {
            background: #fff;
        }
    }

    &.blue {
        .top-bar,
        .blank {
            background: linear-gradient(to right, #489af6, #82cbff);
        }
    }

    &.greenToBlue {
        .top-bar,
        .blank {
            background: linear-gradient(134deg, #00cfa3 0%, #05cfcd 61%, #00d3ea 100%);
        }
    }

    &.f5 {
        .top-bar,
        .blank {
            background: linear-gradient(180deg, #fff 0%, #f5f5f5 100%);
        }
    }

    &.transparent {
        .top-bar,
        .blank {
            background: transparent;
        }
    }

    .top-bar {
        position: fixed;
        top: 0;
        width: 100%;
        max-width: 1247px;
        box-sizing: border-box;

        &.no-fix-wid {
            .bar {
                width: 100%;
            }
        }

        .bar {
            align-items: center;

            .title {
                font-size: 54px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-weight: 700;
                text-align: center;
                max-width: var(--big-max-width, 780px);
            }

            .subtitle {
                color: #1f1f1f;
            }

            .left {
                text-align: left;
                padding-left: 33px;
                white-space: nowrap;

                .icon {
                    display: inline-block;
                    vertical-align: middle;

                    &-blank {
                        width: 150px;
                        height: 150px;

                        &-bg {
                            width: 96px;
                            height: 96px;
                            margin: 0 auto;
                            margin-top: 50%;
                            transform: translate(-9px, -50%);
                        }
                    }

                    &-bg {
                        background: rgba(0, 0, 0, 0.15);
                        border-radius: 100%;
                    }

                    &-none {
                        visibility: hidden;
                    }
                }
            }
        }
    }

    .fixedbg {
        position: fixed;
        left: 0;
        right: 0;
    }

    .title-icon {
        width: 60px;
        height: 60px;
        display: inline-block;
        vertical-align: text-top;
        background-size: 100% 100%;
        margin-right: 6px;
    }

    .menu-swan-wrapper {
        width: 120px;
        height: 96px;
        border-radius: 48px;
        background: #FFFFFF4D;
    }

    .menu-wrapper {
        width: 120px;
        height: 96px;
        border-radius: 48px;
        background: #FFFFFF4D;
    }
}
