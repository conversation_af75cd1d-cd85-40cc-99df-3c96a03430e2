import type { ComponentClass, ReactNode, CSSProperties } from 'react';

import { UrlParamsType } from '../../typings';
import { menuItem } from '../CMenu/index.d';


export interface BaseComponent {
    className?: string;
    customStyle?: string | CSSProperties;
    children?: ReactNode | ReactNode[];
}

export interface CTopBarProps extends BaseComponent {

    /**
     * 标题
     */
    title?: string;

    /**
     * 副标题
     */
    subtitle?: string;

    /**
     * 副标题跳转地址
     */
    subtitleUrl?: string;

    subTitleSlot?: ReactNode;

    /**
     * title自定义节点
     */
    titleSlot?: ReactNode;

    /**
     * title自定义节点-不占位
     */
    titleCustomSlot?: ReactNode;

    /**
     * title右上角自定义节点
     */
    titleRightSlot?: ReactNode;

    /**
     * h5右侧菜单
     */
    menu?: menuItem[];

    /**
     * 隐藏标题
     */
    hideTitle?: boolean;

    /**
     * 页面是否在TabBar上
     */
    isInTabBar?: boolean;

    /**
     * bar高度
     */
    barHeight?: number;

    /**
     * 文字颜色
     */
    textColor?: string;

    /**
     * 延迟设置文字颜色
     */
    delaySetHeaderColor?: boolean;

    /**
     * 延迟进行设备兼容
     */
    delayCompatDevices?: boolean;

    /**
     * 层级
     */
    zIndex?: number | string;

    /**
     * 是否需要占位
     */
    blank?: boolean;

    /**
     * 占位背景
     */
    blankBg?: string;

    /**
     * bar背景色
     */
    barBg?: string;

    /**
     * 插槽高度
     */
    slotHeight?: number | string;

    /**
     * 吸顶背景延伸高度
     */
    fixedBgHeight?: number | string;

    /**
     * 吸顶背景延伸层级 （页面内容设置position:relative z-index 来盖住延伸背景）
     */
    fixedBgZIndex?: number | string;

    /**
     * 是否吸顶
     */
    isFixed?: boolean;

    /**
     * 是否隐藏Home
     */
    hideHome?: boolean;

    /**
     * 是否隐藏回退按钮
     */
    hideBack?: boolean;

    /**
     * 标题文案左侧图标
     */
    icon?: string;

    /**
     * 是否阻止返回
     */
    backDetain?: boolean;

    /**
     * 主页地址
     */
    homeUrlParams?: UrlParamsType;

    /**
     * icon是否带背景
     */
    iconBg?: boolean;

    /**
     * 是否强制显示返回按钮
     */
    isForceBack?: boolean;

    /**
     * 是否隐藏TopBar
     */
    isHideHeader?: boolean;

    /**
     * 左侧标题
     */
    titleLeftSlot?: ReactNode;

    /**
     * topBar高度变更
     */
    topBarHeightChanged?: (height: number) => void;

    /**
     * ???
     */
    introPageHome?: (eventName: string) => void;

    /**
     * 阻止返回回调
     */
    onBackDetain?: (eventName: string, navigateBack: () => void, navigateHome: () => void) => void;

    /**
     * 返回按钮的回调
     */
    navigateCallBack?: () => void;
}

export interface IConfig {
    statusBarHeight: number;
    navigationBarHeight: number;
    navigateBackShow: boolean;
    isHigherNavigate: boolean;
    hidePageHeader: boolean;
    showHome: boolean;
    barPageClass: string;
    env: string | undefined;
    backStyle: {
        marLeft: number | string;
        marTop: number | string;
    };
    backToHome: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface TextLineState {}

declare const CTopBar: ComponentClass<CTopBarProps>;

export default CTopBar;
