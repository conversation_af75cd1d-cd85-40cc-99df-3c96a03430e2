import {useCallback} from 'react';
import {showToast} from '../../../utils/customShowToast';
import {useCommonPay} from '../../../../../utils-shared/pay/useCommonPay';
import {useGetSessionId} from '../../../hooks/triageStream/pageDataController';
import {getUserBizActionReq} from '../../../models/services/triageStream';
import {ubcCommonClkSend} from '../../../utils/generalFunction/ubc';
import {parseURL, assembleUrl} from '../../../utils/generalFunction/urlRelated';
import {useMsgDataSetController} from '../../../hooks/triageStream/dataController';
import type {MsgId, Qid} from '../../../typings';
import type {undirectService} from '../index.d';
import {navigate} from '../../../utils/basicAbility/commonNavigate';
import {ButtonConfig, InteractionInfo, ModalControl} from './usePreCheckOrderModal';

interface IGetMakeOrderRelatedInfoType extends ModalControl {
    msgId: MsgId;
    skuList?: undirectService[];
    qid?: Qid;
}

// 定义showToast的公共配置
const TOAST_CONFIG = {
    icon: 'none',
    duration: 3000
};

export const useGetMakeOrderRelatedInfo = (args: IGetMakeOrderRelatedInfoType) => {
    const {msgId, skuList, qid, openModal, closeModal} = args;

    const {confirmPay} = useCommonPay();
    const sessionId = useGetSessionId();
    const {updateMsgData} = useMsgDataSetController({msgId});

    // 用户支付取消时，刷新 sku 数据
    const onCancelPay = useCallback(async () => {
        const params = {
            bizActionType: 'switchCoupon' as const,
            chatData: {
                sessionId: sessionId || ''
            },
            bizActionData: {
                switchCouponInfo: {
                    msgId,
                    couponId: 0,
                    serviceType: 'undirectService',
                    qid
                }
            }
        };
        const [err, data] = await getUserBizActionReq<'switchCoupon'>(params);
        if (!err) {
            data?.data?.message && updateMsgData(data?.data?.message[0]);
        }
    }, [msgId, qid, sessionId, updateMsgData]);

    /**
     *
     * @description 确认支付
     */
    const makeOrder = useCallback(
        (skuDataToOrder: undirectService) => {
            // eslint-disable-next-line no-async-promise-executor
            return new Promise(async (resolve, reject) => {
                try {
                    const url = skuDataToOrder?.btn?.interactionInfo?.url || '';
                    const params = skuDataToOrder?.btn?.interactionInfo?.params;

                    if (!url) {
                        reject(new Error('makeOrder 下单 url 参数错误'));
                    }

                    const {params: paramsOfUrl, pathname} = parseURL(url);

                    const reqParams = {
                        ...paramsOfUrl
                    };
                    const resUrl = assembleUrl(pathname, reqParams);

                    const data = await confirmPay({
                        interactionInfo: {
                            url: resUrl,
                            params
                        },
                        onCancelPay,
                        // 免费服务时，不展示正在支付中的文案，改为展示正在前往候诊页
                        loadingText: !skuDataToOrder?.skuId ? '正在前往候诊页' : '正在支付中'
                    });

                    // 处理预检逻辑
                    if (data?.actionInfo) {
                        handleInteraction(data.actionInfo);
                    }

                    if (data?.isExpertUnavailable) {
                        showToast({
                            ...TOAST_CONFIG,
                            title: '名额已抢完, 您可继续选择该医生的其他服务'
                        });
                    }

                    if (data?.isUserUnavailable) {
                        showToast({
                            ...TOAST_CONFIG,
                            title: '本周名额已用尽, 您可继续选择该医生的其他服务'
                        });
                    }

                    resolve(null);

                    skuDataToOrder?.skuId &&
                        ubcCommonClkSend({
                            value: `streamFocusService_${skuDataToOrder?.skuId}`,
                            ext: {
                                product_info: {
                                    skuList: skuList?.map(skuItem => {
                                        return skuItem?.skuId;
                                    })
                                }
                            }
                        });
                } catch (err) {
                    reject(err);
                }
            });
        },
        [confirmPay, onCancelPay, skuList]
    );

    // 处理交互信息
    const handleInteraction = useCallback(
        (interactionInfo: InteractionInfo) => {
            if (!interactionInfo) return;

            const {interaction, interactionInfo: info} = interactionInfo;

            switch (interaction) {
                case 'modal':
                    // 处理模态框
                    if (info?.modalContent) {
                        closeModal();
                        // 打开对话框
                        openModal({
                            content: info.modalContent.title,
                            buttons: info.modalContent.buttonList, // 传递按钮列表
                            onButtonClick: handleButtonClick
                        });
                    }
                    break;

                case 'toast':
                    closeModal();
                    // 处理toast提示
                    if (info?.title) {
                        showToast({
                            ...TOAST_CONFIG,
                            title: info.title
                        });
                    }
                    break;

                case 'request':
                    // 处理请求
                    if (info?.url) {
                        const param = {
                            btn: {
                                interactionInfo: {
                                    url: info?.url,
                                    ...(info?.params && {params: info?.params})
                                }
                            }
                        };
                        makeOrder(param);
                    }
                    break;

                case 'openLink':
                    // 处理打开链接
                    if (info?.url) {
                        navigate({
                            openType: 'navigate',
                            url: info.url
                        });
                    }
                    break;

                case 'close':
                    // 处理关闭对话框
                    closeModal();
                    break;

                default:
                    console.warn('未知的交互类型:', interaction);
                    break;
            }
        },
        [closeModal, openModal, makeOrder]
    );

    // 处理按钮点击
    const handleButtonClick = useCallback(
        (button: ButtonConfig | undefined) => {
            if (!button) {
                closeModal();
                return;
            }

            // 关闭当前对话框
            closeModal();

            // 处理按钮的交互
            handleInteraction({
                interaction: button.interaction,
                interactionInfo: button.interactionInfo
            });
        },
        [closeModal, handleInteraction]
    );

    /**
     *
     * @description 登录成功后，进行下单操作; 二次校验登录状态
     */
    const loginSuccessfulCallback = useCallback(
        (skuForOrder: undirectService) => {
            // eslint-disable-next-line no-async-promise-executor
            return new Promise(async (resolve, reject) => {
                try {
                    await makeOrder(skuForOrder);
                    resolve(null);
                } catch (err) {
                    reject(err);
                }
            });
        },
        [makeOrder]
    );

    return {
        loginSuccessfulCallback
    };
};
