import {View} from '@tarojs/components';
import cx from 'classnames';

import styles from './index.module.less';

const ExpertSkeleton = () => {
    return (
        <View className={cx(styles.expertSkeletonWrapper, 'wz-flex, wz-col-top')}>
            <View className={cx(styles.avatarSkeleton, styles.skeletonBg, 'wz-mb-15')}></View>
            <View className={cx(styles.doctorCardInfoWrapper, 'wz-ml-18')}>
                <View className='wz-flex, wz-row-between'>
                    <View>
                        <View
                            className={cx(styles.docNameSkeleton, styles.skeletonBg, 'wz-mb-27')}
                        ></View>
                        <View
                            className={cx(styles.hosInfoSkeleton, styles.skeletonBg, 'wz-mb-27')}
                        ></View>
                        <View
                            className={cx(styles.goodAtSkeleton, styles.skeletonBg, 'wz-mb-27')}
                        ></View>
                    </View>
                    <View className={cx(styles.btnSkeleton, styles.skeletonBg)}></View>
                </View>
                <View className={cx(styles.tipsSkeleton, styles.skeletonBg)}></View>
            </View>
        </View>
    );
};

export default ExpertSkeleton;
