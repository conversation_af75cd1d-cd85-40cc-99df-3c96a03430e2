/*
 * @Author: renyi03
 * @Description: 服务详情弹层
 */

import type {undirectServiceUpdatePayload} from '../../index.d';
import {undirectService} from '../../../index.d';

export interface IProps {
    selectSku?: undirectService;
    needLogin?: boolean;
    disabledShowFooter?: boolean;
    isLogin?: boolean;
    updateCollectedInfoAndSku: (type: string, payload?: undirectServiceUpdatePayload) => void;
}
