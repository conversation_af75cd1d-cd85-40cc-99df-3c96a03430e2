/*
 * @Author: banxiaoke
 * @Description: 非定向服务列表头部组件
 */
import cx from 'classnames';
import {View, Text, Image} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import {memo, useState, useEffect, useCallback, useMemo, useRef} from 'react';

import type {TopArea} from '../../index.d';

import styles from './ServiceTitle.module.less';

interface ServiceTitleProps {
    subText: string | undefined;
    topArea: TopArea | undefined;
    titleImg?: string;
    displayType?: string;
    serviceTip?: string;
    title?: string;
    leftIcon?: string;
    tagArr?: string[];
    isRenderCombine?: boolean;
}
const fallback =
    'https://ss1.baidu.com/6ONXsjip0QIZ8tyhnq/it/u=343652663,2298673057&fm=179&app=35&f=PNG?w=144&h=144&s=C8011F7047775A21364C71D9030050A1';
let timer;
const setTimeoutTime = 2500;

const ServiceTitle = (props: ServiceTitleProps) => {
    const {subText = '', topArea, titleImg, serviceTip, tagArr, isRenderCombine, leftIcon} = props;
    const {
        doctorDisplayArea: {avatars = [], words, isCarousel}
    } = topArea || {doctorDisplayArea: {}};

    const [showTrans, setShowTrans] = useState(true);
    const [curIndex, setCurIndex] = useState(8);
    const [showList, setShowList] = useState<string[]>([]);
    const [width] = useState(isRenderCombine ? 17 : 20);
    const carouselNumRef = useRef(isRenderCombine ? 4 : 5);

    const carouselLoop = useCallback(
        newVal => {
            timer = setTimeout(() => {
                const isBoundry = newVal === avatars.length;
                setCurIndex(isBoundry ? 0 : curIndex + 1);
                setShowTrans(!isBoundry);
            }, setTimeoutTime);
        },
        [curIndex, avatars.length]
    );

    useEffect(() => {
        // 清除不可点击卡片的轮播，对新卡进行轮播处理
        timer && clearTimeout(timer);
        isCarousel && carouselLoop(curIndex);

        return () => {
            timer && clearTimeout(timer);
        };
    }, [carouselLoop, curIndex, isCarousel]);

    useEffect(() => {
        const carouselNum = carouselNumRef.current;
        if (avatars.length >= carouselNum && isCarousel) {
            const mergeList = avatars.concat(avatars.slice(0, carouselNum));
            setShowList(mergeList);
        } else {
            avatars.length > 0 && setShowList(avatars.slice(0, carouselNum - 1));
        }
    }, [avatars, isCarousel]);

    const loopStyle = useMemo(() => {
        return {
            transform: `translateX(${-curIndex * width * (2 / 3)}px)`,
            transition: showTrans ? 'all .5s cubic-bezier(0.2, 0, 0.2, 1)' : 'none'
        };
    }, [curIndex, showTrans, width]);

    // 获取服务标题
    const getServiceTitle = useCallback(() => {
        if (!titleImg) {
            return null;
        }

        if (leftIcon) {
            return (
                <View className={cx('wz-flex wz-col-center wz-pb-33')}>
                    <WImage
                        className={cx(styles.titleLeftIcon, 'wz-mr-24')}
                        src={leftIcon}
                        mode='aspectFit'
                    />
                    <View className={cx(styles.titleText, 'wz-fs-57')}>{titleImg}</View>
                </View>
            );
        }

        // 组合卡标题为文案
        if (isRenderCombine) {
            return <View className={cx(styles.titleText, 'wz-fs-54')}>{titleImg}</View>;
        }

        return <WImage className={cx(styles.titleImg, 'wz-mr-27')} src={titleImg} />;
    }, [isRenderCombine, leftIcon, titleImg]);

    return (
        <>
            <View className={cx(styles.textColor)}>
                <View className={cx(styles.title, 'wz-flex wz-fs-42 wz-taro-ellipsis wz-mb-33')}>
                    {getServiceTitle()}
                    {subText && (
                        <View className={styles.title}>
                            <Text className='wz-pr-18'>{subText}</Text>
                        </View>
                    )}
                    {tagArr?.map((url, index) => {
                        return (
                            <Image
                                key={index}
                                className={cx(styles.tagImg, 'wz-ml-12')}
                                src={url}
                            />
                        );
                    })}
                </View>
                {showList && showList.length > 0 && (
                    <View className={cx(styles.textColor, 'wz-fs-42 wz-flex wz-mb-18')}>
                        <View
                            className={cx(
                                styles.rotation,
                                isRenderCombine ? styles.rotationCombine : ''
                            )}
                            style={{
                                width: `${width * carouselNumRef.current}px`
                            }}
                        >
                            <View
                                className={styles.rotationContainer}
                                style={isCarousel ? loopStyle : {}}
                            >
                                {showList &&
                                    showList.length > 0 &&
                                    showList.map((item, index) => {
                                        return (
                                            <View
                                                key={index}
                                                className={cx(styles.expertAvatar)}
                                                style={
                                                    isCarousel
                                                        ? {
                                                            opacity:
                                                                  index >= curIndex &&
                                                                  index - curIndex <
                                                                      carouselNumRef.current
                                                                      ? 1
                                                                      : 0,
                                                            transition: showTrans
                                                                ? 'all .5s cubic-bezier(0.2, 0, 0.2, 1)'
                                                                : 'none',
                                                            width: `${width}px`,
                                                            display:
                                                                  index >= curIndex &&
                                                                  index - curIndex ===
                                                                      carouselNumRef.current - 1
                                                                      ? 'none'
                                                                      : 'block',
                                                            marginLeft: `${-width * (1 / 3)}px`
                                                        }
                                                        : {
                                                            width: `${width}px`,
                                                            marginLeft: `${-width * (1 / 3)}px`,
                                                            display: 'block'
                                                        }
                                                }
                                            >
                                                <WImage
                                                    key={index}
                                                    round
                                                    className={cx(
                                                        styles.expertAvatar,
                                                        isRenderCombine
                                                            ? styles.expertAvatarCombine
                                                            : ''
                                                    )}
                                                    src={item || fallback}
                                                    fallback={<WImage src={fallback} />}
                                                />
                                            </View>
                                        );
                                    })}
                            </View>
                        </View>
                        <Text
                            className={cx(
                                'wz-ml-21',
                                isRenderCombine ? styles.onlineDocCombine : styles.onlineDoc
                            )}
                        >
                            {words}
                        </Text>
                    </View>
                )}

                {serviceTip && <View className='wz-pb-18 wz-fs-42 wz-pt-21'>{serviceTip}</View>}
            </View>
        </>
    );
};
export default memo(ServiceTitle);
