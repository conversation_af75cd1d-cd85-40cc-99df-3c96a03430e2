.textColor {
    color: #858585;
    position: relative;
}

.title {
    display: flex;
    align-items: center;
}

.titleLeftIcon {
    width: 12px;
    height: 57px;
}

.mainText {
    color: #1d1919;
    font-weight: 700;
    font-style: italic;
}

.expertAvatar {
    width: 63px;
    height: 63px;
    border: 4px solid #fff;
    border-radius: 32px;
    box-sizing: border-box;
}

.expertAvatarCombine {
    width: 56px;
    height: 56px;
}

.rotation {
    position: absolute;
    left: 21px;
    height: 63px;
    width: 168px;
}

.rotationCombine {
    left: 18px;
}

.rotationContainer {
    display: flex;
    align-items: center;
}

.onlineDoc,
.onlineDocCombine {
    padding-left: 200px;
    height: 67px;
    line-height: 67px;
}

.onlineDocCombine {
    padding-left: 142px;
    color: #848691;
}

.titleImg {
    width: 270px;
    height: 48px;
}

.titleText {
    font-weight: 600;
    color: #000311;
}

.tagImg {
    width: 180px;
    height: 54px;
}
