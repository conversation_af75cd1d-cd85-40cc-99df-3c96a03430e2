import cx from 'classnames';
import {View, Text, Image} from '@tarojs/components';
import {memo, FC, useMemo, useCallback, useState, useEffect} from 'react';
import {Tag, Popup} from '@baidu/wz-taro-tools-core';
import {<PERSON>R<PERSON>Arrow, WiseDownArrow} from '@baidu/wz-taro-tools-icons';
import {HButton} from '@baidu/health-ui';
import {pxTransform, eventCenter} from '@tarojs/taro';
import {Price, CLoginButton, Portal} from '@baidu/vita-ui-cards-common';

import {undirectService} from '../index.d';
import {isEmpty} from '../../../utils';
import {showToast} from '../../../utils/customShowToast';
import {handlerWenzhenStorage} from '../../../utils/index';
import {VITA_RECOMMEND_SKU_UNFOLD} from '../../../constants/storageEnv';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../../utils/generalFunction/ubc';
import {useScrollControl} from '../../../../../pages-im/src/hooks/triageStream/useScrollControl';
import {formatPrice} from '../common/utils';

import NonDirSkeleton from '../Skeletons/NonDirSkeleton';
import ServiceTitle from './component/ServiceTitle';
import ServicePopup from './component/ServicePopup';
import type {UndirectServiceDataV2Props} from './index.d';
import styles from './index.module.less';

const unfoldSkuStorage = handlerWenzhenStorage(VITA_RECOMMEND_SKU_UNFOLD, 50);
const TIME_OUT_TIME = 5000;
/**
 *
 * @description 定向服务卡 v2 版本
 * @returns
 */
const UndirectService: FC<UndirectServiceDataV2Props> = props => {
    const {
        skuData = {},
        updateCollectedInfoAndSku,
        lineText,
        isLogin,
        updateSkuDetailData,
        isSkuDetailPopupShow,
        setIsSkuDetailPopupShow,
        isSkuDisabled,
        hideTopSkuArea,
        skuDisabledToastText,
        adjectiveRecommendUnDirectMsgId,
        patientFinishTypewriter,
        storageKey,
        isHistoryMsg,
        isRenderCombine,
        renderType,
        showLineText,
        showSkeleton,
        msgId,
        ext,
        isLast
    } = props;
    const {subText, topArea, titleImg, list, btnInfo, serviceTip, toastTip, leftIcon, tagArr} =
        skuData;
    const [selectSku, setSelectSku] = useState();
    const [showRecommendSku, setRecommendSku] = useState(
        unfoldSkuStorage('get').includes(storageKey) || btnInfo?.disabled
    );

    const [showGuideHand, setShowGuideHand] = useState(false);

    const {scrollToBottom, scrollToMessage} = useScrollControl();

    // 展示 提示浮层
    const showTip = useCallback(
        (detail, e) => {
            e.stopPropagation();

            if (
                detail &&
                !isEmpty(detail.detailInfo) &&
                !isEmpty(detail?.detailInfo?.features) &&
                !isEmpty(detail?.detailInfo?.explains)
            ) {
                setIsSkuDetailPopupShow(true);
                setSelectSku(detail);
            } else {
                setSelectSku(detail);
            }
            updateSkuDetailData(detail);
        },
        [setIsSkuDetailPopupShow, updateSkuDetailData]
    );

    // 非定向sku点击事件
    const handleSkuItemClick = useCallback(
        (sku, ubcExtInfo) => {
            if (!sku?.btn?.disabled) {
                updateCollectedInfoAndSku('sku', {
                    selectSkuData: sku,
                    ubcExtInfo: {
                        ...ubcExtInfo,
                        pos: ubcExtInfo?.pos + 1
                    }
                });
            } else {
                showToast({
                    title: toastTip,
                    icon: 'none'
                });
            }
        },
        [toastTip, updateCollectedInfoAndSku]
    );

    // 获取sku单位价格，是否有分位
    const isFenValue = useCallback(price => {
        if (!price) return false;

        return price % 100 !== 0;
    }, []);

    // 渲染sku列表
    const renderList = useMemo(() => {
        return (
            list &&
            list.length > 0 &&
            list.map((sku: undirectService, index: number) => {
                const {
                    skuId = '',
                    title,
                    sub,
                    detailInfo,
                    priceText,
                    orgPrice,
                    tag,
                    promotionPrice,
                    titleIcon
                } = sku;

                if (showSkeleton) {
                    return (
                        <View className={cx(styles.skuSkeletonItem)} key={skuId}>
                            <NonDirSkeleton />
                        </View>
                    );
                }

                return (
                    <View
                        key={skuId}
                        className={cx(
                            styles.skuItem,
                            isRenderCombine ? styles.skuItemCombine : '',
                            'wz-flex'
                        )}
                    >
                        <View className={cx(styles.left, leftIcon ? 'wz-ml-36' : '')}>
                            <CLoginButton
                                isLogin={isLogin}
                                closeShowNewUserTag={true}
                                useH5CodeLogin={true}
                                onLoginFail={error => {
                                    console.error('error', error);
                                }}
                                onLoginSuccess={() => {
                                    handleSkuItemClick(sku, {
                                        // 非定向服务大卡也占一个位置
                                        pos: btnInfo ? index + 1 : index,
                                        value_id: skuId || ''
                                    });
                                    ubcCommonClkSend({
                                        value: `no_direct_sku_typewriter_service_clk_${isLogin}`,
                                        ext: {
                                            product_info: {
                                                msgId: msgId || '',
                                                ...ext
                                            }
                                        }
                                    });
                                }}
                            >
                                <View
                                    className={cx(
                                        styles.consult,
                                        isRenderCombine ? 'wz-fs-54' : 'wz-fs-57',
                                        'wz-flex wz-col-center wz-fw-500'
                                    )}
                                >
                                    {title && <View>{title}</View>}
                                    {tag && (
                                        <Tag
                                            className={cx(styles.tagWrapper, 'wz-ml-18')}
                                            size='medium'
                                            shape='square'
                                            variant='outlined'
                                            style={{
                                                padding: `${pxTransform(9)} ${pxTransform(14)} ${pxTransform(8)}`,
                                                fontSize: pxTransform(33),
                                                backgroundColor: 'inherit',
                                                color: '#fd503e',
                                                borderColor: 'rgba(253, 80, 62, 0.5)'
                                            }}
                                        >
                                            <View className={styles.tagTxt}>{tag}</View>
                                        </Tag>
                                    )}
                                    {titleIcon?.map((item, index) => {
                                        if (item.type === 'img' && item.url) {
                                            return (
                                                <Image
                                                    className={cx(styles.titleIcon, 'wz-ml-18')}
                                                    key={index}
                                                    src={item.url || ''}
                                                    mode='aspectFit'
                                                />
                                            );
                                        }

                                        return null;
                                    })}
                                </View>
                            </CLoginButton>
                            {sub && (
                                <View
                                    className={cx(
                                        styles.skus,
                                        'wz-flex wz-col-center wz-fs-42 wz-mt-27'
                                    )}
                                    onClick={e => showTip(sku, e)}
                                >
                                    <Text className={styles.line1}>{sub}</Text>
                                    {!isEmpty(detailInfo) &&
                                        !isEmpty(detailInfo?.features) &&
                                        !isEmpty(detailInfo?.explains) && (
                                        <WiseRightArrow color='#B8B8B8' size={36} />
                                    )}
                                </View>
                            )}
                        </View>
                        <CLoginButton
                            isLogin={isLogin}
                            closeShowNewUserTag={true}
                            useH5CodeLogin={true}
                            onLoginFail={error => {
                                console.error('error', error);
                            }}
                            onLoginSuccess={() => {
                                handleSkuItemClick(sku, {
                                    pos: btnInfo ? index + 1 : index,
                                    value_id: skuId || ''
                                });
                            }}
                        >
                            <View />
                        </CLoginButton>
                        <CLoginButton
                            isLogin={isLogin}
                            closeShowNewUserTag={true}
                            useH5CodeLogin={true}
                            onLoginFail={error => {
                                console.error('error', error);
                            }}
                            onLoginSuccess={() => {
                                handleSkuItemClick(sku, {
                                    pos: btnInfo ? index + 1 : index,
                                    value_id: skuId || ''
                                });
                            }}
                        >
                            <View className={styles.right}>
                                <View
                                    className={cx(
                                        'wz-flex',
                                        'wz-col-center',
                                        'wz-text-right',
                                        'wz-mb-15',
                                        'wz-row-right',
                                        'wz-pr-45',
                                        styles.priceArea
                                    )}
                                >
                                    <View
                                        className={cx('wz-fs-36', 'wz-mr-18', styles.originPrice)}
                                    >
                                        {orgPrice}
                                    </View>
                                    {isRenderCombine ? (
                                        <View className={cx('wz-fs-48', styles.curPrice)}>
                                            <Text className='wz-fs-36'>￥</Text>
                                            <Price
                                                price={formatPrice(promotionPrice || 0, {
                                                    significant: isFenValue(promotionPrice) ? 2 : 1,
                                                    multiple: 2
                                                })}
                                            />
                                        </View>
                                    ) : (
                                        <View className={cx('wz-fs-48', styles.curPrice)}>
                                            {priceText}
                                        </View>
                                    )}
                                </View>
                                <View
                                    className={cx(
                                        'wz-flex',
                                        'wz-col-center',
                                        'wz-row-center',
                                        'wz-fs-42',
                                        'wz-fw-500',
                                        'wz-ml-12',
                                        styles.toConsult,
                                        sku?.btn?.disabled ? styles.toConsultDisabled : ''
                                    )}
                                >
                                    {sku?.btn?.value}
                                </View>
                            </View>
                        </CLoginButton>
                    </View>
                );
            })
        );
    }, [
        list,
        showSkeleton,
        isRenderCombine,
        leftIcon,
        isLogin,
        isFenValue,
        handleSkuItemClick,
        btnInfo,
        ext,
        msgId,
        showTip
    ]);

    useEffect(() => {
        const keys = unfoldSkuStorage('get') || [];

        if (showRecommendSku) {
            if (!keys.includes(storageKey)) {
                unfoldSkuStorage('add', storageKey);
            }
        } else {
            if (keys.includes(storageKey)) {
                unfoldSkuStorage('del', storageKey);
            }
        }
    }, [showRecommendSku, storageKey]);

    // 判断lineText是否展示，与定向服务卡联动
    const getLineTextCom = useCallback(() => {
        if (!showLineText || !lineText) {
            return null;
        }

        return (
            <View
                className={cx(
                    styles['horizontal-line-container'],
                    'wz-pb-9 wz-pt-12',
                    !showRecommendSku ? styles.skuTop : ''
                )}
            >
                <View className='c-color-desc wz-ml-48 wz-mr-48 wz-fs-42, wz-text-center'>
                    <View
                        className={cx(
                            styles['horizontal-line-tip'],
                            'wz-plr-48 wz-flex wz-col-center wz-row-center'
                        )}
                        onClick={() => {
                            setRecommendSku(showRecommendSku => !showRecommendSku);
                            // 不是最后一条消息，不往下滚
                            if (isLast) {
                                scrollToBottom('ImCollectedAndNoNDirectSkuTypewriterMore');
                            }
                        }}
                    >
                        {!showRecommendSku ? lineText || '更多服务推荐' : ''}
                        {showRecommendSku ? null : <WiseDownArrow size={42} className='wz-ml-9' />}
                    </View>
                </View>
            </View>
        );
    }, [lineText, scrollToBottom, showLineText, showRecommendSku, isLast]);

    const memoRenderTopSku = useMemo(() => {
        if (hideTopSkuArea) {
            return null;
        }
        return (
            <View>
                <ServiceTitle
                    subText={subText}
                    topArea={topArea}
                    titleImg={titleImg}
                    leftIcon={leftIcon}
                    tagArr={tagArr}
                    serviceTip={serviceTip}
                    isRenderCombine={isRenderCombine}
                />

                {btnInfo && (
                    <CLoginButton
                        isLogin={isLogin}
                        closeShowNewUserTag={true}
                        useH5CodeLogin={true}
                        onLoginFail={error => {
                            console.error('error', error);
                        }}
                        onLoginSuccess={() => {
                            handleSkuItemClick(
                                {btn: btnInfo},
                                {
                                    pos: 0,
                                    value_id: 'free'
                                }
                            );
                            ubcCommonClkSend({
                                value: `no_direct_sku_typewriter_free_clk_${isLogin}`,
                                ext: {
                                    product_info: {
                                        msgId: msgId || '',
                                        ...ext
                                    }
                                }
                            });
                        }}
                        disableSuccessToast={true}
                    >
                        <View className={showGuideHand && !isRenderCombine ? styles.guide : ''}>
                            <HButton
                                text={btnInfo?.value}
                                height={isRenderCombine ? 135 : 114}
                                rootStyle={{width: '100%', boxSizing: 'border-box'}}
                                className={cx(
                                    styles.btn,
                                    'wz-fw-500',
                                    isRenderCombine ? 'wz-mt-15' : 'wz-mt-27 wz-mb-45'
                                )}
                                disabled={btnInfo?.disabled}
                            ></HButton>
                        </View>
                    </CLoginButton>
                )}
                {getLineTextCom()}
            </View>
        );
    }, [
        isRenderCombine,
        tagArr,
        leftIcon,
        btnInfo,
        hideTopSkuArea,
        isLogin,
        serviceTip,
        showGuideHand,
        subText,
        getLineTextCom,
        titleImg,
        ext,
        msgId,
        topArea,
        handleSkuItemClick
    ]);

    useEffect(() => {
        if (isHistoryMsg || hideTopSkuArea || btnInfo?.disabled) {
            return;
        }

        if (patientFinishTypewriter) {
            const timer = setTimeout(() => {
                setShowGuideHand(true);
                clearTimeout(timer);
            }, TIME_OUT_TIME);

            eventCenter.on('im_page_touch', () => {
                setShowGuideHand(false);
                timer && clearTimeout(timer);
            });

            return () => {
                eventCenter.off('im_page_touch');
                timer && clearTimeout(timer);
            };
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [patientFinishTypewriter]);

    useEffect(() => {
        let timer;
        if (showGuideHand) {
            timer = setTimeout(() => {
                setShowGuideHand(false);
                clearTimeout(timer);
            }, TIME_OUT_TIME);

            return () => {
                timer && clearTimeout(timer);
            };
        }
    }, [showGuideHand]);

    // 刷新卡片之后，如果免费咨询不能点击,或者隐藏了免费咨询自动展开服务列表
    useEffect(() => {
        if (btnInfo?.disabled || hideTopSkuArea) {
            setRecommendSku(true);
        }
    }, [btnInfo?.disabled, hideTopSkuArea]);

    useEffect(() => {
        // 通过btnInfo判断是否存在免费服务，可能会变；
        if (btnInfo) {
            ubcCommonViewSend({
                value: 'no_direct_sku_typewriter_free',
                ext: {
                    value_type: renderType,
                    value_id: 'free',
                    product_info: {
                        msgId: msgId || '',
                        ...ext
                    }
                }
            });
        }
    }, [btnInfo, ext, msgId, renderType]);

    useEffect(() => {
        !isEmpty(list) &&
            list?.map(item => {
                ubcCommonViewSend({
                    value: 'ImUnDirectSku',
                    ext: {
                        value_type: `sku_${renderType}`,
                        value_id: item.skuId,
                        product_info: {
                            msgId: msgId || '',
                            ...ext
                        }
                    }
                });
            });
    }, [ext, list, msgId, renderType]);

    useEffect(() => {
        showRecommendSku &&
            ubcCommonViewSend({
                value: 'no_direct_sku_typewriter_service',
                ext: {
                    product_info: {
                        msgId: msgId || '',
                        ...ext
                    }
                }
            });
    }, [ext, msgId, showRecommendSku]);

    return (
        <View className={cx(styles.undirectService, 'wz-plr-45')}>
            {memoRenderTopSku}
            {showRecommendSku || hideTopSkuArea || isRenderCombine ? (
                <View className={cx(styles.skuWrapper)}>{renderList}</View>
            ) : null}
            <Portal>
                <Popup
                    open={isSkuDetailPopupShow}
                    onClose={() => setIsSkuDetailPopupShow(false)}
                    rounded
                    placement='bottom'
                    title='服务详情'
                    titleStyle={{borderBottom: 'none'}}
                >
                    <Popup.Close />
                    <ServicePopup
                        selectSku={selectSku}
                        updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                        isLogin={isLogin}
                    />
                </Popup>
            </Portal>
            {/* 非最新的sku卡，不可点击，有遮罩层 */}
            {isSkuDisabled && (
                <View
                    className={styles.isDisabledContainer}
                    onClick={() => {
                        showToast({
                            title: skuDisabledToastText || '当前推荐已失效，请点击新的服务卡',
                            icon: 'none'
                        });
                        adjectiveRecommendUnDirectMsgId &&
                            scrollToMessage(adjectiveRecommendUnDirectMsgId, 'undirectService');
                    }}
                ></View>
            )}
        </View>
    );
};

export default memo(UndirectService);
