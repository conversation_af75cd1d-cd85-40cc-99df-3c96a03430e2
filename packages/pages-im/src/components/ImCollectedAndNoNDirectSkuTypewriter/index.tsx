import {View} from '@tarojs/components';
import {showLoading, hideLoading} from '@tarojs/taro';
import {memo, type FC, useCallback, useState, useReducer, useRef, useEffect, useMemo} from 'react';
import {Portal} from '@baidu/vita-ui-cards-common';
import {Dialog, Button} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';

import {
    useGetUserData,
    useGetSessionId,
    useGetLastMsgId,
    useUpdateUserData,
    useGetAdjectiveRecommendUnDirectMsgId,
    useGetAdjectiveRecommendExpertMsgId
} from '../../hooks/triageStream/pageDataController';
import {isEmpty} from '../../utils';
import {useMsgDataGetController} from '../../hooks/triageStream/dataController';
import {getUserBizActionReq} from '../../models/services/triageStream';
import type {userInfoItem} from '../../models/services/triageStream/index.d';
import {showToast} from '../../utils/customShowToast';
import {updateTriageStreamMsgAtom} from '../../store/triageStreamAtom/msg';
import {MsgItemType} from '../../store/triageStreamAtom/index.type';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../utils/generalFunction/ubc';
import {useConversationDataController} from '../../hooks/triageStream/useConversationDataController';
import {useHeightObserver} from '../../hooks/common/useHeightObserver';
import {useScrollControl} from '../../hooks/triageStream/useScrollControl';
import {ServiceTypeMap} from '../../constants/common';
import type {expertItem} from '../ImCollectedAndReExpertTypewriter/index.d';
import {navigate} from '../../utils/basicAbility/commonNavigate';
import ExpertRecommendation from '../ImCollectedAndReExpertTypewriter/ExpertRecommendation';
import type {undirectServiceUpdatePayload} from './UndirectService/index.d';
import {usePreCheckOrderModal} from './hooks/usePreCheckOrderModal';
import PatientCard from './PatientCard';
import ConsultForm from './ConsultForm';
import {formReducer} from './ConsultForm/form-reducer';
import {useGetMakeOrderRelatedInfo} from './hooks/useGetMakeOrderRelatedInfo';
import UndirectService from './UndirectService';
import type {PatientInfo} from './ConsultForm/index.d';
import type {
    ImCollectedAndNoNDirectSkuProps,
    ImNoNDirectSkuPData,
    OpenPatientType,
    undirectService
} from './index.d';

import styles from './index.module.less';

/**
 *
 * @description AI智能体定向服务卡
 * @returns
 */
const ImCollectedAndNoNDirectSku: FC<ImCollectedAndNoNDirectSkuProps> = props => {
    const {msgId, localExt} = props;
    const {dataSource} = localExt || {};
    const {content, ext = {}, cardStyle} = props?.data || {};
    const {scene = ''} = ext || {};
    const {
        collectedInfo,
        skuData,
        requireDesc,
        lineText,
        isExpired,
        hideTopSkuArea,
        expertData,
        maxShowLen
    } = content;
    const {qid, statusText} = collectedInfo || '';
    const openPatientPopTypeRef = useRef<boolean>(false); // 存储点开来源类型，用于判断是否发生了变化
    // eslint-disable-next-line max-len
    const {lastMsgId} = useGetLastMsgId();
    const isHistoryMsg = lastMsgId !== msgId || dataSource !== 'conversation';

    const [patientFinishTypewriter, setPatientFinishTypewriter] = useState(isHistoryMsg); // 打字机是否完成

    const [showConsultForm, setShowConsultForm] = useState(false); // 是否展示编辑态表单弹层
    const [showConsultList, setShowConsultList] = useState(false); // 是否展示选择就诊人列表弹层
    const [openPatientPopType, setOpenPatientPopType] = useState<OpenPatientType>('patient'); // 点开打开弹窗的来源类型
    const [selectedSkuItemData, setSelectedSkuItemData] = useState<undirectService>(
        (skuData?.list || [])[0]
    );

    const [isSkuDisabled, setIsSkuDisabled] = useState(false);
    const [updateCollectedInfoAndSkuType, setUpdateCollectedInfoAndExpert] =
        useState<OpenPatientType>('patient');
    const [selectPatientData, setSelectPatientData] = useState<PatientInfo | undefined>(
        collectedInfo?.curPatient
    );

    const {userData} = useGetUserData();

    const prevIsLoginRef = useRef<boolean>(userData?.isLogin || false); // 存储之前的值，用于判断是否发生了变化

    const sessionId = useGetSessionId();
    const {updateUserData} = useUpdateUserData();
    const {adjectiveRecommendUnDirectMsgId} = useGetAdjectiveRecommendUnDirectMsgId();
    const {adjectiveRecommendExpertMsgId} = useGetAdjectiveRecommendExpertMsgId();
    const {data: docServiceData} = useMsgDataGetController({msgId: adjectiveRecommendExpertMsgId});
    const {data: undirectServiceData} = useMsgDataGetController({
        msgId: adjectiveRecommendUnDirectMsgId
    });
    const [skuDisabledToastText, setSkuDisabledToastText] =
        useState('当前推荐已失效，请点击新的服务卡');

    const {scrollToMessage} = useScrollControl();

    const [isSkuDetailPopupShow, setIsSkuDetailPopupShow] = useState(false); // 是否展示 sku 详情弹窗
    const {updateSessionCapsulesTools} = useConversationDataController();

    const [isDialogOpen, setIsDialogOpen] = useState(false); // 就诊人科室性别冲突弹窗
    const [dialogConfig, setDialogConfig] = useState({
        content: '',
        confirmText: '确认',
        cancelText: '取消',
        onConfirm: () => {}
    });

    const [showSkeleton, setShowSkeleton] = useState(false);

    // 判断是否是本次实验组合卡
    const [isRenderCombine] = useState(cardStyle?.renderType === 3);

    const [selectedExpertItemData, setSelectedExpertItemData] = useState<expertItem>(
        (expertData?.list || [])[0]
    );

    const storageKey = `${sessionId}_${msgId}`;
    const imCollectedAndNoNDirectSkuTypewriterId = useMemo(() => `im-flow-${msgId}`, [msgId]);

    useEffect(() => {
        // isExpired接口端判断无效卡,直接禁用当前卡
        if (isExpired || adjectiveRecommendUnDirectMsgId !== props?.msgId) {
            setIsSkuDisabled(true);
        }
        isExpired && setSkuDisabledToastText('服务卡已失效，你可向健康管家重新咨询');
    }, [adjectiveRecommendUnDirectMsgId, isExpired, props?.msgId]);

    // 初始化高度变化监听hook
    useHeightObserver(
        imCollectedAndNoNDirectSkuTypewriterId,
        lastMsgId === msgId,
        'dom_height_observer'
    );

    const [state, dispatch] = useReducer(formReducer, {
        ...collectedInfo?.curPatient,
        canSubmit: true,
        zhusu: collectedInfo?.clinicalDesc || '',
        images: collectedInfo?.images
    });
    const {PreCheckOrderModal, openModal, closeModal} = usePreCheckOrderModal();

    const getShowExpertMaxLen = useCallback(() => {
        // 没有返回最大展示数量，兜底展示两个定向
        if (!maxShowLen) {
            return 2;
        }

        let res = maxShowLen;

        // 存在付费sku
        if (skuData?.list?.length) {
            res -= skuData?.list?.length;
        }

        // 存在首卡
        if (!hideTopSkuArea && skuData?.btnInfo) {
            res -= 1;
        }

        return res >= 0 ? res : 0;
    }, [maxShowLen, hideTopSkuArea, skuData]);

    // 登录 & 下单部分逻辑
    const {loginSuccessfulCallback} = useGetMakeOrderRelatedInfo({
        msgId: props?.msgId,
        skuList: skuData?.list,
        qid,
        openModal,
        closeModal
    });
    /**
     *
     * @description 更新选中的 skuId，并触发相关计算
     */
    const updateSkuDetailData = useCallback((selectSkuData: undirectService) => {
        setSelectedSkuItemData(selectSkuData); // 更新选中的 sku 数据
    }, []);

    // 就诊人不存在时，直接展示表单编辑态
    const openEditPatientPop = useCallback(() => {
        setShowConsultList(false);
        setShowConsultForm(true);
        dispatch({
            payload: {
                ...collectedInfo,
                ...collectedInfo?.curPatient,
                zhusu: collectedInfo?.clinicalDesc,
                images: collectedInfo?.images
            }
        });
    }, [collectedInfo]);

    // 就诊人存在时，打开就诊人选择弹层
    const openEditPatient = useCallback(() => {
        if (collectedInfo?.curPatient?.contactId) {
            setShowConsultList(true);
            setSelectPatientData(
                collectedInfo?.curPatient?.contactId ? collectedInfo?.curPatient : undefined
            );
            dispatch({
                payload: {
                    ...collectedInfo?.curPatient,
                    zhusu: collectedInfo?.clinicalDesc || '',
                    images: collectedInfo?.images
                }
            });
        }
    }, [collectedInfo]);

    const handleAddPatient = useCallback(() => {
        setShowConsultForm(true);
    }, []);

    // 处理弹层关闭逻辑
    const handleCloseConsultForm = useCallback(
        (type: string) => {
            if (type === 'consult') {
                setShowConsultForm(false);
                dispatch({
                    payload: {
                        ...collectedInfo?.curPatient,
                        zhusu: state?.zhusu || '',
                        images: state?.images || []
                    }
                });
            }
            if (type === 'consultList') {
                setSelectPatientData(undefined);
                setShowConsultList(false);
            }

            // 500ms 延迟关闭骨架屏
            setTimeout(() => {
                setShowSkeleton?.(false);
            }, 500);

            ubcCommonClkSend({
                value: `ImAIRecommendUnDirect_${scene}_${type}_close`,
                ext: {
                    product_info: {
                        msgId: props?.msgId || '',
                        ...ext
                    }
                }
            });
        },
        [scene, props?.msgId, ext, collectedInfo?.curPatient, state?.zhusu, state?.images]
    );

    // 选择就诊人
    const handleSelectPatient = useCallback(
        (selectPatient: PatientInfo) => {
            setSelectPatientData(selectPatient);
            dispatch({
                payload: {
                    ...selectPatientData,
                    zhusu: state?.zhusu || '',
                    images: state?.images || []
                }
            });
        },
        [selectPatientData, state?.images, state?.zhusu]
    );

    // 根据当前选中的skuId查找最新的sku数据
    const findSelectedSku = useCallback(
        (skuId: string) => {
            return skuData.list?.find(skuItem => skuItem.skuId === skuId);
        },
        [skuData]
    );

    const addPatientToast = useCallback(() => {
        showToast({
            title: '请完善您的提问信息',
            icon: 'none'
        });
    }, []);

    // 更新选中sku逻辑
    const handleSelectSku = useCallback(() => {
        if (!selectedSkuItemData?.skuId) {
            setSelectedSkuItemData({btn: skuData?.btnInfo || {}});
        } else {
            const selectSku = findSelectedSku(selectedSkuItemData?.skuId || '');
            selectSku && setSelectedSkuItemData(selectSku);
        }
    }, [findSelectedSku, selectedSkuItemData?.skuId, skuData?.btnInfo]);

    const handleInteractionWithConfirm = useCallback(
        (options, callbackParams) => {
            if (options?.interactionInfo?.confirm) {
                setDialogConfig({
                    content: options?.interactionInfo?.confirm?.title,
                    confirmText: options?.interactionInfo?.confirm?.confirmText || '确认下单',
                    cancelText: options?.interactionInfo?.confirm?.cancelText || '返回修改',
                    onConfirm: () => callbackParams && loginSuccessfulCallback(callbackParams)
                });
                setIsDialogOpen(true);
            } else {
                callbackParams && loginSuccessfulCallback(callbackParams);
            }
        },
        [loginSuccessfulCallback]
    );

    const handleConfirm = () => {
        dialogConfig?.onConfirm?.();
        setIsDialogOpen(false);
    };

    const handleCancel = () => {
        setIsDialogOpen(false);
    };

    const findSelectedExpert = useCallback(
        (docID: string) => {
            return expertData?.list?.find(expert => expert?.docID === docID);
        },
        [expertData]
    );

    // 监听登录发生变化后的数据处理
    useEffect(() => {
        if (openPatientPopTypeRef.current) {
            // 无就诊人时，直接提示完善信息，并弹起就诊人编辑弹层
            if (!collectedInfo?.curPatient?.contactId) {
                (openPatientPopType === 'sku' || openPatientPopType === 'expert') &&
                    addPatientToast();
                openEditPatientPop();
                setIsSkuDetailPopupShow(false);
                // 更新当前选中的sku信息
                openPatientPopType === 'sku' && handleSelectSku();
                return;
            }
            // 未登录变为登录在状态后，存在就诊人时，点击的为就诊人模块时弹窗就诊人选择列表，否则直接走下单逻辑
            if (updateCollectedInfoAndSkuType === 'patient' || !collectedInfo?.clinicalDesc) {
                // 免费sku，并且无劵，禁止点击的时候，并且并且不完整，不弹起病情编辑半弹窗
                if (
                    !selectedSkuItemData?.skuId &&
                    skuData?.btnInfo?.disabled &&
                    !collectedInfo?.clinicalDesc
                ) {
                    return;
                }
                openEditPatient(); // 存在就诊人，弹就诊人切换弹层
            } else {
                // 直接走下单逻辑(免费服务无skuId时，直接走下单逻辑)
                if (!selectedSkuItemData?.skuId && !skuData?.btnInfo?.disabled) {
                    if (skuData?.btnInfo?.interactionInfo?.confirm) {
                        // 展示科室冲突确认弹窗
                        handleInteractionWithConfirm(skuData?.btnInfo, {
                            btn: skuData?.btnInfo || {}
                        });
                    } else {
                        loginSuccessfulCallback({btn: skuData?.btnInfo || {}});
                    }
                } else if (updateCollectedInfoAndSkuType === 'sku') {
                    // 非定向
                    const selectSku = findSelectedSku(selectedSkuItemData?.skuId || '');
                    if (selectSku?.btn) {
                        handleInteractionWithConfirm(selectSku?.btn, selectSku || {});
                    }
                } else if (updateCollectedInfoAndSkuType === 'expert') {
                    // 定向
                    const selectExpert = findSelectedExpert(selectedExpertItemData?.docID || '');
                    // 直接走跳转逻辑
                    if (selectExpert) {
                        jumpToTriage(selectExpert?.btnInfo);
                    }
                }
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [openPatientPopTypeRef.current]);

    // 更新卡片数据：未登录转登录后，更新两类服务卡
    const handleMessageUpdate = useCallback(
        messageList => {
            messageList.forEach(messageItem => {
                updateTriageStreamMsgAtom(`${sessionId}_${messageItem?.meta?.msgId}`, messageItem, {
                    _debugSymbol: 'loginUpdate'
                });
            });
            // 数据需要更新再出骨架屏
            setShowSkeleton(true);
            setTimeout(() => {
                setShowSkeleton(false);
            }, 200);
        },
        [sessionId]
    );

    // 用户行为接口数据参数
    const handleParams = useCallback(() => {
        const userLoginInfoListData: userInfoItem[] = [];
        // 合并两个消息的数据
        [undirectServiceData, docServiceData].forEach(item => {
            if (item && item?.data && item?.data?.content) {
                const {cardId} = item?.data?.content || '';
                const {msgId} = item?.meta || '';
                const {collectedInfo} = item?.data?.content?.data?.content || {};
                const compParam = {
                    msgId,
                    patientInfo: collectedInfo?.curPatient,
                    serviceType: ServiceTypeMap[cardId],
                    zhusu: collectedInfo?.clinicalDesc || '',
                    qid: collectedInfo?.qid
                };
                userLoginInfoListData.push(compParam);
            }
        });
        const params = {
            bizActionType: 'userLogin' as const,
            chatData: {
                sessionId
            },
            bizActionData: {
                userLoginInfoList: {
                    msgList: userLoginInfoListData
                }
            }
        };
        return params;
    }, [docServiceData, sessionId, undirectServiceData]);

    const showFreeSkuTipToast = useCallback(
        (message: MsgItemType<unknown>[]) => {
            const msgData = message?.find(item => item?.meta?.msgId === props?.msgId);
            const {skuData} = msgData?.data?.content?.data?.content as ImNoNDirectSkuPData;
            const {btnInfo, toastTip} = skuData;

            if (btnInfo?.disabled && toastTip) {
                showToast({
                    title: toastTip,
                    icon: 'none'
                });
            }
        },
        [props?.msgId]
    );

    // 服务卡点击卡片失效效果
    const handleCardDisabled = useCallback(() => {
        showToast({
            title: skuDisabledToastText,
            icon: 'none'
        });
        sessionId &&
            adjectiveRecommendUnDirectMsgId &&
            scrollToMessage(adjectiveRecommendUnDirectMsgId, 'updateCollectedInfoAndSku');

        ubcCommonClkSend({
            value: `ImAIRecommendUnDirect_${scene}_disable`,
            ext: {
                product_info: {
                    ...ext
                },
                value_type: cardStyle?.renderType
            }
        });
    }, [
        skuDisabledToastText,
        sessionId,
        adjectiveRecommendUnDirectMsgId,
        scrollToMessage,
        scene,
        ext,
        cardStyle?.renderType
    ]);

    // 定向跳转逻辑
    const jumpToTriage = useCallback(
        btnInfo => {
            const {interaction, interactionInfo} = btnInfo || selectedExpertItemData?.btnInfo || {};
            if (interaction && interaction === 'openLink' && interactionInfo?.url) {
                navigate({
                    openType: 'navigate',
                    url: interactionInfo?.url
                });
            }
        },
        [selectedExpertItemData?.btnInfo]
    );

    // 更新服务卡数据，兼容组合卡
    const updateCollectedInfoAndSku = useCallback(
        // eslint-disable-next-line complexity
        async (type: OpenPatientType, payload?: undirectServiceUpdatePayload) => {
            const {
                selectSkuData: selectSku,
                expertData: selectedExpertData,
                ubcExtInfo
            } = payload || {};
            const isExpertMode = !!selectedExpertData;

            if (isSkuDisabled) {
                handleCardDisabled();

                return;
            }

            if (isExpertMode) {
                selectedExpertData && setSelectedExpertItemData(selectedExpertData);
            } else {
                selectSku && setSelectedSkuItemData(selectSku);
            }

            setOpenPatientPopType(type);

            // 未登录
            if (sessionId && !prevIsLoginRef.current && !userData?.isLogin) {
                showLoading({
                    title: '登录中...',
                    mask: true
                });
                const params = handleParams();
                const [err, data] = await getUserBizActionReq<'userLogin'>(params);
                if (!err) {
                    data?.data?.message && handleMessageUpdate(data?.data?.message || []);
                    data?.data?.userData && updateUserData(data?.data?.userData);
                    // 更新胶囊工具数据
                    data?.data?.toolData?.capsules &&
                        updateSessionCapsulesTools(data?.data?.toolData?.capsules);
                    prevIsLoginRef.current = data?.data?.userData?.isLogin || false;
                    setUpdateCollectedInfoAndExpert(type);
                    openPatientPopTypeRef.current = true;
                    hideLoading({noConflict: true});
                    // 业务需求：仅点击免费咨询时，才展示无券提示
                    !selectSku?.skuId &&
                        data?.data?.message &&
                        showFreeSkuTipToast(data?.data?.message || []);
                }
            } else {
                // 已登录--无就诊人
                if (!collectedInfo?.curPatient?.contactId) {
                    // 点击【去咨询】时，需要先toast后再出弹层
                    (type === 'sku' || type === 'expert') && addPatientToast();
                    openEditPatientPop();

                    return;
                }
                // 已登录--有就诊人--就诊人选择模块
                if (type === 'patient' || !collectedInfo?.clinicalDesc) {
                    openEditPatient();
                } else {
                    // 已登录--有就诊人--服务卡去咨询
                    // 直接走下单逻辑

                    // 组合卡片点击专家按钮逻辑
                    if (isExpertMode) {
                        selectedExpertData && jumpToTriage(selectedExpertData?.btnInfo);
                    } else {
                        // 组合卡片点击非定向老通路
                        handleInteractionWithConfirm(selectSku?.btn, selectSku || {});
                    }
                }
            }
            if (type === 'sku' || type === 'expert') {
                let valueType = isExpertMode ? 'doc' : type;
                if (cardStyle?.renderType && cardStyle?.renderType > 2) {
                    valueType = `${valueType}_${cardStyle?.renderType}`;
                }

                ubcCommonClkSend({
                    value: 'ImAIRecommendUnDirect',
                    ext: {
                        value_type: valueType,
                        value_id: isExpertMode
                            ? selectedExpertData?.docID
                            : selectSku?.skuId || 'free',
                        product_info: {
                            msgId: props?.msgId || '',
                            ...ext
                        },
                        ...ubcExtInfo
                    }
                });
            } else {
                ubcCommonClkSend({
                    value: 'ImAIRecommendUnDirect_edit',
                    ext: {
                        product_info: {
                            msgId: props?.msgId || '',
                            ...ext
                        }
                    }
                });
            }
        },
        [
            isSkuDisabled,
            sessionId,
            userData?.isLogin,
            ext,
            props?.msgId,
            handleParams,
            handleMessageUpdate,
            updateUserData,
            updateSessionCapsulesTools,
            collectedInfo?.curPatient?.contactId,
            collectedInfo?.clinicalDesc,
            cardStyle?.renderType,
            handleCardDisabled,
            addPatientToast,
            openEditPatientPop,
            handleInteractionWithConfirm,
            jumpToTriage,
            showFreeSkuTipToast,
            openEditPatient
        ]
    );

    useEffect(() => {
        if (lastMsgId !== msgId && !patientFinishTypewriter) {
            setPatientFinishTypewriter(true);
        }
    }, [lastMsgId, msgId, patientFinishTypewriter]);

    useEffect(() => {
        ubcCommonViewSend({
            value: 'ImAIRecommendUnDirect',
            ext: {
                product_info: {
                    msgId: props?.msgId || '',
                    ...ext
                },
                value_type: cardStyle?.renderType || 2
            }
        });
    }, [cardStyle?.renderType, ext, props?.msgId]);

    return (
        <View id={imCollectedAndNoNDirectSkuTypewriterId} className={styles.skuContainer}>
            {/* AI重构-就诊人 */}
            <PatientCard
                isLogin={userData?.isLogin || false}
                collectedInfo={collectedInfo}
                updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                isSkuDisabled={isSkuDisabled}
                patientFinishTypewriter={patientFinishTypewriter}
                statusText={statusText}
                setPatientFinishTypewriter={setPatientFinishTypewriter}
                storageKey={storageKey}
                adjectiveRecommendUnDirectMsgId={adjectiveRecommendUnDirectMsgId}
                paddingBottom={patientFinishTypewriter ? 72 : 0}
                className={styles.gradient}
            />
            <View
                className={cx(
                    styles.undirectServiceWrapper,
                    patientFinishTypewriter ? styles.expand : ''
                )}
            >
                <UndirectService
                    skuData={skuData}
                    hideTopSkuArea={hideTopSkuArea}
                    isRenderCombine={isRenderCombine}
                    renderType={cardStyle?.renderType}
                    patientFinishTypewriter={patientFinishTypewriter}
                    updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                    lineText={lineText}
                    isLogin={userData?.isLogin || false}
                    updateSkuDetailData={updateSkuDetailData}
                    isSkuDetailPopupShow={isSkuDetailPopupShow}
                    setIsSkuDetailPopupShow={setIsSkuDetailPopupShow}
                    isSkuDisabled={isSkuDisabled}
                    skuDisabledToastText={skuDisabledToastText}
                    adjectiveRecommendUnDirectMsgId={adjectiveRecommendUnDirectMsgId}
                    storageKey={storageKey}
                    isHistoryMsg={isHistoryMsg}
                    showLineText={!maxShowLen}
                    showSkeleton={showSkeleton}
                    msgId={props?.msgId || ''}
                    ext={ext}
                    isLast={lastMsgId === msgId}
                />

                {!isEmpty(expertData?.list) && (
                    <ExpertRecommendation
                        isLogin={userData?.isLogin || false}
                        expertData={expertData || {}}
                        // 这里只触发组合卡逻辑
                        updateCombinationCollectedInfoAndExpert={updateCollectedInfoAndSku}
                        // 组合卡中失效应该导向下一个组合卡而非定向卡
                        adjectiveRecommendExpertMsgId={adjectiveRecommendUnDirectMsgId}
                        isExpertDisabled={isSkuDisabled}
                        isNoUndirectService={hideTopSkuArea && isEmpty(skuData?.list)}
                        mode='combination'
                        maxShowLen={getShowExpertMaxLen()}
                        showSkeleton={showSkeleton}
                        storageKey={storageKey}
                        ext={ext}
                        msgId={msgId || ''}
                    />
                )}
            </View>
            <Portal>
                {collectedInfo && (showConsultForm || showConsultList) && (
                    <ConsultForm
                        showConsultForm={showConsultForm}
                        showConsultList={showConsultList}
                        collectedInfo={collectedInfo}
                        requireDesc={requireDesc}
                        handleAddPatient={handleAddPatient}
                        handleCloseConsultForm={handleCloseConsultForm}
                        jumpToTriage={jumpToTriage}
                        openEditPatient={openEditPatient}
                        state={state}
                        dispatch={dispatch}
                        openPatientPopType={openPatientPopType}
                        handleSelectPatient={handleSelectPatient}
                        selectPatientData={selectPatientData}
                        selectedSkuItemData={selectedSkuItemData}
                        loginSuccessfulCallback={loginSuccessfulCallback}
                        skuList={skuData?.list}
                        toastTip={skuData?.toastTip}
                        msgId={props?.msgId || ''}
                        sessionId={sessionId || ''}
                        qid={qid}
                        ext={ext}
                        setSelectPatientData={setSelectPatientData}
                        setShowSkeleton={setShowSkeleton}
                    />
                )}
            </Portal>
            <Portal>
                <Dialog open={isDialogOpen} onClose={handleCancel}>
                    <Dialog.Content>{dialogConfig?.content}</Dialog.Content>
                    <Dialog.Actions>
                        <Button onClick={handleCancel}>{dialogConfig?.cancelText}</Button>
                        <Button onClick={handleConfirm} style={{color: '#00c8c8'}}>
                            {dialogConfig?.confirmText}
                        </Button>
                    </Dialog.Actions>
                </Dialog>
            </Portal>
            <Portal>
                {/* 预检弹窗 */}
                <PreCheckOrderModal />
            </Portal>
        </View>
    );
};

export default memo(ImCollectedAndNoNDirectSku);
