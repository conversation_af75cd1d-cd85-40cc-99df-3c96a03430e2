/*
 * @Author: liu<PERSON>i
 * @Description: 常量
 */

// 上传图片
export const IMG = {
    tips: '补充病例信息(选填，仅用于医生判断)，处方、病例、检查报告等'
};

export const PATIENT_TEMP = [
    {
        key: 'gender',
        label: '性别',
        type: 'selectGender',
        defaultValue: '男',
        list: [
            {
                text: '女'
            },
            {
                text: '男'
            }
        ]
    },
    {
        key: 'age',
        label: '年龄',
        type: 'selectAge',
        value: '',
        placeholder: '请选择患者年龄'
    }
];

export const ACTION = {
    validate: 'validate',
    changePatient: 'changePatient',
    selectAge: 'selectAge',
    changeZhusu: 'changeZhusu',
    changeImages: 'changeImages'
};

export const patientInitInfo = {
    name: '',
    age: '',
    ageText: '',
    gender: '',
    zhusu: '',
    realName: 0,
    contactId: '',
    servicePhone: ''
};

export const selected = 'https://med-fe.cdn.bcebos.com/wz-mini/skuHook/selected.png';
export const unselected = 'https://med-fe.cdn.bcebos.com/wz-mini/skuHook/unSelected.png';
export const privacyTips = 'https://med-fe.cdn.bcebos.com/wz-mini/skuHook/privacyTips.png';
export const patientLoadingImg = 'https://med-fe.cdn.bcebos.com/wz-mini/skuHook/patientLoading.gif';
export const ZHUSU_MAX_TEXT = 500;
export const ZHUSU_MIN_TEXT = 10;
export const TOAST_TIME = 1000;
export const MAX_SCROLL_NUM = 200;
export const phoneNumberReg = /^1[3456789]\d{9}$/;
