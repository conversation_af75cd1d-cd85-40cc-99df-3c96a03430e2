export interface PatientList {
    contactId: string;
    name: string;
    age: string;
    gender: string;
    mouth: number;
    isCertified?: number; // 是否实名
}

/** 单个就诊人类型 */

export interface FetchLastQuestionParams {
    /**
     * 医生id
     */
    expertId: number;

    /**
     * 用户编辑的就诊人ID
     */
    contactId?: string;

    /**
     * 用户下单的skuId
     */
    skuId: number;

    /**
     * 是否有就诊人
     */
    hasPatient?: 1 | 0;
}

// eslint-disable-next-line no-shadow
export enum CertifiedUser {
    /** 1: 实名 */
    realName = 1,

    /** 2: 未实名 */
    noRealName = 0
}

export type CertifiedUserType =
    `${Extract<CertifiedUser, number>}` extends `${infer N extends number}` ? N : never;

export interface FetchLastQuestionResult {
    isCertifiedUser: CertifiedUserType;
    isLogin: number;
    patient: Patient;
    question: Question;
    statement: Statement[];
    formType: number;
    servicePhone?: string;
}

interface Statement {
    title: string;
    content: string[];
}

interface Question {
    content: string;
}

interface Patient {
    name: string;
    contactId: string;
    gender: string;
    isSelectSex: number;
    age: string;
    isSelectAge: number;
    birthday: string;
    isCertified: number;
    month: number;
}

// eslint-disable-next-line no-shadow
export enum Empower {
    /** 1: 授权 */
    yes = 1,

    /** 2: 未授权 */
    not = 0
}

export type EmpowerType = `${Extract<Empower, number>}` extends `${infer N extends number}`
    ? N
    : never;

export interface QuickFillParams {
    op_type: EmpowerType;
}

export interface QuickFillResult {
    patient: {
        name: string;
        gender: string;
        age: number;
        month: number;
    };
}
