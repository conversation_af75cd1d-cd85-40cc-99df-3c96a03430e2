/*
 *  @Author: lijing106
 * @Description: 就诊人操作弹层
 */
import cx from 'classnames';
import {pxTransform} from '@tarojs/taro';
import {View, Text, Form} from '@tarojs/components';
import {WiseRightArrow} from '@baidu/wz-taro-tools-icons';
import {memo, FC, useMemo, useCallback, useState, useEffect, useRef} from 'react';
import {
    Popup,
    Button,
    FormCard,
    SelectAgePopup,
    SafeArea,
    Uploader,
    Dialog
} from '@baidu/wz-taro-tools-core';
import type {ItemInfo} from '@baidu/wz-taro-tools-core/form-input-item';

import {showToast} from '../../../utils/customShowToast';
import {useMsgDataSetController} from '../../../hooks/triageStream/dataController';
import {getUserBizActionReq} from '../../../models/services/triageStream';
import {ubcCommonClkSend, ubcCommonViewSend} from '../../../utils/generalFunction/ubc';
import {
    TIRAGE_MAX_PIC_NUM,
    BUCKET_NAME,
    ONLINE_HOST,
    SINGLE_NUM,
    H5_UPLOAD_PIC_NUM_TIPS
} from '../../../constants/common';
import {preAndUploadPic} from '../../../utils/basicAbility/upload';
import {API_HOST} from '../../../models/apis/host';
import {formatAgeText} from '../common/utils';
import type {CONSULT_TYPE} from '../index.d';

import {getBdFileMapAtom} from '../../../store/triageStreamAtom/bdFileMapAtom';
import {PATIENT_TEMP, TOAST_TIME, ZHUSU_MAX_TEXT, IMG, patientInitInfo} from './const';
import ChoosePatientList from './components/ChoosePatientList';
import DiseaseDesc from './components/DiseaseDesc';
import RequireDesc from './components/RequireDesc';
import type {IConsultFormProps, PatientInfo} from './index.d';
import styles from './index.module.less';

interface FileProps extends Uploader.File {
    fileName?: string;
}

const bucketConfName =
    ONLINE_HOST.indexOf(API_HOST) > -1 ? BUCKET_NAME[2] : `${BUCKET_NAME[2]}-test`;

const ConsultForm: FC<IConsultFormProps> = (props: IConsultFormProps) => {
    const {
        showConsultForm,
        collectedInfo,
        requireDesc,
        handleAddPatient,
        handleCloseConsultForm,
        jumpToTriage,
        showConsultList,
        state,
        dispatch,
        openPatientPopType,
        selectPatientData,
        handleSelectPatient,
        selectedSkuItemData,
        loginSuccessfulCallback,
        msgId,
        sessionId,
        skuList,
        setSelectPatientData,
        setShowSkeleton,
        qid,
        ext,
        toastTip
    } = props;

    const initImg: FileProps[] =
        state?.images?.map(item => {
            return {
                fileName: item.fileName || '',
                url: item.small || ''
            };
        }) || [];
    const [openAgeSelect, setOpenAgeSelect] = useState(false);
    const [isAddPatient, setIsAddPatient] = useState(false); // 是否是点击添加就诊人
    const {updateMsgData} = useMsgDataSetController({msgId});
    const [editPopType, setEditPopType] = useState<CONSULT_TYPE>('consult');
    const [isDisabled, setIsDisabled] = useState(false);
    const [files, setFiles] = useState<FileProps[]>(initImg || []);

    const [isDialogOpen, setIsDialogOpen] = useState(false); // 就诊人科室性别冲突弹窗
    const [dialogConfig, setDialogConfig] = useState({
        content: '',
        confirmText: '确认',
        cancelText: '取消',
        onConfirm: () => {}
    });

    const patirentRef = useRef(false);

    const memoPatientListStyle = useMemo(() => {
        return {maxHeight: `calc(80vh - ${pxTransform(133)})`};
    }, []);

    const handleConfirm = () => {
        dialogConfig?.onConfirm?.();
        setIsDialogOpen(false);
    };

    const handleCancel = () => {
        setIsDialogOpen(false);
    };

    /**
     * ImConsultForm 选择年龄 上报
     */
    const chooseAge = useCallback(editStatus => {
        editStatus === 1 && setOpenAgeSelect(true);

        ubcCommonClkSend({
            value: 'no_direct_sku_typewriter_age'
        });
    }, []);

    // item 选项切换回调
    const onInputItemChange = useCallback(
        (key, itemData: PatientInfo) => {
            if (key === 'gender') {
                dispatch({payload: {gender: itemData?.gender || ''}});
                const genderType = itemData?.gender === '男' ? 1 : 0;
                ubcCommonClkSend({
                    value: `no_direct_sku_typewriter_gender_${genderType}`
                });
            } else {
                dispatch({payload: itemData});
            }
        },
        [dispatch]
    );
    // 新增患者
    const addPatient = useCallback(() => {
        setIsAddPatient(true);
        handleAddPatient && handleAddPatient();
        dispatch({
            payload: {
                ...patientInitInfo,
                zhusu: state?.zhusu || ''
            }
        });

        ubcCommonClkSend({
            value: 'ImAIRecommendUnDirect_patient_add',
            ext: {
                product_info: {
                    msgId,
                    ...ext
                }
            }
        });
    }, [dispatch, ext, handleAddPatient, msgId, state?.zhusu]);

    // 根据当前选中的skuId查找最新的sku数据
    const findSelectedSku = useCallback(
        (skuId: string) => {
            return skuList?.find(skuItem => skuItem.skuId === skuId);
        },
        [skuList]
    );

    const showToastMsg = useCallback(() => {
        showToast({
            title: isAddPatient ? '新增成功' : '保存成功',
            icon: 'none'
        });
    }, [isAddPatient]);

    useEffect(() => {
        // 校验表单是否可以提交（非新增时校验性别和年龄、主诉，新增时校验性别和年龄）
        if (
            (!isAddPatient && (!state?.zhusu?.trim() || !state?.gender || !state?.age)) ||
            (isAddPatient && (!state?.gender || !state?.age))
        ) {
            setIsDisabled(true);
        } else {
            setIsDisabled(false);
        }
    }, [isAddPatient, state?.age, state?.gender, state?.zhusu]);

    // 关闭新增或者编辑弹层
    const handleCloseConsultPop = useCallback(() => {
        setIsAddPatient(false);
        handleCloseConsultForm('consult');
    }, [handleCloseConsultForm]);

    useEffect(() => {
        if (patirentRef.current) {
            // 兼容免费skuId为空的情况,注意，当sku为免费咨询，并且不可点击时，不能走下单逻辑
            if (!selectedSkuItemData?.skuId) {
                if (selectedSkuItemData?.btn?.disabled) {
                    showToast({
                        title: toastTip,
                        icon: 'none'
                    });
                } else {
                    loginSuccessfulCallback(selectedSkuItemData || {});
                }
            } else {
                const selectSku = findSelectedSku(selectedSkuItemData?.skuId || '');
                selectSku && loginSuccessfulCallback(selectSku || {});
            }
            handleCloseConsultForm(editPopType);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [patirentRef.current]);

    const handleUploadImage = useCallback(images => {
        const imageList: string[] = [];
        images.map(item => {
            imageList.push(item?.fileName);
        });
        return imageList;
    }, []);

    // 非定向sku 确认下单及科室冲突
    const handleInteractionWithConfirm = useCallback(
        (
            options: {
                interactionInfo?: {
                    confirm?: {
                        title: string;
                        confirmText: string;
                        cancelText: string;
                        url?: string;
                    };
                };
            },
            onConfirm: () => void
        ) => {
            if (options?.interactionInfo?.confirm) {
                setDialogConfig({
                    content: options?.interactionInfo?.confirm?.title,
                    confirmText: options?.interactionInfo?.confirm?.confirmText || '确认下单',
                    cancelText: options?.interactionInfo?.confirm?.cancelText || '返回修改',
                    onConfirm
                });
                setIsDialogOpen(true);
            } else {
                onConfirm();
            }
        },
        []
    );

    const handleUpdateMsgData = useCallback(
        async (params, type) => {
            // eslint-disable-next-line no-console
            const {age, gender, zhusu, contactId, images} = params;
            const patientInfo =
                contactId && type === 'consultList'
                    ? {contactId}
                    : {
                        age,
                        gender
                    };

            setEditPopType(type);
            const paramsOpt = !isAddPatient ? {zhusu, images: handleUploadImage(images)} : {};
            const paramsData = {
                bizActionType: 'editZhusu' as const,
                chatData: {
                    sessionId
                },
                bizActionData: {
                    editZhusuInfo: {
                        patientInfo,
                        msgId,
                        ...paramsOpt,
                        serviceType: 'undirectService',
                        qid
                    }
                }
            };
            setIsDisabled(true);
            setShowSkeleton?.(true);
            const [err, data] = await getUserBizActionReq<'editZhusu'>(paramsData);
            try {
                if (!err) {
                    data?.data?.message && updateMsgData(data?.data?.message[0] || {});
                    const btnInfo =
                        data?.data?.message?.[0]?.data?.content?.data?.content?.skuData?.btnInfo;

                    // 如果是医生列表直接跳走
                    if (openPatientPopType === 'expert') {
                        showToastMsg();
                        jumpToTriage?.();
                    }
                    // sku咨询直接走下单
                    else if (
                        openPatientPopType === 'sku' &&
                        !(isAddPatient && !state.zhusu?.trim())
                    ) {
                        handleInteractionWithConfirm(btnInfo, () => (patirentRef.current = true));
                    } else {
                        setSelectPatientData?.(
                            data?.data?.message?.[0]?.data?.content?.data?.content?.collectedInfo
                                ?.curPatient || {}
                        );
                        showToastMsg();
                    }
                    handleCloseConsultForm(type);
                    setIsDisabled(false);
                    setIsAddPatient(false);
                }
            } catch (error) {
                setIsDisabled(false);
                console.error(error);
            }
        },
        [
            qid,
            handleCloseConsultForm,
            handleUploadImage,
            jumpToTriage,
            isAddPatient,
            msgId,
            state.zhusu,
            openPatientPopType,
            sessionId,
            setSelectPatientData,
            setShowSkeleton,
            showToastMsg,
            updateMsgData,
            handleInteractionWithConfirm
        ]
    );

    /**
     * 提交数据
     */
    const onSubmit = useCallback(
        async type => {
            const {zhusu: content = '', gender = ''} = state;
            if (!isAddPatient && (state?.zhusu?.length || 0) > ZHUSU_MAX_TEXT) {
                showToast({
                    title: `病情描述不能超过${ZHUSU_MAX_TEXT}个字符`,
                    icon: 'none',
                    duration: TOAST_TIME
                });

                return;
            }

            if (!state?.zhusu?.trim() && !isAddPatient) {
                showToast({
                    title: '病情描述不能为空',
                    icon: 'none',
                    duration: TOAST_TIME
                });

                return;
            }
            // 在就诊人模块选择就诊人信息时，会根据当前选择的就诊人信息，请求行为接口，刷新页面数据，更新就诊人模块信息
            if (type === 'consultList') {
                // eslint-disable-next-line max-len
                handleUpdateMsgData(
                    {contactId: selectPatientData?.contactId, zhusu: content || '', images: files},
                    type
                );
            } else {
                // 无就诊人时，就诊人模块新增就诊人
                handleUpdateMsgData(
                    {
                        gender,
                        age: state?.age,
                        zhusu: content,
                        skuId: selectedSkuItemData?.skuId || '',
                        images: files
                    },
                    type
                );
            }
            ubcCommonClkSend({
                value: `ImAIRecommendUnDirect_${type}_submit`,
                ext: {
                    product_info: {
                        msgId,
                        ...ext
                    }
                }
            });
        },
        [
            state,
            isAddPatient,
            msgId,
            ext,
            handleUpdateMsgData,
            selectPatientData?.contactId,
            files,
            selectedSkuItemData?.skuId
        ]
    );

    // 选择性别
    const getGenderChoose = useCallback(
        formItem => {
            return (
                <View className='wz-flex wz-row-right'>
                    {['男', '女'].map((genderItem, genderIndex) => {
                        return (
                            <View
                                key={genderIndex}
                                className={cx(
                                    styles.formGenderBtn,
                                    'wz-flex',
                                    'wz-col-center',
                                    'wz-row-center',
                                    {
                                        [styles.formGenderBtnActive]:
                                            formItem?.value === genderItem ||
                                            formItem?.defaultValue === genderItem
                                    },
                                    'wz-fs-45 wz-ml-24 wz-plr-48'
                                )}
                                onClick={() =>
                                    onInputItemChange &&
                                    onInputItemChange(formItem.key, {gender: genderItem})
                                }
                            >
                                {genderItem}
                            </View>
                        );
                    })}
                </View>
            );
        },
        [onInputItemChange]
    );

    // 选择年龄
    const getAgeChoose = useCallback(
        (formItem, editStatus) => {
            return (
                <View
                    className={cx('wz-flex wz-row-right wz-col-center colorGray wz-fs-45', [
                        editStatus === 1 ? '' : ''
                    ])}
                    onClick={() => chooseAge(editStatus)}
                >
                    {state.age ? (
                        <Text> {formatAgeText(state?.age)}</Text>
                    ) : (
                        <Text className={cx(styles.placeholderColor)}> {formItem.placeholder}</Text>
                    )}
                    {editStatus === 1 && <WiseRightArrow color='#B5B5B5' />}
                </View>
            );
        },
        [chooseAge, state.age]
    );

    const memoPatientEdit = useMemo(() => {
        return <View className='form-title'>{isAddPatient ? '新增患者' : '病情信息'}</View>;
    }, [isAddPatient]);

    const onUpload = useCallback(async () => {
        try {
            const remainNum = TIRAGE_MAX_PIC_NUM - files?.length;
            // eslint-disable-next-line max-len
            const imgData = await preAndUploadPic({
                count: remainNum > SINGLE_NUM ? SINGLE_NUM : remainNum,
                bucketConfName,
                remainNum,
                H5Tips: H5_UPLOAD_PIC_NUM_TIPS
            });
            if (imgData && imgData.length) {
                setFiles([
                    ...files,
                    ...imgData.map(({fileName, path, type, originalFileObj}) => ({
                        fileName,
                        type,
                        url: path,
                        name: originalFileObj?.name
                    }))
                ]);
            }
        } catch (err) {
            console.error('onUpload 出错：', err);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [files]);

    useEffect(() => {
        if (showConsultForm) {
            ubcCommonViewSend({
                value: 'no_direct_sku_typewriter_form'
            });
        }
    }, [showConsultForm]);

    useEffect(() => {
        if (showConsultList) {
            ubcCommonViewSend({
                value: 'no_direct_sku_typewriter_list'
            });
        }
    }, [showConsultList]);

    return (
        <View className={styles.consultForm}>
            <Popup
                open={showConsultForm}
                rounded
                placement='bottom'
                catchMove={false}
                title={memoPatientEdit}
                style={{
                    height: `calc(80vh - ${pxTransform(133)})`,
                    background: 'linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 18%, #F5F5F5 100%)',
                    zIndex: 1012,
                    borderRadius: `${pxTransform(63)} ${pxTransform(63)} 0 0`
                }}
                titleStyle={{
                    backgroundColor: 'rgb(245, 245, 245, 0)',
                    border: 'none'
                }}
                onClose={handleCloseConsultPop}
                className={cx(!showConsultList && styles['consult-popup'])}
            >
                <View className={styles.consultPopupContent}>
                    <View className={cx(styles.patientList, 'wz-mlr-30 wz-pb-30')}>
                        {!collectedInfo?.curPatient?.contactId && !isAddPatient && (
                            <View
                                className={cx(
                                    styles.patientInfoTitle,
                                    'wz-flex wz-col-center wz-fs-54 wz-fw-500 wz-mlr-39'
                                )}
                            >
                                患者信息
                            </View>
                        )}
                        <Form>
                            <FormCard className={styles.formCard}>
                                {(PATIENT_TEMP as ItemInfo[]).map((item: ItemInfo) => {
                                    const editStatus = 1;

                                    const formItem = Object.assign(
                                        {},
                                        item,
                                        item?.key
                                            ? {
                                                value: state[
                                                      item?.key as
                                                          | 'age'
                                                          | 'ageText'
                                                          | 'gender'
                                                          | 'name'
                                                ],
                                                defaultValue:
                                                      state[
                                                          item?.key as
                                                              | 'age'
                                                              | 'ageText'
                                                              | 'gender'
                                                              | 'name'
                                                      ],
                                                editStatus
                                            }
                                            : {}
                                    );

                                    return (
                                        <View
                                            key={item.key}
                                            className={cx(
                                                'wz-flex wz-row-between wz-col-center',
                                                'c-color-prime wz-fs-48 wz-mlr-39',
                                                'wz-ptb-54'
                                            )}
                                        >
                                            <View>
                                                <Text className='wz-fw-500'>{item.label}</Text>
                                            </View>
                                            {item?.key === 'age'
                                                ? getAgeChoose(formItem, editStatus)
                                                : getGenderChoose(formItem)}
                                        </View>
                                    );
                                })}
                            </FormCard>
                        </Form>
                        {!collectedInfo?.curPatient?.contactId && !isAddPatient && (
                            <>
                                {/* 病情描述 */}
                                <DiseaseDesc {...{state, dispatch}} />
                                <View className='wz-pt-54 wz-ml-39'>
                                    <View className='wz-fs-48 wz-mb-45 wz-fw-500'>上传图片</View>
                                    <Uploader
                                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                        // @ts-expect-error
                                        value={files}
                                        imageClassName={styles.uploadImage}
                                        multiple
                                        maxFiles={TIRAGE_MAX_PIC_NUM}
                                        tips={IMG.tips}
                                        onUpload={onUpload}
                                        onChange={setFiles}
                                        bdFileMap={
                                            process.env.TARO_ENV === 'swan'
                                                ? getBdFileMapAtom()
                                                : undefined
                                        }
                                    />
                                </View>
                            </>
                        )}
                    </View>
                    {/* 接诊要求 无就诊人时会展示 */}
                    {requireDesc && !isAddPatient && <RequireDesc requireDesc={requireDesc} />}
                </View>
                <Popup.Button>
                    <View className={cx(styles['patient-list-btn'], ' wz-ptb-24 wz-plr-54')}>
                        <Button
                            color='default'
                            size='large'
                            block
                            className={cx(styles['patient-list-btn-submit'], 'wz-fw-500')}
                            onClick={() => onSubmit('consult')}
                            disabled={isDisabled}
                        >
                            <Text className='wz-fs-54'>
                                {openPatientPopType === 'sku' ? '立即咨询' : '完成'}
                            </Text>
                        </Button>
                        <SafeArea
                            id='safearea'
                            style={{backgroundColor: '#fff'}}
                            position='bottom'
                        />
                    </View>
                </Popup.Button>
                <Popup.Close />
                <Popup.Backdrop open={showConsultForm} style={{zIndex: 1011}} />
            </Popup>
            <Popup
                open={showConsultList}
                placement='bottom'
                title={<View catchMove>病情信息</View>}
                rounded
                catchMove={false}
                style={{
                    ...memoPatientListStyle,
                    background: 'linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 18%, #F5F5F5 100%)',
                    zIndex: 1011,
                    borderRadius: `${pxTransform(63)} ${pxTransform(63)} 0 0`,
                    height: `calc(80vh - ${pxTransform(133)})`
                }}
                titleStyle={{background: 'rgb(245, 245, 245, 0)', border: 'none'}}
                onClose={() => handleCloseConsultForm('consultList')}
                className={styles['consult-popup']}
            >
                <Popup.Close />
                <View className={styles.consultPopupContent}>
                    <View className={cx(styles.patientList, 'wz-mlr-30')}>
                        <ChoosePatientList
                            {...{
                                collectedInfo,
                                onInputItemChange,
                                handleAddPatient: addPatient,
                                selectPatientData,
                                handleSelectPatient,
                                state,
                                dispatch,
                                openPatientPopType,
                                files,
                                TIRAGE_MAX_PIC_NUM,
                                IMG,
                                onUpload,
                                setFiles
                            }}
                        />
                    </View>
                    {/* 接诊要求 */}
                    {requireDesc && <RequireDesc requireDesc={requireDesc} />}
                </View>
                <Popup.Button>
                    <View className={cx(styles['patient-list-btn'], ' wz-ptb-24 wz-plr-54')}>
                        <Button
                            color='default'
                            size='large'
                            block
                            className={cx(styles['patient-list-btn-submit'], 'wz-fw-500')}
                            onClick={() => onSubmit('consultList')}
                            disabled={isDisabled}
                        >
                            <Text className='wz-fs-54'>完成</Text>
                        </Button>
                        <SafeArea
                            id='safearea'
                            style={{backgroundColor: '#fff'}}
                            position='bottom'
                        />
                    </View>
                </Popup.Button>
                <Popup.Backdrop open={showConsultList} style={{zIndex: 1010}} />
            </Popup>
            <SelectAgePopup
                open={openAgeSelect}
                interval='0,120'
                key={state.age}
                defaultValue={(state.age as string) || '20'}
                onCancel={() => setOpenAgeSelect(false)}
                onConfirm={v => {
                    setOpenAgeSelect(false);
                    dispatch({
                        payload: {
                            age: v?.value
                        }
                    });
                }}
            />
            <Dialog open={isDialogOpen} onClose={handleCancel}>
                <Dialog.Content>{dialogConfig?.content}</Dialog.Content>
                <Dialog.Actions>
                    <Button onClick={handleCancel}>{dialogConfig?.cancelText}</Button>
                    <Button onClick={handleConfirm} color='success'>
                        {dialogConfig?.confirmText}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </View>
    );
};
export default memo(ConsultForm);
