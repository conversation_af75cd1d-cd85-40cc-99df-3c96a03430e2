import {MsgId} from '../../typings';

import type {MsgMetaData} from '../../store/triageStreamAtom/index.type';
import type {expertDataProps} from '../ImCollectedAndReExpertTypewriter/index.d';
import type {CollectedInfoProps} from './PatientCard/index.d';
import type {RequireDescProps} from './ConsultForm/index.d';

export interface ExtInfo {
    [key: string]: string | number;
}

export interface ImCollectedAndNoNDirectSkuProps {
    cardId: string;
    cardName: string;
    version: number;
    data: {
        content: ImNoNDirectSkuPData;
        ext?: ExtInfo;
        cardStyle?: {
            // 1: 默认样式，无打字机效果——渲染ImCollectedAndNoDirectSku组件；2：打字机效果样式——渲染ImCollectedAndNoDirectSkuTypewriter，3：组合卡样式
            renderType: number;
        };
    };
    msgId: MsgId;
    localExt?: MsgMetaData['localExt'];
}

// 就诊卡片和sku数据结构
export interface ImNoNDirectSkuPData {
    requireDesc: RequireDescProps;
    collectedInfo: CollectedInfoProps;
    skuData: ImUndirectServicetCon;
    lineText?: string;
    isExpired?: boolean;
    hideTopSkuArea?: boolean;
    expertData?: expertDataProps;
    maxShowLen?: number;
}

// sku: 非定向服务；patient：修改就诊人或主诉；expert：专家列表
export type OpenPatientType = 'sku' | 'patient' | 'expert';

export type CONSULT_TYPE = 'consultList' | 'consult'; // consultList是就诊人选择弹层，consult是就诊人编辑弹层

export interface IDetailInfo {
    title?: string;
    doctorTitle?: string;
    discount?: string;
    features?: string[];
    explainTitle?: string;
    explains?: string[];
    oldPrice?: string; // 服务价格
    finalPrice?: string; // 合计价格
    btn?: string;
    promotionReductionPrice?: string; // 平台立减 活动优惠(减价)
    couponReductionPrice?: string; // 用券优惠(减价)
}

export interface IConfirm {
    title?: string;
    confirmText?: string;
    cancelText?: string;
}

export interface IBtn {
    interaction?: string;
    interactionInfo?: {
        url?: string;
        confirm?: IConfirm;
        params?: Record<string, unknown>;
    };
    value?: string;
    disabled?: boolean;
}

export interface TitleIconItem {
    type?: 'img' | 'icon';
    url?: string;
}

export interface undirectService {
    title?: string;
    priceText?: string;
    orgPrice?: string;
    salePrice?: number;
    promotionPrice?: number;
    sub?: string;
    detailInfo?: IDetailInfo;
    skuId?: string;
    tag?: string;
    btn?: IBtn;
    titleIcon?: TitleIconItem[];
}
export interface DoctorDisplayArea {
    avatars: string[];
    words: string;
    isCarousel: boolean;
}
export interface TopArea {
    doctorDisplayArea: DoctorDisplayArea;
}
export interface ImUndirectServicetCon {
    btnInfo?: IBtn;
    subText?: string;
    list?: undirectService[] | [];
    topArea?: TopArea;
    titleImg?: string;
    leftIcon?: string;
    tipText?: string;
    serviceTip?: string;
    toastTip?: string;
    tagArr?: string[];
}
