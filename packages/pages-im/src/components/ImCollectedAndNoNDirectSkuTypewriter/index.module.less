.skuContainer {
    width: 100%;
    background: #fff;
    border-radius: 63px;
}

.undirectServiceWrapper {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.expand {
    max-height: 10000px; // 设为足够大以容纳内容
}

.gradient {
    background-repeat: no-repeat;
    background-size: calc(100% - 6px) 100%;
    background-position: 3px 0;
    background-image: linear-gradient(to bottom, #e9f5fd calc(100% - 132px), #fff 100%);
}
