import {View} from '@tarojs/components';
import {showLoading, hideLoading} from '@tarojs/taro';
import {memo, type FC, useCallback, useState, useRef, useEffect} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';
import {
    useGetUserData,
    useGetSessionId,
    useUpdateUserData,
    useGetGuahaoRecommendExpertMsgId,
    useGetAdjectiveRecommendExpertMsgId,
    useGetAdjectiveRecommendUnDirectMsgId
} from '../../hooks/triageStream/pageDataController';
import {navigate} from '../../utils/basicAbility/commonNavigate';
import {updateTriageStreamMsgAtom} from '../../store/triageStreamAtom/msg';
import {useMsgDataGetController} from '../../hooks/triageStream/dataController';
import {getUserBizActionReq} from '../../models/services/triageStream';
import {showToast} from '../../utils/customShowToast';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../utils/generalFunction/ubc';
import {useConversationDataController} from '../../hooks/triageStream/useConversationDataController';
import type {userInfoItem} from '../../models/services/triageStream/index.d';
import {ServiceTypeMap} from '../../constants/common';
import {useScrollControl} from '../../hooks/triageStream/useScrollControl';
import {imgUrlMap} from '../../constants/resourcesOnBos';
import ExpertRecommendation from './ExpertRecommendation';
import type {ImCollectedAndReExpertProps, expertItem} from './index.d';
import styles from './index.module.less';

/**
 *
 * @description 挂号推荐专家服务卡
 * @returns
 */
const ImCollectedAndReExpert: FC<ImCollectedAndReExpertProps> = props => {
    const {content, ext = {}} = props?.data || {};
    const {scene = '', descLimit = {}} = ext || {};
    const {limitLength = 0, limitToast = ''} = descLimit || {};
    const {collectedInfo, expertData, isExpired} = content;
    const {complaintDetail} = collectedInfo || {};
    const openPatientPopTypeRef = useRef<boolean>(false); // 存储点开来源类型，用于判断是否发生了变化
    const [selectedExpertItemData, setSelectedExpertItemData] = useState<expertItem>(
        (expertData?.list || [])[0]
    );
    const [isExpertDisabled, setIsExpertDisabled] = useState(false);

    const {userData} = useGetUserData();

    const prevIsLoginRef = useRef<boolean>(userData?.isLogin || false); // 存储之前的值，用于判断是否发生了变化

    const sessionId = useGetSessionId();
    const {updateUserData} = useUpdateUserData();
    const {guahaoRecommendExpertMsgId} = useGetGuahaoRecommendExpertMsgId();
    const {adjectiveRecommendExpertMsgId} = useGetAdjectiveRecommendExpertMsgId();
    const {adjectiveRecommendUnDirectMsgId} = useGetAdjectiveRecommendUnDirectMsgId();

    const {updateSessionCapsulesTools} = useConversationDataController();
    const {data: docServiceData} = useMsgDataGetController({msgId: adjectiveRecommendExpertMsgId});
    const {data: undirectServiceData} = useMsgDataGetController({
        msgId: adjectiveRecommendUnDirectMsgId
    });

    const {scrollToMessage} = useScrollControl();
    const recommendText = '为您推荐相关医生';
    const recommendConditionText = '根据病情为您推荐相关医生';

    useEffect(() => {
        // isExpired接口端判断无效卡,直接禁用当前卡
        if (isExpired || guahaoRecommendExpertMsgId !== props?.msgId) {
            setIsExpertDisabled(true);
        }
    }, [guahaoRecommendExpertMsgId, isExpired, props?.msgId]);

    const addPatientToast = useCallback(() => {
        showToast({
            title: '请完善您的提问信息',
            icon: 'none'
        });
    }, []);

    const jumpToTriage = useCallback(btnInfo => {
        const {interaction, interactionInfo} = btnInfo;
        if (interaction && interaction === 'openLink' && interactionInfo?.url) {
            navigate({
                openType: 'navigate',
                url: interactionInfo?.url
            });
        }
    }, []);

    // 更新卡片数据：未登录转登录后，更新两类服务卡
    const handleMessageUpdate = useCallback(
        messageList => {
            messageList.forEach(messageItem => {
                updateTriageStreamMsgAtom(`${sessionId}_${messageItem?.meta?.msgId}`, messageItem, {
                    _debugSymbol: 'loginUpdate'
                });
            });
        },
        [sessionId]
    );

    // 用户行为接口数据参数
    const handleParams = useCallback(() => {
        const userLoginInfoListData: userInfoItem[] = [];
        // 合并两个消息的数据
        // 经与齐冰确认在页面中登录过后，需要将最新的2类卡片（定向、非定向），按约定的协议传给后端，用于卡片刷新
        [undirectServiceData, docServiceData].forEach(item => {
            if (item && item?.data && item?.data?.content) {
                const {cardId} = item?.data?.content || '';
                const {msgId} = item?.meta || '';
                const {collectedInfo} = item?.data?.content?.data?.content || {};
                const compParam = {
                    msgId,
                    patientInfo: collectedInfo?.curPatient,
                    serviceType: ServiceTypeMap[cardId],
                    zhusu: collectedInfo?.clinicalDesc || '',
                    qid: collectedInfo?.qid
                };
                userLoginInfoListData.push(compParam);
            }
        });
        const params = {
            bizActionType: 'userLogin' as const,
            chatData: {
                sessionId
            },
            bizActionData: {
                userLoginInfoList: {
                    msgList: userLoginInfoListData
                }
            }
        };
        return params;
    }, [docServiceData, sessionId, undirectServiceData]);

    // 更新服务卡数据，重新渲染卡片组件
    const updateCollectedInfoAndExpert = useCallback(
        async (selectExpert?: expertItem) => {
            if (isExpertDisabled) {
                showToast({
                    title: isExpired
                        ? '服务卡已失效，你可向健康管家重新咨询'
                        : '当前推荐已失效，请点击新的服务卡',
                    icon: 'none'
                });
                sessionId &&
                    guahaoRecommendExpertMsgId &&
                    scrollToMessage(guahaoRecommendExpertMsgId, 'updateCollectedInfoAndExpert');
                ubcCommonClkSend({
                    value: `ImGuahaoRecommendExpert_${scene}_disable`,
                    ext: {
                        product_info: {
                            ...ext
                        }
                    }
                });

                return;
            }

            selectExpert && setSelectedExpertItemData(selectExpert);

            // 未登录
            if (sessionId && !prevIsLoginRef.current && !userData?.isLogin) {
                const params = handleParams();
                showLoading({
                    title: '登录中...',
                    mask: true
                });
                const [err, data] = await getUserBizActionReq<'userLogin'>(params);
                if (!err) {
                    data?.data?.message && handleMessageUpdate(data?.data?.message || []);
                    data?.data?.userData && updateUserData(data?.data?.userData);
                    // 更新胶囊工具数据
                    data?.data?.toolData?.capsules &&
                        updateSessionCapsulesTools(data?.data?.toolData?.capsules);
                    prevIsLoginRef.current = data?.data?.userData?.isLogin || false;
                    openPatientPopTypeRef.current = true;
                    hideLoading();
                }
            } else {
                selectExpert && jumpToTriage(selectExpert?.btnInfo);
            }
        },
        [
            isExpired,
            isExpertDisabled,
            sessionId,
            userData?.isLogin,
            guahaoRecommendExpertMsgId,
            scene,
            ext,
            handleParams,
            handleMessageUpdate,
            updateUserData,
            updateSessionCapsulesTools,
            collectedInfo?.curPatient?.contactId,
            collectedInfo?.clinicalDesc,
            addPatientToast,
            limitLength,
            jumpToTriage,
            limitToast,
            props?.msgId
        ]
    );

    // 根据当前选中的skuId查找最新的sku数据
    const findSelectedExpert = useCallback(
        (docID: string) => {
            return expertData.list?.find(expert => expert.docID === docID);
        },
        [expertData]
    );

    // 监听登录发生变化后的数据处理
    useEffect(() => {
        if (openPatientPopTypeRef.current) {
            const selectExpert = findSelectedExpert(selectedExpertItemData?.docID || '');
            // 直接走跳转逻辑
            selectExpert && jumpToTriage(selectExpert?.btnInfo);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [openPatientPopTypeRef.current]);

    useEffect(() => {
        ubcCommonViewSend({
            value: 'ImGuahaoRecommendExpert',
            ext: {
                product_info: {
                    msgId: props?.msgId || '',
                    ...ext
                }
            }
        });
    }, [ext, props?.msgId]);

    return (
        <View className={styles.guahaoWrapper}>
            {complaintDetail ? (
                <View className={styles.recDescWrapper}>
                    <View className={cx(styles.title, 'wz-flex wz-fw-700')}>
                        <WImage
                            className={cx(styles.titleIcon, 'wz-mr-9')}
                            src={imgUrlMap.guahaoVitaCardTitleIcon}
                        />
                        {recommendConditionText}
                    </View>
                    <View className={cx(styles.desc, 'wz-fs-14')}>
                        <View className={cx(styles.descLabel, 'wz-fw-700')}>病情描述：</View>
                        {complaintDetail}
                    </View>
                </View>
            ) : (
                <View className={cx(styles.onlyTitle, 'wz-fs-51 wz-plr-45 wz-pb-45 wz-fw-500')}>
                    {recommendText}
                </View>
            )}
            <View
                className={cx(
                    styles.doctorListWrapper,
                    complaintDetail ? styles.doctorTopColor : '',
                    'wz-fs-14'
                )}
            >
                <ExpertRecommendation
                    expertData={expertData}
                    isExpertDisabled={isExpertDisabled}
                    isLogin={userData?.isLogin || false}
                    updateCollectedInfoAndExpert={updateCollectedInfoAndExpert}
                    guahaoRecommendExpertMsgId={guahaoRecommendExpertMsgId}
                    msgId={props?.msgId || ''}
                    ext={ext}
                />
            </View>
        </View>
    );
};

export default memo(ImCollectedAndReExpert);
