import {MsgId} from '../../typings';

export interface ImCollectedAndReExpertProps {
    cardId: string;
    cardName: string;
    version: number;
    data: {
        content: ImCollectedAndReExpertPData;
        ext?: {
            scene?: string;
            descLimit?: DescLimit;
        };
    };
    msgId: MsgId;
}

export interface DescLimit {
    limitLength?: number;
    limitToast?: string;
}

// 就诊卡片和sku数据结构
export interface ImCollectedAndReExpertPData {
    requireDesc: {
        titleCon: {
            type: 'text' | 'icon';
            value: string;
        }[];
        descList: {
            title: string;
            desc: string[];
        }[];
    };
    collectedInfo: {
        clinicalDesc?: string; // 病情描述
        complaintDetail?: string; // 挂号病情描述
    };
    expertData: expertDataProps;
    lineText?: string;
    isExpired?: boolean;
}

export interface IBtn {
    interaction?: string;
    interactionInfo?: {
        url?: string;
    };
    value?: string;
}

export interface typeValObjData {
    type: string;
    value: string;
}

export interface pennantItemData {
    name: string;
    count: number;
}

export interface attributeTagItem {
    text: string;
    key: string;
    color: string;
    borderColor: string;
}

export interface topLabelItem {
    text: string;
    key: string;
    bgColor: string;
    borderColor: string;
}

export interface indicatorItem extends typeValObjData {
    highLightColor?: string;
}

export interface expertItem {
    expertId: string;
    coreId: string;
    docID: string;
    expertName: string;
    expertPic: string;
    expertLevel?: string;
    expertHospital?: string;
    expertDepartment?: string;
    freeTag?: string;
    expertGoodAt?: typeValObjData[];
    price?: string;
    isOnline?: number;
    goodCommentCount?: number;
    goodCommentList?: pennantItemData[];
    ghCount?: string;
    showPortraitTag?: typeValObjData[];
    attributeTag?: attributeTagItem[];
    topLabels?: topLabelItem[];
    indicatorList?: indicatorItem[];
    actionInfo?: IBtn;
    btnInfo?: IBtn;
    recommendationReasons?: string;
    // 位置
    pos?: number;
}

export interface expertDataProps {
    title?: string;
    list?: expertItem[];
}
