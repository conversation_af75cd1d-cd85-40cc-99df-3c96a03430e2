.guahaoWrapper {
    position: relative;
    width: 100%;
    overflow: hidden;

    .recDescWrapper {
        position: relative;
        border: 3px solid #fff;
        background-color: #e9f5fd;
        padding: 54px 45px 48px;
        border-radius: 63px;
        margin-bottom: -45px;

        .title {
            line-height: 1;
            color: #272933;
            font-size: 48px;
        }

        .desc {
            position: relative;
            line-height: 66px;
            margin-top: 51px;
            color: #272933;
            padding-left: 60px;
            align-items: start;

            .descLabel {
                word-break: keep-all;
                display: inline;
            }
        }

        .desc::after {
            content: ' ';
            position: absolute;
            left: 24px;
            top: 27px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: url('https://med-fe.cdn.bcebos.com/wz-mini/vitaGuahaoDescPoint.png')
                no-repeat;
            background-size: 100% 100%;
        }

        .titleIcon {
            width: 60px;
            height: 60px;
        }
    }

    .recDescWrapper::after {
        content: ' ';
        display: block;
        width: 3px;
        height: 132px;
        background-color: #fff;
        position: absolute;
        left: -3px;
        bottom: -60px;
    }

    .recDescWrapper::before {
        content: ' ';
        display: block;
        width: 3px;
        height: 132px;
        background-color: #fff;
        position: absolute;
        right: -3px;
        bottom: -60px;
    }

    .doctorTopColor {
        padding-top: 45px;
        background: linear-gradient(to bottom, #e9f5fd 0, #fff 100px, #fff 100%);
        z-index: -1;
    }

    .doctorListWrapper {
        border-bottom-right-radius: 63px;
        border-bottom-left-radius: 63px;
        background-color: #fff;
    }

    .onlyTitle {
        position: relative;
        padding-top: 60px;
        border-top-left-radius: 63px;
        border-top-right-radius: 63px;
        background-color: #fff;
    }

    .onlyTitle::after {
        content: ' ';
        height: 1px;
        background-color: #ededf0;
        position: absolute;
        bottom: 0;
        left: 45px;
        right: 45px;
    }
}
