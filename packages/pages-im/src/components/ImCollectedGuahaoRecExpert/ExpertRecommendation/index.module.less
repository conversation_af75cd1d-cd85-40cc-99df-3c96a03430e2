.expertRecommendationWrapper {
    overflow: hidden;
}

.expertRecommendation {
    width: 100%;
    padding: 72px 45px 15px;
    border-radius: 45px;
    overflow: hidden;
    box-sizing: border-box;
}

.isDisabledContainer {
    position: absolute;
    background: #fff;
    inset: 0;
    opacity: 0.7;
    z-index: 30;
    border-radius: 63px;

    .isDisabledIcon {
        position: absolute;
        right: 0;
    }

    .ineffectiveIcon {
        width: 198px;
        height: 156px;
        position: absolute;
        top: 0;
        right: 84px;
    }
}

.doctorCard {
    overflow: hidden;
}

.line {
    width: 100%;
    height: 1px;
    background: #e6e6e6;
    overflow: hidden;
    margin-left: 147px;
    margin-top: 60px;
}

.recommendationIcon {
    margin-bottom: 63px;
}

.moreBtn {
    color: #858585;
    position: absolute;
    bottom: 0;
    background: #fff;
    height: 145px;
    width: 100%;
    z-index: 3;
    border-radius: 63px;
}

.moreBtn::before {
    content: ' ';
    display: block;
    position: absolute;
    top: -120px;
    left: 0;
    height: 120px;
    width: 100%;
    background: linear-gradient(to bottom, rgb(255 255 255 / 0%), #fff);
}
