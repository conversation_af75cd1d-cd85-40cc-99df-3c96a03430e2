import {MsgId} from '../../../typings';

import {expertDataProps, expertItem} from '../index.d';
export interface ExpertRecommendationProps {
    expertData: expertDataProps;
    isLogin: boolean;
    updateCollectedInfoAndExpert: (expertData: expertItem) => void;
    isExpertDisabled?: boolean;
    guahaoRecommendExpertMsgId?: MsgId | undefined;
    msgId?: string;
    ext?: {
        [key: string]: string | number;
    };
}
