/* stylelint-disable */
.aiBotThinkContainer {
    padding: 73.5px 3px;
}
.aiBotThink {
    font-family: PingFang SC;
    .loadingIcon {
        display: flex;
        align-items: center;
        width: auto;
        aspect-ratio: unset;
        border-radius: unset;
        background: unset;
        opacity: unset;
        animation: unset;
        animation-direction: unset;
    }
    .dot {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: #b2eeee;
        margin-right: 21px;
        opacity: 0.6;
        animation: dotFlashing 1s infinite linear alternate;
    }
    .dot:nth-child(2) {
        animation-delay: 0.2s;
    }
    .dot:nth-child(3) {
        animation-delay: 0.4s;
        margin-right: 0;
    }
    @keyframes dotFlashing {
        0% {
            opacity: 0.2;
        }
        50%,
        100% {
            opacity: 1;
        }
    }
}
