import cx from 'classnames';
import {memo, type FC} from 'react';
import {View} from '@tarojs/components';
import {MarkDdownCard} from '@baidu/vita-ui-cards-common';

import type {CardsData} from '../../typings';

import styles from './index.module.less';

export interface IImThinkingDataType {
    text: string;
}

interface IProps {
    data: CardsData<IImThinkingDataType>['content'];
}

const ImThinking: FC<IProps> = () => {
    return (
        <MarkDdownCard className={styles.aiBotThinkContainer}>
            <View className={cx(styles.aiBotThink, 'wz-flex wz-row-left wz-col-center')}>
                <View className={cx(styles.loadingIcon)}>
                    <View className={styles.dot} />
                    <View className={styles.dot} />
                    <View className={styles.dot} />
                </View>
            </View>
        </MarkDdownCard>
    );
};

export default memo(ImThinking);
