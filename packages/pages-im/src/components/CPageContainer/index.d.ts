import type { ComponentClass, ReactNode, CSSProperties } from 'react';
import { ShareAppMessageReturn, ShareAppMessageObject } from '@tarojs/taro';

// import { CTopBarProps } from '@components/base/CTopBar/index.d';
// import { ILeaveProps } from '@components/card/LeaveDialog/index.d';
// import { GuideParams } from '@components/common/GuideWrapper';
// import type { TriageAdsData, TriageActionData, TriageImActionData, AnticipationPopup } from '@src/typings/cui';
// import type { BaseComponent, ShareConfig } from '@components/base/Base.d';
export interface BaseComponent {
    className?: string;
    customStyle?: string | CSSProperties;
    children?: ReactNode | ReactNode[];
}

export interface CPageContainerProps extends BaseComponent {

    /**
     * 背景色
     */
    background?: string;

    /**
     * 是否展示默认loading
     */
    hidePageLoading?: boolean;

    /**
     * topBar样式
     */
    // topBarProps?: CTopBarProps;
    topBarProps?: any;

    /**
     * 是否展示顶部标题栏
     */
    hdheader?: boolean;

    /**
     * 是否展示滚动后的 header，当 hdHeader 为 true 时生效
     * 当children 是scroll-view时，并且添加了onScroll事件时，滚动显示会失效
     */
    showScrollHeader?: boolean;

    /**
     * 是否开启骨架屏
     */
    showSkeleton?: boolean;

    /**
     * 骨架屏名称
     */
    skeletonName?: string;

    /**
     * 分享标题
     */
    shareTitle?: string;

    /**
     * 分享配置
     */
    // shareConfig?: ShareConfig;
    shareConfig?: any;

    /** 组件内自定义分享参数 */
    conponentShareInfo?: (param?: ShareAppMessageObject) => ShareAppMessageReturn;

    /**
     * 兜底挽留
     */
    // leaveData?: ILeaveProps;
    leaveData?: any;

    /**
     * 挽留弹窗数据
     */
    // adsData?: TriageAdsData;
    adsData?: any;

    /**
     * 进行中订单弹窗
     */
    actionData?: {
        // [key in string]: TriageActionData;
        [key in string]: any;
    };

    imActionData?: {
        // [key in string]: TriageImActionData;
        [key in string]: any;
    };

    /**
     * 定向问诊预期管理弹窗
     */
    // anticipationPopup?: AnticipationPopup;
    anticipationPopup?: any;

    /**
     * topbar自定义插槽
     */
    renderTopBar?: () => ReactNode;

    /**
     * 分享点击打点value
     */
    shareUbcVal?: string;

    onEntered?: () => void;

    /**
     * 点击返回按钮的回调
     */
    navigateCallBack?: () => void;

    onBackDetain?: (eventName, navigateBack, navigateHome) => void;
    backDetain?: boolean;

    /**
     * * GuideWrapper组件参数：距底距离
     */
    // guideParams?: GuideParams;
    guideParams?: any;
}

export interface IShareConfigProps {
    timestamp?: number;
    nonceStr?: string;
    signature?: string;
    appId?: string;
    url?: string;
}
declare const CPageContainer: ComponentClass<CPageContainerProps>;

export default CPageContainer;
