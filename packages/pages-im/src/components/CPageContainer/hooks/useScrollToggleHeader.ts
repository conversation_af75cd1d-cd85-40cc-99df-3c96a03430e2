import { BaseEventOrig, ScrollViewProps } from '@tarojs/components';
import { usePageScroll } from '@tarojs/taro';
import { useState } from 'react';

export const useScrollToggleHeader = (children: React.ReactNode, open: boolean) => {
    const [scrollHideHeader, setScrollHideHeader] = useState(false);
    const [showMyChildren, setShowMyChildren] = useState(false);
    const [scrollChildren, setScrollChildren] = useState<React.ReactNode>([]);

    // 如果关闭的话，就直接返回
    if (!open) {
        return [scrollHideHeader, showMyChildren, scrollChildren];
    }

    const toggleHeaderHandle = (top: number) => {
        if (top < 22) {
            setScrollHideHeader(false);
        } else {
            setScrollHideHeader(true);
        }
    };

    const scrollToToggleHeader = (e: BaseEventOrig<ScrollViewProps.onScrollDetail>) => {
        const top = e.detail?.scrollTop || 0;
        toggleHeaderHandle(top);
    };

    // 1. 如果插槽不是数组 且 用scroll-view包了一层， 就添加onScoll 来处理滚动
    const childrenCopy = children as React.ReactElement;
    if (!Array.isArray(childrenCopy) && childrenCopy.type === 'scroll-view') {
        const originProps = childrenCopy.props;
        // 如果没有添加了onScroll 就手动添加用来处理滚动
        if (!('onScroll' in originProps)) {
            originProps.onScroll = scrollToToggleHeader;
            childrenCopy.props = originProps;
            setScrollChildren(childrenCopy);
            setShowMyChildren(true);
        }
    } else {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        usePageScroll(e => {
            toggleHeaderHandle(e.scrollTop);
        });
    }

    return [scrollHideHeader, showMyChildren, scrollChildren];
};
