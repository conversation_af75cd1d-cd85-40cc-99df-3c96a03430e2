.bubble {
    background: linear-gradient(135deg, #00cfa3, #00d3ea);
    color: #fff;
    padding: 45px 63px 45px 36px;
    border-radius: 48px;
    min-width: 220px;
    font-size: 45px;
    line-height: 1.4;
    box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
    position: absolute !important;
    box-sizing: border-box;
    overflow: visible;
    margin-top: 20px;
    left: 30px;
    top: 100%;
}

.content {
    font-size: 45px;
    font-family: PingFang SC;
}

.triangle {
    position: absolute;
    top: -21px; /* 向上偏移，与气泡衔接处 */
    left: 48px;
    width: 0;
    height: 0;
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    border-bottom: 30px solid #00cfa3;
    z-index: 31;
}
