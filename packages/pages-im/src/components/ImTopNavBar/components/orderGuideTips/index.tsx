import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import {CLoginButton, Portal} from '@baidu/vita-ui-cards-common';
import {memo, type FC, useEffect, useCallback, useState} from 'react';
import {debounce} from 'lodash-es';

import {useGetUrlParams} from '../../../../hooks/common';
import {useCreateSessionhook} from '../../../../hooks/triageStream/session';
import {useGetUserData} from '../../../../hooks/triageStream/pageDataController';
import {useGetCardEventCallback} from '../../../../hooks/useGetCardEventCallback';
import {useHandleUserLoginBizAction} from '../../../../hooks/triageStream/useHandleUserBizAction';

import {ubcCommonClkSend, ubcCommonViewSend} from '../../../../utils/generalFunction/ubc';

import HistoryRecordPopup from '../../../HistoryRecordPopup';

import type {OrderGuideTipType} from './index.d';

import styles from './index.module.less';

// UBC 埋点常量
const UBC = {
    BASE_VALUE: 'top_tips_order_guide',
    VIEW: 'view',
    CLICK: 'clk'
} as const;

const DEBOUNCETIME = 1000;

/**
 * 订单引导提示组件
 *
 * 用于显示订单引导提示，包含标题信息和操作按钮
 * 处理用户导航和交互追踪
 *
 * @param {OrderGuideTipType} props - 组件属性
 * @param {Object} props.titleInfo - 标题区域信息
 * @param {Object} props.newConversationInfo - 新建会话按钮信息
 * @param {Object} props.historyRecordInfo - 历史记录按钮信息
 */
const OrderGuideTip: FC<OrderGuideTipType> = ({titleInfo, btnTools}) => {
    const {onOpenLink} = useGetCardEventCallback();
    const {isDirected = 0} = useGetUrlParams();
    const {createSession} = useCreateSessionhook();
    const {userData} = useGetUserData() || {};
    const {isLogin} = userData || {};

    const {handleLoginBizAction} = useHandleUserLoginBizAction();

    const [open, setOpen] = useState(false);

    // 组件挂载或 isDirected 变化时触发埋点
    useEffect(() => {
        ubcCommonViewSend({
            value: `${UBC.BASE_VALUE}_${UBC.VIEW}`,
            ext: {
                // eslint-disable-next-line camelcase
                product_info: {isDirected}
            }
        });
    }, [isDirected]);

    /**
     * 处理导航链接点击事件
     * @param {Object} key - 动作信息对象
     * @param {string} ubcVal - UBC 埋点值
     */
    const handleClickJump = useCallback(
        (key, ubcVal: string) => {
            const {interactionInfo} = key?.actionInfo || {};

            if (interactionInfo) {
                onOpenLink({info: interactionInfo});
            }

            ubcCommonClkSend({
                value: `${UBC.BASE_VALUE}_${ubcVal}_${UBC.CLICK}`,
                ext: {
                    // eslint-disable-next-line camelcase
                    product_info: {isDirected}
                }
            });
        },
        [isDirected, onOpenLink]
    );

    /**
     * 处理操作按钮点击事件
     * @param {Object} actionInfo - 动作信息对象
     */
    const handleClickBtn = useCallback(
        type => {
            if (type === 'historyRecord') {
                setOpen(true);
                handleLoginBizAction();
            }

            if (type === 'newConversation') {
                createSession();
            }

            ubcCommonClkSend({
                value: `${UBC.BASE_VALUE}_${type}_${UBC.CLICK}`,
                ext: {
                    // eslint-disable-next-line camelcase
                    product_info: {isDirected}
                }
            });
        },
        [createSession, handleLoginBizAction, isDirected]
    );

    /**
     * 渲染操作按钮，包含图标和标题
     * @param {Object} info - 按钮信息对象
     * @returns {JSX.Element} 按钮组件
     */
    const renderBtnDom = useCallback(
        info => {
            if (!info) return null;

            const {avatar, title, type} = info;
            return (
                <CLoginButton
                    isLogin={isLogin}
                    closeShowNewUserTag={true}
                    useH5CodeLogin={true}
                    onLoginFail={error => {
                        console.error('error', error);
                    }}
                    onLoginSuccess={debounce(
                        () => {
                            handleClickBtn(type);
                        },
                        DEBOUNCETIME,
                        {leading: true, trailing: false}
                    )}
                >
                    <View className={cx(styles.actionButton, 'wz-flex wz-col-center')}>
                        <WImage className={cx(styles.buttonIcon, 'wz-mr-9')} src={avatar} />
                        <View>{title}</View>
                    </View>
                </CLoginButton>
            );
        },
        [handleClickBtn, isLogin]
    );

    return (
        <View className={cx(styles.orderGuideTip, 'wz-flex wz-row-left wz-fs-42 wz-plr-51')}>
            {titleInfo && (
                <View
                    className={cx(styles.titleInfo, 'wz-flex wz-row-center c-click-status')}
                    onClick={() => handleClickJump(titleInfo, 'title')}
                >
                    <WImage className={styles.leftIcon} src={titleInfo?.avatar} mode='aspectFill' />
                    <Text>{titleInfo?.title}</Text>
                </View>
            )}
            <View className={cx(styles.actionButtons, 'wz-flex wz-row-center')}>
                {btnTools?.map(item => {
                    return renderBtnDom(item);
                })}
            </View>
            <Portal>
                <HistoryRecordPopup open={open} setOpen={setOpen}></HistoryRecordPopup>
            </Portal>
        </View>
    );
};

export default memo(OrderGuideTip);
