import {useEffect, useMemo, useState} from 'react';
// 数据结构类型
export type ContentListItem = {
    type: string;
    content: ContentValue[];
};

export type ContentValue = {
    value: string;
};

export type ContentList = ContentListItem[];

export const useBubbleData = titleInfo => {
    return useMemo(() => {
        if (!titleInfo?.actionInfo) return [];

        const {interaction, interactionInfo} = titleInfo.actionInfo;

        if (interaction !== 'modal' || !interactionInfo?.modalContent?.contentList) {
            return [];
        }

        return interactionInfo.modalContent.contentList;
    }, [titleInfo]);
};

export const useBubbleVisibility = bubbleData => {
    const [isVisible, setIsVisible] = useState(false);
    useEffect(() => {
        if (!bubbleData.length && isVisible) {
            setIsVisible(false);
        }
    }, [bubbleData, isVisible]);

    const showBubble = () => setIsVisible(true);
    const hideBubble = () => setIsVisible(false);

    return {isVisible, showBubble, hideBubble};
};
