import {memo, FC} from 'react';
import cx from 'classnames';
import {View} from '@tarojs/components';

import {hooks} from '../../../index';

// import OrderGuideTip from './components/orderGuideTips';
import OrderGuideTip from './components/orderGuideTipsV2';
import {INavTopProps} from './index.d';
// import {btnTools} from './btnTools';
import {btnTools} from './btnToolsV2';

const TopNavBar: FC<INavTopProps> = ({hideBottomLine}: INavTopProps) => {
    const {useGetTitleInfo} = hooks || {};
    const {titleInfo} = useGetTitleInfo();

    const isRender = titleInfo;

    return (
        <>
            {isRender && <OrderGuideTip titleInfo={titleInfo} btnTools={btnTools} />}
            {!hideBottomLine && isRender && <View className={cx('c-line-superfine')} />}
        </>
    );
};

export default memo(TopNavBar);
