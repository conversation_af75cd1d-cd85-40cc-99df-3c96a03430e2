/**
 * @file: 图片+文字卡片组件
 * @author: gong<PERSON><PERSON><PERSON>
 */
import cx from 'classnames';
import {View} from '@tarojs/components';
import {useCallback, memo, FC} from 'react';
import {ImText} from '@baidu/wz-taro-tools-core';
import {ImImage} from '@baidu/vita-ui-cards-common';

import styles from '../../pages/im/components/CardComponents/index.module.less';
import imRichMsgStyles from './index.module.less';
import {ImRichMsgProps} from './index.d';

const ImRichMsg: FC<ImRichMsgProps> = props => {
    const {data, textExt, imageExt} = props || {};
    const {content, cardStyle, ext} = data || {};
    const list = content?.list || [];

    // 转换组件需要的格式数据
    const formatComponentData = useCallback(
        (arg, customCardStyle) => {
            return {
                cardStyle: {
                    ...cardStyle,
                    ...(customCardStyle || {})
                },
                content: arg,
                ext
            };
        },
        [cardStyle, ext]
    );

    // 图片组件
    const getImageCom = useCallback(
        imgList => {
            if (!imgList?.length) {
                return null;
            }

            return imgList?.map((item, index) => {
                const d = formatComponentData(
                    item,
                    imageExt?.isPrivate
                        ? {
                            width: 360,
                            height: 360
                        }
                        : {}
                );

                return <ImImage data={d} {...imageExt} key={index} />;
            });
        },
        [formatComponentData, imageExt]
    );

    return (
        <View
            className={cx(
                imRichMsgStyles.bubbleWrapper,
                !textExt?.isPrivate ? imRichMsgStyles.bubbleWrapperSystem : '',
                'wz-flex'
            )}
        >
            {list.map((item, index) => {
                if (item?.type === 'images') {
                    return getImageCom(item?.images);
                }

                if (item?.type === 'text') {
                    return (
                        <View
                            className={cx(
                                textExt?.isPrivate
                                    ? styles.bubbleWrapper
                                    : styles.bubbleWrapperServicer,
                                'wz-plr-45 wz-ptb-42 wz-flex-col wz-col-top wz-fs-51',
                                imRichMsgStyles.textWrapper,
                                index > 0 && 'wz-mt-51'
                            )}
                            key={index}
                        >
                            <ImText
                                key={index}
                                data={formatComponentData({value: item?.content}, {})}
                                {...textExt}
                            />
                        </View>
                    );
                }
            })}
        </View>
    );
};

export default memo(ImRichMsg);
