import cx from 'classnames';
import {View, Image} from '@tarojs/components';
import {useState, FC, useEffect, useCallback} from 'react';
import {Backdrop} from '@baidu/wz-taro-tools-core';
import {WiseCloseThin} from '@baidu/wz-taro-tools-icons';

import {ubcCommonClkSend, ubcCommonViewSend} from '../../utils/generalFunction/ubc';
import {GIF_PATH} from './index.d';

import styles from './index.module.less';

interface AIFollowUpDialogProps {
    onClose?: (isStay: boolean) => void;
}

const AIFollowUpDialog: FC<AIFollowUpDialogProps> = props => {
    const {onClose: closeDialog} = props;
    const [open, setOpen] = useState<boolean>(false);

    const closePop = useCallback(() => {
        closeDialog && closeDialog(true);
        ubcCommonClkSend({
            value: 'ReturnVisitPathModal_close'
        });
    }, [closeDialog]);

    const handleBackDetain = useCallback(() => {
        closeDialog && closeDialog(false);
        ubcCommonClkSend({
            value: 'ReturnVisitPathModal_btn'
        });
    }, [closeDialog]);

    useEffect(() => {
        setOpen(true);
        ubcCommonViewSend({
            value: 'ReturnVisitPathModal'
        });
    }, []);

    return (
        <Backdrop open={open} className={styles.aiFollowUpBackdrop}>
            <View className={cx(styles.aiFollowUpnWrap, 'wz-flex wz-col-center wz-row-center')}>
                <View className={styles.aiFollowUpContent}>
                    <Image src={GIF_PATH} className={styles.gifImage} mode='widthFix' />
                    <View className={styles.lottieButtonOverlay} onClick={handleBackDetain} />
                </View>
                <WiseCloseThin
                    className={styles.aiFollowUpClose}
                    onClick={closePop}
                    size={120}
                    color='#fff'
                />
            </View>
        </Backdrop>
    );
};

export default AIFollowUpDialog;
