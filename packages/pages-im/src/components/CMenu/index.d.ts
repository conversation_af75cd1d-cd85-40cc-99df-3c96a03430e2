import type { FC, ReactNode, CSSProperties } from 'react';


export interface BaseComponent {
    className?: string;
    customStyle?: string | CSSProperties;
    children?: ReactNode | ReactNode[];
}

export interface ICMenuProps extends BaseComponent {

    /**
     * 菜单数据
     */
    menu?: menuItem[];
}

export interface menuItem {
    icon?: string;
    text?: string;
    url?: string;
    type?: string;
    logValue?: string;
}

declare const CMenu: FC<ICMenuProps>;

export default CMenu;
