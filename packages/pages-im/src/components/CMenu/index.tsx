/**
 * @file h5右上角菜单
 * <AUTHOR>
 */
import {View, Text} from '@tarojs/components';
import {type FC, memo, useState, useCallback, useEffect} from 'react';
import {debounce} from 'lodash-es';
import {pxTransform, eventCenter} from '@tarojs/taro';
import {WiseUser, WiseRemark, WiseMenuMore, WiseEdit} from '@baidu/wz-taro-tools-icons';
import {WImage} from '@baidu/wz-taro-tools-core';
import {CLoginButton} from '@baidu/vita-ui-cards-common';

import {navigate} from '../../utils/basicAbility/commonNavigate';
import {useGetUserData} from '../../hooks/triageStream/pageDataController';
import {useHandleUserLoginBizAction} from '../../hooks/triageStream/useHandleUserBizAction';
import {ubcCommonClkSend} from '../../../src/utils/generalFunction/ubc';
import HistoryRecordPopup from '../HistoryRecordPopup';

import styles from './index.module.less';
import {ICMenuProps} from './index.d';

const DEBOUNCETIME = 1000;
const initLoginStatus = {
    isLogin: false,
    status: 'init'
};

const CMenu: FC<ICMenuProps> = props => {
    const {menu} = props;

    const [type, setType] = useState('');
    const [open, setOpen] = useState(false);
    const [menuShow, setMenuShow] = useState(false);

    const {userData} = useGetUserData() || {};
    const {isLogin} = userData || {};
    const {handleLoginBizAction} = useHandleUserLoginBizAction();

    const renderIcon = size => {
        const historyIcon = 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiHistoryIcon.png';

        return {
            user: <WiseUser size={size} />,
            question: <WiseRemark size={size} />,
            edit: <WiseEdit size={size} />,
            history: (
                <WImage
                    src={historyIcon}
                    style={{width: pxTransform(size), height: pxTransform(size)}}
                />
            )
        };
    };

    const handleLogin = useCallback(() => {
        if (initLoginStatus?.status === 'updateed' && !initLoginStatus?.isLogin) {
            handleLoginBizAction();
            initLoginStatus.isLogin = true;
        }
    }, [handleLoginBizAction]);

    /**
     * 处理操作按钮点击事件
     * @param {Object} actionInfo - 动作信息对象
     */
    const handleClickBtn = useCallback(
        (type: string, logValue?: string) => {
            type && setType(type);
            setOpen(true);
            // 若在语音播报 则关闭
            eventCenter.trigger('stopTTS');

            type === 'history' && handleLogin();
            logValue &&
                ubcCommonClkSend({
                    value: logValue
                });
        },
        [handleLogin]
    );

    const handerClick = item => {
        navigate({
            url: item.url,
            openType: 'navigate'
        });
    };

    useEffect(() => {
        if (initLoginStatus?.status === 'init') {
            initLoginStatus.isLogin = userData?.isLogin;
            initLoginStatus.status = 'updateed';
        }
    }, [userData?.isLogin]);

    return (
        <View>
            {menu?.length === 1 && (
                <CLoginButton
                    isLogin={isLogin}
                    closeShowNewUserTag={true}
                    useH5CodeLogin={true}
                    onLoginFail={error => {
                        console.error('error', error);
                    }}
                    onLoginSuccess={debounce(
                        () => {
                            handleClickBtn(menu[0]?.type || '', menu[0]?.logValue || '');
                        },
                        DEBOUNCETIME,
                        {leading: true, trailing: false}
                    )}
                >
                    {menu[0].icon && renderIcon(60)[menu[0].icon]}
                </CLoginButton>
            )}
            {menu && menu.length > 1 && (
                <>
                    <View className={styles.menuWrapper} onClick={() => setMenuShow(!menuShow)}>
                        <WiseMenuMore size={60} />
                    </View>
                    {menuShow && (
                        <View className={styles.menuActions}>
                            {menu &&
                                menu.map((item, index) => {
                                    return (
                                        <View
                                            key={index}
                                            className={styles.menuItem}
                                            onClick={() => handerClick(item)}
                                        >
                                            {item.icon && renderIcon(51)[item.icon]}
                                            <Text className={styles.menuItemText}>{item.text}</Text>
                                        </View>
                                    );
                                })}
                        </View>
                    )}
                </>
            )}
            {type === 'history' && <HistoryRecordPopup open={open} setOpen={setOpen} />}
        </View>
    );
};

export default memo(CMenu);
