import {View} from '@tarojs/components';
import {showLoading, hideLoading} from '@tarojs/taro';
import {memo, type FC, useCallback, useState, useReducer, useRef, useEffect} from 'react';
import {Portal} from '@baidu/vita-ui-cards-common';
import {
    useGetUserData,
    useGetSessionId,
    useUpdateUserData,
    useGetAdjectiveRecommendExpertMsgId,
    useGetAdjectiveRecommendUnDirectMsgId
} from '../../hooks/triageStream/pageDataController';
import {navigate} from '../../utils/basicAbility/commonNavigate';
import {updateTriageStreamMsgAtom} from '../../store/triageStreamAtom/msg';
import {useMsgDataGetController} from '../../hooks/triageStream/dataController';
import {getUserBizActionReq} from '../../models/services/triageStream';
import {showToast} from '../../utils/customShowToast';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../utils/generalFunction/ubc';
import {useConversationDataController} from '../../hooks/triageStream/useConversationDataController';
import type {userInfoItem} from '../../models/services/triageStream/index.d';
import {ServiceTypeMap} from '../../constants/common';
import {useScrollControl} from '../../hooks/triageStream/useScrollControl';
import PatientCard from './PatientCard';
import ConsultForm from './ConsultForm';
import {formReducer} from './ConsultForm/form-reducer';
import ExpertRecommendation from './ExpertRecommendation';
import type {PatientInfo} from './ConsultForm/index.d';
import type {ImCollectedAndReExpertProps, OpenPatientType, expertItem} from './index.d';

/**
 *
 * @description AI智能体定向服务卡
 * @returns
 */
const ImCollectedAndReExpert: FC<ImCollectedAndReExpertProps> = props => {
    const {content, ext = {}} = props?.data || {};
    const {scene = '', descLimit = {}} = ext || {};
    const {limitLength = 0, limitToast = ''} = descLimit || {};
    const {collectedInfo, expertData, requireDesc, isExpired} = content;
    const {qid} = collectedInfo || '';
    const openPatientPopTypeRef = useRef<boolean>(false); // 存储点开来源类型，用于判断是否发生了变化
    // eslint-disable-next-line max-len
    const [showConsultForm, setShowConsultForm] = useState(false); // 是否展示编辑态表单弹层
    const [showConsultList, setShowConsultList] = useState(false); // 是否展示选择就诊人列表弹层
    const [openPatientPopType, setOpenPatientPopType] = useState<OpenPatientType>('patient'); // 点开打开弹窗的来源类型
    const [selectedExpertItemData, setSelectedExpertItemData] = useState<expertItem>(
        (expertData?.list || [])[0]
    );

    const [isExpertDisabled, setIsExpertDisabled] = useState(false);
    const [updateCollectedInfoAndExpertType, setUpdateCollectedInfoAndExpert] =
        useState<OpenPatientType>('patient');
    const [selectPatientData, setSelectPatientData] = useState<PatientInfo | undefined>(
        collectedInfo?.curPatient
    );

    const {userData} = useGetUserData();

    const prevIsLoginRef = useRef<boolean>(userData?.isLogin || false); // 存储之前的值，用于判断是否发生了变化

    const sessionId = useGetSessionId();
    const {updateUserData} = useUpdateUserData();
    const {adjectiveRecommendExpertMsgId} = useGetAdjectiveRecommendExpertMsgId();
    const {adjectiveRecommendUnDirectMsgId} = useGetAdjectiveRecommendUnDirectMsgId();

    const {updateSessionCapsulesTools} = useConversationDataController();
    const {data: docServiceData} = useMsgDataGetController({msgId: adjectiveRecommendExpertMsgId});
    const {data: undirectServiceData} = useMsgDataGetController({
        msgId: adjectiveRecommendUnDirectMsgId
    });

    const {scrollToMessage} = useScrollControl();

    useEffect(() => {
        // isExpired接口端判断无效卡,直接禁用当前卡
        if (isExpired || adjectiveRecommendExpertMsgId !== props?.msgId) {
            setIsExpertDisabled(true);
        }
    }, [adjectiveRecommendExpertMsgId, isExpired, props?.msgId]);

    const [state, dispatch] = useReducer(formReducer, {
        ...collectedInfo?.curPatient,
        canSubmit: true,
        zhusu: collectedInfo?.clinicalDesc || '',
        images: collectedInfo?.images || []
    });
    // 就诊人不存在时，直接展示表单编辑态
    const openEditPatientPop = useCallback(() => {
        setShowConsultList(false);
        setShowConsultForm(true);
        dispatch({
            payload: {
                ...collectedInfo,
                ...collectedInfo?.curPatient,
                zhusu: collectedInfo?.clinicalDesc,
                images: collectedInfo?.images || []
            }
        });
    }, [collectedInfo]);

    // 就诊人存在时，打开就诊人选择弹层
    const openEditPatient = useCallback(() => {
        if (collectedInfo?.curPatient?.contactId) {
            setShowConsultList(true);
            setSelectPatientData(
                collectedInfo?.curPatient?.contactId ? collectedInfo?.curPatient : undefined
            );
            dispatch({
                payload: {
                    ...collectedInfo?.curPatient,
                    zhusu: collectedInfo?.clinicalDesc || '',
                    images: collectedInfo?.images || []
                }
            });
        }
    }, [collectedInfo]);

    const handleAddPatient = useCallback(() => {
        setShowConsultForm(true);
    }, []);

    // 处理弹层关闭逻辑
    const handleCloseConsultForm = useCallback(
        (type: string) => {
            if (type === 'consult') {
                setShowConsultForm(false);
                dispatch({
                    payload: {
                        ...collectedInfo?.curPatient,
                        zhusu: state?.zhusu || '',
                        images: state?.images || []
                    }
                });
            }
            if (type === 'consultList') {
                setSelectPatientData(undefined);
                setShowConsultList(false);
            }
            ubcCommonClkSend({
                value: `ImAIRecommendExpert_${scene}_${type}_close`
            });
        },
        [collectedInfo?.curPatient, state?.images, state?.zhusu, scene]
    );

    // 选择就诊人
    const handleSelectPatient = useCallback(
        (selectPatient: PatientInfo) => {
            setSelectPatientData(selectPatient);
            dispatch({
                payload: {
                    ...selectPatientData,
                    zhusu: state?.zhusu || '',
                    images: state?.images || []
                }
            });
        },
        [selectPatientData, state?.images, state?.zhusu]
    );

    // 根据当前选中的skuId查找最新的sku数据
    const findSelectedExpert = useCallback(
        (docID: string) => {
            return expertData.list?.find(expert => expert.docID === docID);
        },
        [expertData]
    );

    const addPatientToast = useCallback(() => {
        showToast({
            title: '请完善您的提问信息',
            icon: 'none'
        });
    }, []);

    const jumpToTriage = useCallback(btnInfo => {
        const {interaction, interactionInfo} = btnInfo;
        if (interaction && interaction === 'openLink' && interactionInfo?.url) {
            navigate({
                openType: 'navigate',
                url: interactionInfo?.url
            });
        }
    }, []);

    // 无就诊人或无主诉时，自动弹起弹层并进行toast提示
    // useEffect(() => {
    //     // 无就诊人id时，无性别、年龄、主诉时，弹起就诊人编辑弹层，并提示完善信息,有就诊人id时，无主诉时，弹起就诊人编辑弹层，并提示完善信息
    //     const {contactId, gender, age} = collectedInfo?.curPatient || {};
    //     const {clinicalDesc} = collectedInfo;
    //     if (!clinicalDesc && contactId) {
    //         setShowConsultList(true);
    //         addPatientToast();
    //     } else if (!contactId && (!gender || !age || !clinicalDesc)) {
    //         setShowConsultForm(true);
    //         addPatientToast();
    //     }
    //     // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, []);

    // 监听登录发生变化后的数据处理
    useEffect(() => {
        if (openPatientPopTypeRef.current) {
            if (!collectedInfo?.curPatient?.contactId) {
                openPatientPopType === 'expert' && addPatientToast();
                openEditPatientPop();

                return;
            }
            // 未登录变为登录在状态后，存在就诊人时，点击的为就诊人模块时弹窗就诊人选择列表，否则直接走下单逻辑
            if (updateCollectedInfoAndExpertType === 'patient' || !collectedInfo?.clinicalDesc) {
                openEditPatient();
            } else {
                const selectExpert = findSelectedExpert(selectedExpertItemData?.docID || '');
                // 直接走跳转逻辑
                selectExpert && jumpToTriage(selectExpert?.btnInfo);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [openPatientPopTypeRef.current]);

    // 更新卡片数据：未登录转登录后，更新两类服务卡
    const handleMessageUpdate = useCallback(
        messageList => {
            messageList.forEach(messageItem => {
                updateTriageStreamMsgAtom(`${sessionId}_${messageItem?.meta?.msgId}`, messageItem, {
                    _debugSymbol: 'loginUpdate'
                });
            });
        },
        [sessionId]
    );

    // 用户行为接口数据参数
    const handleParams = useCallback(() => {
        const userLoginInfoListData: userInfoItem[] = [];
        // 合并两个消息的数据
        [undirectServiceData, docServiceData].forEach(item => {
            if (item && item?.data && item?.data?.content) {
                const {cardId} = item?.data?.content || '';
                const {msgId} = item?.meta || '';
                const {collectedInfo} = item?.data?.content?.data?.content || {};
                const compParam = {
                    msgId,
                    patientInfo: collectedInfo?.curPatient,
                    serviceType: ServiceTypeMap[cardId],
                    zhusu: collectedInfo?.clinicalDesc || '',
                    qid: collectedInfo?.qid
                };
                userLoginInfoListData.push(compParam);
            }
        });
        const params = {
            bizActionType: 'userLogin' as const,
            chatData: {
                sessionId
            },
            bizActionData: {
                userLoginInfoList: {
                    msgList: userLoginInfoListData
                }
            }
        };
        return params;
    }, [docServiceData, sessionId, undirectServiceData]);

    // 更新定向服务卡数据，重新渲染卡片组件
    const updateCollectedInfoAndExpert = useCallback(
        // eslint-disable-next-line complexity
        async (type: OpenPatientType, selectExpert?: expertItem) => {
            if (isExpertDisabled) {
                showToast({
                    title: isExpired
                        ? '服务卡已失效，你可向健康管家重新咨询'
                        : '当前推荐已失效，请点击新的服务卡',
                    icon: 'none'
                });
                sessionId &&
                    adjectiveRecommendExpertMsgId &&
                    scrollToMessage(adjectiveRecommendExpertMsgId, 'updateCollectedInfoAndExpert');
                ubcCommonClkSend({
                    value: `ImAIRecommendExpert_${scene}_disable`,
                    ext: {
                        // eslint-disable-next-line camelcase
                        product_info: {
                            ...ext
                        }
                    }
                });

                return;
            }

            // 点击打点
            if (type === 'expert') {
                ubcCommonClkSend({
                    value: 'ImAIDir',
                    ext: {
                        value_type: 'doc',
                        value_id: selectExpert?.docID || '',
                        product_info: {
                            ...ext,
                            msgId: props?.msgId || '',
                            pos: selectExpert?.pos || 0
                        }
                    }
                });
            } else {
                ubcCommonClkSend({
                    value: 'ImAIRecommendUnDirect_edit',
                    ext: {
                        product_info: {
                            ...ext,
                            msgId: props?.msgId || ''
                        }
                    }
                });
            }
            selectExpert && setSelectedExpertItemData(selectExpert);
            setOpenPatientPopType(type);

            // 未登录
            if (sessionId && !prevIsLoginRef.current && !userData?.isLogin) {
                const params = handleParams();
                showLoading({
                    title: '登录中...',
                    mask: true
                });
                const [err, data] = await getUserBizActionReq<'userLogin'>(params);
                if (!err) {
                    data?.data?.message && handleMessageUpdate(data?.data?.message || []);
                    data?.data?.userData && updateUserData(data?.data?.userData);
                    // 更新胶囊工具数据
                    data?.data?.toolData?.capsules &&
                        updateSessionCapsulesTools(data?.data?.toolData?.capsules);
                    prevIsLoginRef.current = data?.data?.userData?.isLogin || false;
                    setUpdateCollectedInfoAndExpert(type);
                    openPatientPopTypeRef.current = true;
                    hideLoading();
                }
            } else {
                // 已登录--无就诊人
                if (!collectedInfo?.curPatient?.contactId) {
                    // 点击【去咨询】时，需要先toast后再出弹层
                    type === 'expert' && addPatientToast();
                    openEditPatientPop();

                    return;
                }
                // 已登录--有就诊人--就诊人选择模块
                if (type === 'patient' || !collectedInfo?.clinicalDesc) {
                    openEditPatient();
                } else {
                    // 主诉不足最少字数，需要打开就诊人信息浮层
                    if (state?.zhusu && state?.zhusu?.length < limitLength) {
                        showToast({
                            title: limitToast,
                            icon: 'none'
                        });
                        openEditPatient();
                        return;
                    }

                    // 已登录--有就诊人--服务卡去咨询
                    // 直接走跳转诊前逻辑
                    selectExpert && jumpToTriage(selectExpert?.btnInfo);
                }
            }
        },
        [
            isExpertDisabled,
            sessionId,
            userData?.isLogin,
            isExpired,
            adjectiveRecommendExpertMsgId,
            scrollToMessage,
            scene,
            ext,
            props?.msgId,
            handleParams,
            handleMessageUpdate,
            updateUserData,
            updateSessionCapsulesTools,
            collectedInfo?.curPatient?.contactId,
            collectedInfo?.clinicalDesc,
            addPatientToast,
            openEditPatientPop,
            openEditPatient,
            state?.zhusu,
            limitLength,
            jumpToTriage,
            limitToast
        ]
    );

    useEffect(() => {
        ubcCommonViewSend({
            value: 'ImAIRecommendExpert',
            ext: {
                product_info: {
                    msgId: props?.msgId || '',
                    ...ext
                }
            }
        });
    }, [ext, props?.msgId]);

    return (
        <View style={{width: '100%'}}>
            {/* AI重构-就诊人 */}
            <PatientCard
                isLogin={userData?.isLogin || false}
                collectedInfo={collectedInfo}
                updateCollectedInfoAndExpert={updateCollectedInfoAndExpert}
                isExpertDisabled={isExpertDisabled}
            />
            <ExpertRecommendation
                expertData={expertData}
                isExpertDisabled={isExpertDisabled}
                isLogin={userData?.isLogin || false}
                updateCollectedInfoAndExpert={updateCollectedInfoAndExpert}
                adjectiveRecommendExpertMsgId={adjectiveRecommendExpertMsgId}
                msgId={props?.msgId || ''}
                ext={ext}
            />
            <Portal>
                {collectedInfo && (showConsultForm || showConsultList) && (
                    <ConsultForm
                        showConsultForm={showConsultForm}
                        showConsultList={showConsultList}
                        collectedInfo={collectedInfo}
                        requireDesc={requireDesc}
                        handleAddPatient={handleAddPatient}
                        handleCloseConsultForm={handleCloseConsultForm}
                        openEditPatient={openEditPatient}
                        state={state}
                        dispatch={dispatch}
                        openPatientPopType={openPatientPopType}
                        handleSelectPatient={handleSelectPatient}
                        selectPatientData={selectPatientData}
                        selectedExpertItemData={selectedExpertItemData}
                        jumpToTriage={jumpToTriage}
                        expertList={expertData?.list}
                        msgId={props?.msgId || ''}
                        sessionId={sessionId || ''}
                        setSelectPatientData={setSelectPatientData}
                        qid={qid}
                        ext={ext}
                    />
                )}
            </Portal>
        </View>
    );
};

export default memo(ImCollectedAndReExpert);
