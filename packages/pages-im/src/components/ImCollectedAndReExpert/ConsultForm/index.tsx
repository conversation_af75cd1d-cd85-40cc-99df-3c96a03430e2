/*
 *  @Author: lijing106
 * @Description: 就诊人操作弹层
 */
import cx from 'classnames';
import {pxTransform} from '@tarojs/taro';
import {View, Text, Form} from '@tarojs/components';
import {WiseRightArrow} from '@baidu/wz-taro-tools-icons';
import {memo, FC, useMemo, useCallback, useState, useEffect, useRef} from 'react';
import {
    Popup,
    Button,
    FormCard,
    SelectAgePopup,
    SafeArea,
    Uploader
} from '@baidu/wz-taro-tools-core';
import type {ItemInfo} from '@baidu/wz-taro-tools-core/form-input-item';

import {showToast} from '../../../utils/customShowToast';
import {useMsgDataSetController} from '../../../hooks/triageStream/dataController';
import {getBdFileMapAtom} from '../../../store/triageStreamAtom/bdFileMapAtom';
import {getUserBizActionReq} from '../../../models/services/triageStream';
import {ubcCommonClkSend} from '../../../utils/generalFunction/ubc';
import {
    TIRAGE_MAX_PIC_NUM,
    BUCKET_NAME,
    ONLINE_HOST,
    SINGLE_NUM,
    H5_UPLOAD_PIC_NUM_TIPS
} from '../../../constants/common';
import {preAndUploadPic} from '../../../utils/basicAbility/upload';
import {API_HOST} from '../../../models/apis/host';
import {formatAgeText} from '../common/utils';
import type {CONSULT_TYPE} from '../index.d';

import {PATIENT_TEMP, TOAST_TIME, ZHUSU_MAX_TEXT, IMG, patientInitInfo} from './const';
import ChoosePatientList from './components/ChoosePatientList';
import DiseaseDesc from './components/DiseaseDesc';
import RequireDesc from './components/RequireDesc';
import type {IConsultFormProps, PatientInfo} from './index.d';
import styles from './index.module.less';

interface FileProps extends Uploader.File {
    fileName?: string;
}

const bucketConfName =
    ONLINE_HOST.indexOf(API_HOST) > -1 ? BUCKET_NAME[2] : `${BUCKET_NAME[2]}-test`;

const ConsultForm: FC<IConsultFormProps> = (props: IConsultFormProps) => {
    const {
        showConsultForm,
        collectedInfo,
        requireDesc,
        handleAddPatient,
        handleCloseConsultForm,
        showConsultList,
        state,
        dispatch,
        openPatientPopType,
        selectPatientData,
        handleSelectPatient,
        selectedExpertItemData,
        jumpToTriage,
        msgId,
        sessionId,
        expertId,
        expertList,
        setSelectPatientData,
        qid,
        ext
    } = props;
    const {descLimit = {}} = ext || {};
    const {limitLength = 0, limitToast = ''} = descLimit || {};

    const initImg: FileProps[] =
        state?.images?.map(item => {
            return {
                fileName: item.fileName || '',
                url: item.small || ''
            };
        }) || [];
    const [openAgeSelect, setOpenAgeSelect] = useState(false);
    const [isAddPatient, setIsAddPatient] = useState(false); // 是否是点击添加就诊人
    const {updateMsgData} = useMsgDataSetController({msgId});
    const [editPopType, setEditPopType] = useState<CONSULT_TYPE>('consult');
    const [isDisabled, setIsDisabled] = useState(false);
    const [files, setFiles] = useState<FileProps[]>(initImg || []);

    const patirentRef = useRef(false);

    const memoPatientListStyle = useMemo(() => {
        return {maxHeight: `calc(80vh - ${pxTransform(133)})`};
    }, []);

    /**
     * ImConsultForm 选择年龄 上报
     */
    const chooseAge = useCallback(editStatus => {
        editStatus === 1 && setOpenAgeSelect(true);
    }, []);

    // item 选项切换回调
    const onInputItemChange = useCallback(
        (key, itemData: PatientInfo) => {
            if (key === 'gender') {
                dispatch({payload: {gender: itemData?.gender || ''}});
            } else {
                dispatch({payload: itemData});
            }
        },
        [dispatch]
    );

    // 新增患者
    const addPatient = useCallback(() => {
        setIsAddPatient(true);
        handleAddPatient && handleAddPatient();
        dispatch({
            payload: {
                ...patientInitInfo,
                zhusu: state?.zhusu || ''
            }
        });
        ubcCommonClkSend({
            value: 'ImAIRecommendExpert_patient_add',
            ext: {
                product_info: {
                    msgId,
                    ...ext
                }
            }
        });
    }, [handleAddPatient, dispatch, state?.zhusu, msgId, ext]);

    // 根据当前选中的skuId查找最新的sku数据
    const findSelectedExpert = useCallback(
        (docID: string) => {
            return expertList?.find(expertItem => expertItem?.docID === docID);
        },
        [expertList]
    );

    const showToastMsg = useCallback(() => {
        showToast({
            title: isAddPatient ? '新增成功' : '保存成功',
            icon: 'none'
        });
    }, [isAddPatient]);

    useEffect(() => {
        if (patirentRef.current) {
            const selectExpert = findSelectedExpert(selectedExpertItemData?.docID || '');
            if (selectExpert) {
                selectExpert && jumpToTriage(selectExpert?.btnInfo || {});
                showToastMsg();
            }
            handleCloseConsultForm(editPopType);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [patirentRef.current]);

    const handleUploadImage = useCallback(images => {
        const imageList: string[] = [];
        images.map(item => {
            imageList.push(item?.fileName);
        });
        return imageList;
    }, []);

    const handleUpdateMsgData = useCallback(
        async (params, type) => {
            const {age, gender, zhusu, contactId, docId, images} = params;
            const patientInfo =
                contactId && type === 'consultList'
                    ? {contactId}
                    : {
                        age,
                        gender
                    };
            setEditPopType(type);
            const paramsOpt = !isAddPatient ? {zhusu, images: handleUploadImage(images)} : {};
            const paramsData = {
                bizActionType: 'editZhusu' as const,
                chatData: {
                    sessionId,
                    expertId: Number(expertId || '')
                },
                bizActionData: {
                    editZhusuInfo: {
                        patientInfo,
                        msgId,
                        ...paramsOpt,
                        docId,
                        serviceType: 'docService',
                        qid
                    }
                }
            };
            setIsDisabled(true);
            const [err, data] = await getUserBizActionReq<'editZhusu'>(paramsData);
            try {
                if (!err) {
                    data?.data?.message && updateMsgData(data?.data?.message[0] || {});
                    // 医生卡直接走跳转
                    if (openPatientPopType === 'expert') {
                        patirentRef.current = true;
                    } else {
                        // 就诊人模块关闭弹层，自动更新卡片信息
                        handleCloseConsultForm(type);
                        // eslint-disable-next-line max-len
                        setSelectPatientData(
                            data?.data?.message[0]?.data?.content?.data?.content?.collectedInfo
                                ?.curPatient || {}
                        );
                        showToastMsg();
                    }
                    setIsDisabled(false);
                    setIsAddPatient(false);
                }
            } catch (error) {
                setIsDisabled(false);
                console.error(error);
            }
        },
        [
            qid,
            expertId,
            handleCloseConsultForm,
            handleUploadImage,
            isAddPatient,
            msgId,
            openPatientPopType,
            sessionId,
            setSelectPatientData,
            showToastMsg,
            updateMsgData
        ]
    );

    /**
     * 提交数据
     */
    const onSubmit = useCallback(
        async type => {
            const {zhusu: content = '', gender = ''} = state;
            if (!isAddPatient && (state?.zhusu?.length || 0) > ZHUSU_MAX_TEXT) {
                showToast({
                    title: `病情描述不能超过${ZHUSU_MAX_TEXT}个字符`,
                    icon: 'none',
                    duration: TOAST_TIME
                });

                return;
            }

            if (!state?.zhusu?.trim() && !isAddPatient) {
                showToast({
                    title: '病情描述不能为空',
                    icon: 'none',
                    duration: TOAST_TIME
                });

                return;
            }

            if (state?.zhusu && state?.zhusu?.length < limitLength) {
                showToast({
                    title: limitToast,
                    icon: 'none'
                });

                return;
            }

            // 在就诊人模块选择就诊人信息时，会根据当前选择的就诊人信息，请求行为接口，刷新页面数据，更新就诊人模块信息
            if (type === 'consultList') {
                // eslint-disable-next-line max-len
                handleUpdateMsgData(
                    {contactId: selectPatientData?.contactId, zhusu: content || '', images: files},
                    type
                );
            } else {
                // 无就诊人时，就诊人模块新增就诊人
                handleUpdateMsgData(
                    {
                        gender,
                        age: state?.age,
                        zhusu: content,
                        docId: selectedExpertItemData?.docID || '',
                        images: files
                    },
                    type
                );
            }
            ubcCommonClkSend({
                value: `ImAIRecommendExpert_${type}_submit`,
                ext: {
                    product_info: {
                        msgId,
                        ...ext
                    }
                }
            });
        },
        [
            state,
            isAddPatient,
            limitLength,
            msgId,
            ext,
            limitToast,
            handleUpdateMsgData,
            selectPatientData?.contactId,
            files,
            selectedExpertItemData?.docID
        ]
    );

    // 选择性别
    const getGenderChoose = useCallback(
        formItem => {
            return (
                <View className='wz-flex wz-row-right'>
                    {['男', '女'].map((genderItem, genderIndex) => {
                        return (
                            <View
                                key={genderIndex}
                                className={cx(
                                    styles.formGenderBtn,
                                    'wz-flex',
                                    'wz-col-center',
                                    'wz-row-center',
                                    {
                                        [styles.formGenderBtnActive]:
                                            formItem?.value === genderItem ||
                                            formItem?.defaultValue === genderItem
                                    },
                                    'wz-fs-45 wz-ml-24 wz-plr-48'
                                )}
                                onClick={() =>
                                    onInputItemChange &&
                                    onInputItemChange(formItem.key, {gender: genderItem})
                                }
                            >
                                {genderItem}
                            </View>
                        );
                    })}
                </View>
            );
        },
        [onInputItemChange]
    );
    // 选择年龄
    const getAgeChoose = useCallback(
        (formItem, editStatus) => {
            return (
                <View
                    className={cx('wz-flex wz-row-right wz-col-center colorGray wz-fs-45', [
                        editStatus === 1 ? '' : ''
                    ])}
                    onClick={() => chooseAge(editStatus)}
                >
                    {state.age ? (
                        <Text> {formatAgeText(state?.age)}</Text>
                    ) : (
                        <Text className={cx(styles.placeholderColor)}> {formItem.placeholder}</Text>
                    )}
                    {editStatus === 1 && <WiseRightArrow color='#B5B5B5' />}
                </View>
            );
        },
        [chooseAge, state.age]
    );

    const memoPatientEdit = useMemo(() => {
        return <View className='form-title'>{isAddPatient ? '新增患者' : '病情信息'}</View>;
    }, [isAddPatient]);

    const onUpload = useCallback(async () => {
        try {
            const remainNum = TIRAGE_MAX_PIC_NUM - files?.length;
            // eslint-disable-next-line max-len
            const imgData = await preAndUploadPic({
                count: remainNum > SINGLE_NUM ? SINGLE_NUM : remainNum,
                bucketConfName,
                remainNum,
                H5Tips: H5_UPLOAD_PIC_NUM_TIPS
            });
            if (imgData && imgData.length) {
                setFiles([
                    ...files,
                    ...imgData.map(({fileName, path, type, originalFileObj}) => ({
                        fileName,
                        type,
                        url: path,
                        name: originalFileObj?.name
                    }))
                ]);
            }
        } catch (err) {
            console.error('onUpload 出错：', err);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [files]);

    useEffect(() => {
        // 校验表单是否可以提交（非新增时校验性别和年龄、主诉，新增时校验性别和年龄）
        if (
            (!isAddPatient && (!state?.zhusu?.trim() || !state?.gender || !state?.age)) ||
            (isAddPatient && (!state?.gender || !state?.age))
        ) {
            setIsDisabled(true);
        } else {
            setIsDisabled(false);
        }
    }, [isAddPatient, state?.age, state?.gender, state?.zhusu]);

    // 关闭新增或者编辑弹层
    const handleCloseConsultPop = useCallback(() => {
        setIsAddPatient(false);
        handleCloseConsultForm('consult');
    }, [handleCloseConsultForm]);

    return (
        <View className={styles.consultForm}>
            <Popup
                open={showConsultForm}
                rounded
                placement='bottom'
                catchMove={false}
                title={memoPatientEdit}
                style={{
                    height: `calc(80vh - ${pxTransform(133)})`,
                    background: 'linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 18%, #F5F5F5 100%)',
                    zIndex: 1012,
                    borderRadius: `${pxTransform(63)} ${pxTransform(63)} 0 0`
                }}
                titleStyle={{
                    backgroundColor: 'rgb(245, 245, 245, 0)',
                    border: 'none'
                }}
                onClose={handleCloseConsultPop}
                className={cx(!showConsultList && styles['consult-popup'])}
            >
                <View className={styles.consultPopupContent}>
                    <View className={cx(styles.patientList, 'wz-mlr-30 wz-pb-30 wz-mb-24')}>
                        {!collectedInfo?.curPatient?.contactId && !isAddPatient && (
                            <View
                                className={cx(
                                    styles.patientInfoTitle,
                                    'wz-flex wz-col-center wz-fs-54 wz-fw-500 wz-mlr-39'
                                )}
                            >
                                患者信息
                            </View>
                        )}
                        <Form>
                            <FormCard className={styles.formCard}>
                                {(PATIENT_TEMP as ItemInfo[]).map((item: ItemInfo) => {
                                    const editStatus = 1;

                                    const formItem = Object.assign(
                                        {},
                                        item,
                                        item?.key
                                            ? {
                                                value: state[
                                                      item?.key as
                                                          | 'age'
                                                          | 'ageText'
                                                          | 'gender'
                                                          | 'name'
                                                ],
                                                defaultValue:
                                                      state[
                                                          item?.key as
                                                              | 'age'
                                                              | 'ageText'
                                                              | 'gender'
                                                              | 'name'
                                                      ],
                                                editStatus
                                            }
                                            : {}
                                    );

                                    return (
                                        <View
                                            key={item.key}
                                            className={cx(
                                                'wz-flex wz-row-between wz-col-center',
                                                'c-color-prime wz-fs-48 wz-mlr-39',
                                                'wz-ptb-54'
                                            )}
                                        >
                                            <View>
                                                <Text className='wz-fw-500'>{item.label}</Text>
                                                <Text
                                                    className={cx(styles.form__item__label__icon)}
                                                >
                                                    *
                                                </Text>
                                            </View>
                                            {item?.key === 'age'
                                                ? getAgeChoose(formItem, editStatus)
                                                : getGenderChoose(formItem)}
                                        </View>
                                    );
                                })}
                            </FormCard>
                        </Form>
                        {!collectedInfo?.curPatient?.contactId && !isAddPatient && (
                            <>
                                {/* 病情描述 */}
                                <DiseaseDesc {...{state, dispatch}} />
                                <View className={cx('wz-pt-54 wz-ml-39')}>
                                    <View className='wz-fs-48 wz-mb-45 wz-fw-500'>上传图片</View>
                                    <Uploader
                                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                        // @ts-expect-error
                                        value={files}
                                        multiple
                                        maxFiles={TIRAGE_MAX_PIC_NUM}
                                        tips={IMG.tips}
                                        onUpload={onUpload}
                                        onChange={setFiles}
                                        bdFileMap={
                                            process.env.TARO_ENV === 'swan'
                                                ? getBdFileMapAtom()
                                                : undefined
                                        }
                                    />
                                </View>
                            </>
                        )}
                    </View>
                    {/* 接诊要求 无就诊人时会展示 */}
                    {requireDesc && !isAddPatient && <RequireDesc requireDesc={requireDesc} />}
                </View>
                <Popup.Button>
                    <View className={cx(styles['patient-list-btn'], ' wz-ptb-24 wz-plr-54')}>
                        <Button
                            color='default'
                            size='large'
                            block
                            className={cx(styles['patient-list-btn-submit'], 'wz-fw-500')}
                            onClick={() => onSubmit('consult')}
                            disabled={isDisabled}
                        >
                            <Text className='wz-fs-54'>
                                {openPatientPopType === 'expert' ? '立即咨询' : '完成'}
                            </Text>
                        </Button>
                        <SafeArea
                            id='safearea'
                            style={{backgroundColor: '#fff'}}
                            position='bottom'
                        />
                    </View>
                </Popup.Button>
                <Popup.Close />
                <Popup.Backdrop open={showConsultForm} style={{zIndex: 1011}} />
            </Popup>
            <Popup
                open={showConsultList}
                placement='bottom'
                title={<View catchMove>病情信息</View>}
                rounded
                catchMove={false}
                style={{
                    ...memoPatientListStyle,
                    background: 'linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 18%, #F5F5F5 100%)',
                    zIndex: 1011,
                    borderRadius: `${pxTransform(63)} ${pxTransform(63)} 0 0`,
                    height: `calc(80vh - ${pxTransform(133)})`
                }}
                titleStyle={{background: 'rgb(245, 245, 245, 0)', border: 'none'}}
                onClose={() => handleCloseConsultForm('consultList')}
                className={styles['consult-popup']}
            >
                <Popup.Close />
                <View className={styles.consultPopupContent}>
                    <View className={cx(styles.patientList, 'wz-mlr-30')}>
                        <ChoosePatientList
                            {...{
                                collectedInfo,
                                onInputItemChange,
                                handleAddPatient: addPatient,
                                selectPatientData,
                                handleSelectPatient,
                                state,
                                dispatch,
                                openPatientPopType,
                                files,
                                TIRAGE_MAX_PIC_NUM,
                                IMG,
                                onUpload,
                                setFiles
                            }}
                        />
                    </View>
                    {/* 接诊要求 */}
                    {requireDesc && <RequireDesc requireDesc={requireDesc} />}
                </View>
                <Popup.Button>
                    <View className={cx(styles['patient-list-btn'], ' wz-ptb-24 wz-plr-54')}>
                        <Button
                            color='default'
                            size='large'
                            block
                            className={cx(styles['patient-list-btn-submit'], 'wz-fw-500')}
                            onClick={() => onSubmit('consultList')}
                            disabled={isDisabled}
                        >
                            <Text className='wz-fs-54'>完成</Text>
                        </Button>
                        <SafeArea
                            id='safearea'
                            style={{backgroundColor: '#fff'}}
                            position='bottom'
                        />
                    </View>
                </Popup.Button>
                <Popup.Backdrop open={showConsultList} style={{zIndex: 1010}} />
            </Popup>
            <SelectAgePopup
                open={openAgeSelect}
                interval='0,120'
                key={state.age}
                defaultValue={(state.age as string) || '20'}
                onCancel={() => setOpenAgeSelect(false)}
                onConfirm={v => {
                    setOpenAgeSelect(false);
                    dispatch({
                        payload: {
                            age: v?.value
                        }
                    });
                }}
            />
        </View>
    );
};
export default memo(ConsultForm);
