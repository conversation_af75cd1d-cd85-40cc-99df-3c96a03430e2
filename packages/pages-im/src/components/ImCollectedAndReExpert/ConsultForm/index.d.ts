import {Uploader} from '@baidu/wz-taro-tools-core';

import {MsgId, Qid} from '../../../typings';
import type {SessionId} from '../../../store/triageStreamAtom/index.type.ts';
import type {CollectedInfoProps, ImageInfo} from '../PatientCard/index.d';
import type {OpenPatientType, expertItem, IBtn, DescLimit} from '../index.d';

export interface RequireDescProps {
    titleCon: {
        type: 'text' | 'icon';
        value: string;
    }[];
    descList: {
        title: string;
        desc: string[];
    }[];
}

interface FileProps extends Uploader.File {
    fileName?: string;
}

export interface IConsultFormProps {
    showConsultForm: boolean;
    collectedInfo: CollectedInfoProps;
    requireDesc: RequireDescProps;
    handleAddPatient?: () => void;
    showConsultList: boolean;
    handleCloseConsultForm: (type: string) => void;
    formType?: number;
    openEditPatient?: () => void;
    state: IState;
    dispatch: (args: IAction) => void;
    openPatientPopType: OpenPatientType;
    handleSelectPatient?: (selectPatient: PatientInfo) => void;
    selectPatientData?: PatientInfo;
    selectedExpertItemData?: expertItem;
    jumpToTriage: (btnInfo: IBtn) => void;
    expertList?: expertItem[];
    msgId: MsgId;
    couponId?: number;
    sessionId: SessionId;
    expertId?: string;
    setSelectPatientData?: (selectPatient: PatientInfo) => void;
    qid?: Qid;
    ext?: {
        scene?: string;
        descLimit?: DescLimit;
    };
}

// eslint-disable-next-line no-shadow
export enum ACTION_TYPE {
    VALIDATE = 'validate',
    CHANGE_PATIENT = 'changePatient',
    // 选择年龄
    SELECT_AGE = 'selectAge',
    // 修改主诉
    CHANGE_ZHUSU = 'changeZhusu'
}

/**
 * 表单信息
 */
export interface PatientInfo {
    chooseType?: string;
    age?: string;
    ageText?: string;
    contactId?: string;
    zhusu?: string;
    department?: string;
    department_lv2?: string;
    department_lv1?: string;
    dept_id_lv1?: string;
    dept_id_lv2?: string;
    genderText?: string;
    label?: string;
    name?: string;
    realName?: number | string;
    gender?: string;
    skuId?: number;
    status?: 'loading' | 'success' | 'error';
    isShowLoading?: boolean;
}

export interface IAction {
    type?: ACTION_TYPE;
    payload: CollectedInfoProps;
}

interface Statement {
    title: string;
    content: string[];
}

// eslint-disable-next-line no-shadow
export enum SKU_TYPE {
    init = 1,
    tel = 2
}

export type SKU_TYPE_VAL = `${Extract<SKU_TYPE, number>}` extends `${infer N extends number}`
    ? N
    : never;

export interface IState {
    canSubmit?: boolean;
    editPatientInfo?: PatientInfo;
    isDirected?: number;
    statement?: Statement[];
    contactId?: string;
    gender?: string;
    age?: string;
    ageText?: string;
    formType?: SKU_TYPE_VAL;
    zhusu?: string;
    status?: 'loading' | 'success' | 'error';
    servicePhone?: string;
    name?: string;
    images?: ImageInfo[];
}
