/*
 *  @Author: l<PERSON><PERSON>i
 * @Description: 选择已有咨询人
 */
import {memo, type FC, useMemo, useCallback, useState, useEffect} from 'react';
import {pxTransform} from '@tarojs/taro';
import {View, Text} from '@tarojs/components';
import {WiseDownArrow, WiseCheckSelectedSolid, WiseSelected} from '@baidu/wz-taro-tools-icons';
import cx from 'classnames';
import {Tag, Uploader} from '@baidu/wz-taro-tools-core';

import {getBdFileMapAtom} from '../../../../../store/triageStreamAtom/bdFileMapAtom';
import {formatAgeText} from '../../../common/utils';
import DiseaseDesc from '../DiseaseDesc';
import type {PatientList} from '../../patientList';

import type {IChoosePatientListProps} from './index.d';
import styles from './index.module.less';

const ChoosePatientList: FC<IChoosePatientListProps> = props => {
    const {
        collectedInfo,
        handleAddPatient,
        handleSelectPatient,
        state,
        dispatch,
        selectPatientData,
        files,
        TIRAGE_MAX_PIC_NUM,
        IMG,
        onUpload,
        setFiles
    } = props;
    const {patientList = []} = collectedInfo;
    const [patientListData, setPatientListData] = useState<PatientList[]>(
        patientList?.slice(0, 2) || []
    );
    const [showMoreBtn, setShowMoreBtn] = useState(false);

    useEffect(() => {
        patientList && patientList?.length > 2 && setShowMoreBtn(true);
        setPatientListData(patientList?.slice(0, 2) || []);
    }, [patientList]);

    const handleShowMore = useCallback(() => {
        setShowMoreBtn(false);
        setPatientListData(patientList);
    }, [patientList]);

    const getList = useMemo(() => {
        return (
            <View
                className={cx(
                    styles['patient-list-card'],
                    styles['form-card-radius-bottom'],
                    'wz-plr-39'
                )}
            >
                {patientListData?.map(item => {
                    return (
                        <View
                            key={item.contactId}
                            onTouchMove={e => {
                                e.stopPropagation();

                                return false;
                            }}
                            onClick={() => {
                                handleSelectPatient && handleSelectPatient(item);
                            }}
                        >
                            <View
                                className={cx(
                                    styles.listItem,
                                    'wz-flex wz-row-between wz-col-center'
                                )}
                            >
                                <View className='wz-flex wz-col-center'>
                                    <Text
                                        className={cx(
                                            styles.name,
                                            styles.line1,
                                            'wz-fs-48 wz-fw-500 wz-mr-27'
                                        )}
                                    >
                                        {item.name || '匿名'}
                                    </Text>
                                    <View className={cx(styles.genderAndAge, 'wz-fs-42')}>
                                        <Text>
                                            {item.gender || '男'}
                                            {item.age ? '/' : ''}
                                        </Text>
                                        {item.age && (
                                            <Text className='wz-mr-27'>
                                                {formatAgeText(item.age)}
                                            </Text>
                                        )}
                                    </View>
                                    {item.isCertified === 1 && (
                                        <Tag
                                            size='medium'
                                            shape='square'
                                            color='primary'
                                            variant='outlined'
                                            style={{
                                                padding: `${pxTransform(9)} ${pxTransform(14)} ${pxTransform(8)}`,
                                                fontSize: pxTransform(33),
                                                borderRadius: pxTransform(12)
                                            }}
                                            className='tagWrapper'
                                        >
                                            <View className='tagTxt wz-fw-500'>已实名</View>
                                        </Tag>
                                    )}
                                </View>
                                <View>
                                    {selectPatientData?.contactId === item.contactId ? (
                                        <WiseCheckSelectedSolid size={63} color='#00c8c8' />
                                    ) : (
                                        <WiseSelected size={63} color='#b7b9c1' />
                                    )}
                                </View>
                            </View>
                        </View>
                    );
                })}
                {/* 查看更多 */}
                {showMoreBtn && (
                    <View className='wz-flex wz-row-center wz-mb-48' onClick={handleShowMore}>
                        <View className={cx(styles.more, 'wz-fs-42')}>展开更多</View>
                        <WiseDownArrow size={45} color='#858585' />
                    </View>
                )}
            </View>
        );
    }, [
        handleSelectPatient,
        handleShowMore,
        patientListData,
        selectPatientData?.contactId,
        showMoreBtn
    ]);

    return (
        <View className='wz-pb-30 wz-mb-24'>
            {/* 选择患者 */}
            <View
                className={cx(
                    styles.titleWrapper,
                    'wz-flex wz-col-center wz-row-between wz-plr-39'
                )}
            >
                <View className='wz-fs-54 wz-fw-500'>选择患者</View>
                <View className={cx(styles.blue, 'wz-fs-45')} onClick={handleAddPatient}>
                    + 添加新患者
                </View>
            </View>
            {/* 患者列表 */}
            {getList}
            {/* 病情描述 */}
            <DiseaseDesc state={state} dispatch={dispatch} />
            <View className='wz-pt-54 wz-ml-39'>
                <View className='wz-fs-48 wz-mb-45 wz-fw-500'>上传图片</View>
                <Uploader
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    value={files}
                    multiple
                    maxFiles={TIRAGE_MAX_PIC_NUM}
                    tips={IMG.tips}
                    onUpload={onUpload}
                    onChange={setFiles}
                    bdFileMap={process.env.TARO_ENV === 'swan' ? getBdFileMapAtom() : undefined}
                />
            </View>
        </View>
    );
};

export default memo(ChoosePatientList);
