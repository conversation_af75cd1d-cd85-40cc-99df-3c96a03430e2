import {useCallback, useState, ReactNode} from 'react';
import {showToast} from '../../../utils/customShowToast';

import {useCommonPay} from '../../../../../utils-shared/pay/useCommonPay';

import {useGetUrlParams} from '../../../hooks/common';
import {useGetSessionId} from '../../../hooks/triageStream/pageDataController';

import {getUserBizActionReq} from '../../../models/services/triageStream';

import httpRequest from '../../../utils/basicAbility/comonRequest/cui';
import {ubcCommonClkSend} from '../../../utils/generalFunction/ubc';
import {parseURL, assembleUrl} from '../../../utils/generalFunction/urlRelated';

import {useMsgDataSetController} from '../../../hooks/triageStream/dataController';

import {API_HOST} from '../../../models/apis/host';
import {PhoneConsultModal} from '../../../components/PhoneConsultation';

import type {MsgId} from '../../../typings';
import type {FocusServiceSku} from '../FocusServiceCard/index.d';

interface IGetMakeOrderRelatedInfoType {
    selectedSkuItemData: FocusServiceSku | undefined;
    msgId: MsgId;
    skuList?: FocusServiceSku[];
}

export const useGetMakeOrderRelatedInfo = (args: IGetMakeOrderRelatedInfoType) => {
    const {selectedSkuItemData, msgId, skuList} = args;

    const {confirmPay} = useCommonPay();
    const sessionId = useGetSessionId();
    const {expert_id} = useGetUrlParams();
    const {updateMsgData} = useMsgDataSetController({msgId});

    const [PhoneConsultModalNode, setPhoneConsultModalNode] = useState<ReactNode>(null);

    // 用户支付取消时，刷新 sku 数据
    const onCancelPay = useCallback(async () => {
        const params = {
            bizActionType: 'switchCoupon' as const,
            chatData: {
                sessionId: sessionId || '',
                expertId: Number(expert_id || '')
            },
            bizActionData: {
                switchCouponInfo: {
                    msgId,
                    couponId: 0
                }
            }
        };
        const [err, data] = await getUserBizActionReq<'switchCoupon'>(params);
        if (!err) {
            data?.data?.message && updateMsgData(data?.data?.message[0]);
        }
    }, [expert_id, msgId, sessionId, updateMsgData]);

    /**
     *
     * @description 电话问诊下单
     */
    const makeOrderOfPhone = useCallback(
        (url: string) => {
            return new Promise(async (resolve, reject) => {
                try {
                    if (!url) {
                        console.error('电话问诊下单出错 payUrl 不存在，当前 sku 数据：', selectedSkuItemData);
                        reject(new Error('电话问诊下单出错 payUrl 不存在'));

                        return;
                    }

                    const [err, resp] = await httpRequest({
                        method: 'GET',
                        header: {'Content-Type': 'application/x-www-form-urlencoded'},
                        url: url?.includes('https://') || url?.includes('http://') ? url : `${API_HOST}${url}`
                    });

                    if (!err) {
                        if (resp?.status === 0) {
                            // @ts-ignore
                            const toast = resp?.data?.toast;
                            if (toast) {
                                showToast({
                                    title: toast,
                                    icon: 'none'
                                });

                                return;
                            }
                            // @ts-ignore
                            const {seePhone, actionData} = resp?.data || {};
                            const modalNode = (
                                <PhoneConsultModal
                                    title={actionData?.title}
                                    desc={actionData?.desc}
                                    descRich={actionData?.descRich}
                                    submitUrl={actionData?.submitUrl}
                                    defaultPhoneNumber={actionData?.default}
                                    seePhone={seePhone}
                                    onClosePopup={() => setPhoneConsultModalNode(null)}
                                    payWay='new'
                                    onCancelPay={onCancelPay}
                                />
                            );
                            setPhoneConsultModalNode(modalNode);
                            resolve(null);
                        }
                    }
                    reject(err);
                } catch (err) {
                    console.error('电话问诊下单出错', err);
                    reject(err);
                }
            });
        },
        [selectedSkuItemData, onCancelPay]
    );

    /**
     *
     * @description 确认支付
     */
    const makeOrder = useCallback(
        (skuDataToOrder: FocusServiceSku) => {
            return new Promise(async (resolve, reject) => {
                try {
                    const url = skuDataToOrder?.btnInfo?.interactionInfo.url || '';

                    if (!url) {
                        reject(new Error('makeOrder 下单 url 参数错误'));
                    }

                    const {params: paramsOfUrl, pathname} = parseURL(url);

                    const reqParams = {
                        ...paramsOfUrl
                    };
                    const resUrl = assembleUrl(pathname, reqParams);

                    if (skuDataToOrder?.formType === 2 && skuDataToOrder?.btnInfo?.interaction === 'request') {
                        await makeOrderOfPhone(resUrl);
                        resolve(null);
                    } else {
                        const data = await confirmPay({
                            interactionInfo: {
                                url: resUrl
                            },
                            onCancelPay
                        });

                        if (data?.isExpertUnavailable) {
                            showToast({
                                title: '名额已抢完, 您可继续选择该医生的其他服务',
                                icon: 'none'
                            });
                        }

                        if (data?.isUserUnavailable) {
                            showToast({
                                title: '本周名额已用尽, 您可继续选择该医生的其他服务',
                                icon: 'none'
                            });
                        }

                        resolve(null);
                    }

                    skuDataToOrder?.skuId && ubcCommonClkSend({
                        value: `streamFocusService_${skuDataToOrder?.skuId}`,
                        ext: {
                            product_info: {
                                skuList: skuList?.map(skuItem => {return skuItem?.skuId })
                            }
                        }
                    });
                } catch (err) {
                    reject(err);
                }
            });
        },
        [confirmPay, makeOrderOfPhone, onCancelPay]
    );

    /**
     *
     * @description 登录成功后，进行下单操作; 二次校验登录状态
     */
    const loginSuccessfulCallback = useCallback(
        (skuForOrder: FocusServiceSku) => {
            return new Promise(async (resolve, reject) => {
                try {
                    await makeOrder(skuForOrder);
                    resolve(null);
                } catch (err) {
                    reject(err);
                }
            });
        },
        [makeOrder]
    );

    return {
        PhoneConsultModalNode,
        loginSuccessfulCallback
    };
};
