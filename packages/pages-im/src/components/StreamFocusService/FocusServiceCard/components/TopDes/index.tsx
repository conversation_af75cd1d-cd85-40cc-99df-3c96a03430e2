import cx from 'classnames';
import { View } from '@tarojs/components';
import { WiseTip } from '@baidu/wz-taro-tools-icons';
import { memo, type FC, useMemo, useState, useCallback } from 'react';
import {Portal} from '@baidu/vita-ui-cards-common';
import GuarantedIcon from '../../../../../components/GuarantedIcon';
import ServiceInfoPopup from '../ServiceInfoPopup';

import styles from './index.module.less';
import type { StreamFocusServiceCardDataV2, FocusServiceSkuInfo } from '../../index.d';

interface TopDesProps {
    isGuaranteed: StreamFocusServiceCardDataV2['isGuaranteed'];
    data: StreamFocusServiceCardDataV2['topDes'];
}

const TopDes: FC<TopDesProps> = props => {
    const { data, isGuaranteed } = props;

    const [isServiceInfoPopupShow, setIsServiceInfoPopupShow] = useState(false); // 是否展示服务介绍弹窗
    const [serviceInfoPopupData, setServiceInfoPopupData] = useState<FocusServiceSkuInfo | null>(null); // 服务介绍弹窗数据

    const genServiceInfoPopup = useMemo(() => {
        return isServiceInfoPopupShow && serviceInfoPopupData ? (
            <ServiceInfoPopup
                info={serviceInfoPopupData}
                open={isServiceInfoPopupShow}
                closeCallback={() => {
                    setIsServiceInfoPopupShow(false);
                }}
            />
        ) : (
            <></>
        );
    }, [isServiceInfoPopupShow, serviceInfoPopupData]);

    const genDes = useMemo(() => {
        if (!data?.desc) {
            return <></>;
        }

        return data?.desc.map((i, idx) => {
            if (i.type === 'text') {
                return (
                    <View key={idx} className='wz-flex'>
                        {idx !== 0 && <View className={cx(styles.topDesLine, 'wz-mr-21 wz-ml-21')}>|</View>}
                        <View className={cx(styles.topDesText)}>{i.value}</View>
                    </View>
                );
            } else if (i.type === 'icon') {
                setServiceInfoPopupData(i.interactionInfo.content);

                if (isGuaranteed) {
                    return <GuarantedIcon key={idx} />;
                }

                return <WiseTip key={idx} color='#B8B8B8' size={42} className='wz-ml-21' />;
            }
        });
    }, [data?.desc, isGuaranteed]);

    /**
     *
     * @description 打开服务介绍弹窗
     */
    const openServiceInfoPopup = useCallback(() => {
        setIsServiceInfoPopupShow(true);
    }, []);

    return (
        <View className={cx(styles.topDesContainer, 'wz-pb-24')}>
            <View className={cx(styles.topDesTitle, 'wz-fs-54')}>{data?.title}</View>
            <View className={cx(styles.topDesContent, 'wz-flex wz-fs-42')} onClick={openServiceInfoPopup}>
                {genDes}
            </View>
            <Portal>{genServiceInfoPopup}</Portal>
        </View>
    );
};

export default memo(TopDes);
