import cx from 'classnames';
import { View, Image } from '@tarojs/components';
import { SafeArea, Popup } from '@baidu/wz-taro-tools-core';
import { memo, type FC, useMemo } from 'react';
import styles from './index.module.less';
import type { FocusServiceSkuInfo } from '../../index.d';

interface IProps {
    open: boolean;
    info: FocusServiceSkuInfo;
    closeCallback: () => void;
}

const ServiceInfoCard: FC<IProps> = (props: IProps) => {
    const { open, info, closeCallback } = props;
    const { sloganUrl = '', serviceIcon = '', list = [] } = info;

    // 列表渲染
    const renderList = useMemo(() => {
        return list.map(item => {
            return (
                <View className={cx('wz-flex', 'wz-col-center', styles.listWrapper)} key={item.title}>
                    <Image src={item.icon || ''} className={cx('wz-mr-45', styles.icon)} />
                    <View className={styles.contentWrapper}>
                        <View className={cx('wz-mb-18', 'wz-fs-48', 'wz-fw-500', styles.title)}>{item.title}</View>
                        <View className={cx('wz-fs-42', styles.content)}>{item.desc}</View>
                    </View>
                </View>
            );
        });
    }, [list]);

    const renderTitle = useMemo(() => {
        return (
            <View className={cx('wz-flex', 'wz-row-center', 'wz-col-center', styles.titleWrapper)}>
                <Image src={sloganUrl} className={styles.sloganIcon} />
                <View className={styles.line}>|</View>
                <Image src={serviceIcon} className={styles.serviceIcon} />
            </View>
        );
    }, [sloganUrl, serviceIcon]);

    const genCon = useMemo(() => {
        return (
            <Popup open={open} onClose={closeCallback} rounded placement='bottom'>
                <Popup.Close />
                <View className={cx('wz-br-36', styles.serviceTipWrapper)}>
                    {renderTitle}
                    {renderList}
                    <SafeArea position='bottom' style={{ background: '#fff' }} />
                </View>
            </Popup>
        );
    }, [closeCallback, open, renderList, renderTitle]);

    return genCon;
};

export default memo(ServiceInfoCard);
