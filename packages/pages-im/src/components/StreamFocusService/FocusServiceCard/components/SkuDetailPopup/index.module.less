.skuDetailContainer {
    width: 100%;
    padding: 36px 51px 0 51px;
    box-sizing: border-box;
    overflow: hidden;

    .skuDetailTitle {
        color: #1f1f1f;
        font-weight: bold;
    }

    .skuRules {
        color: #858585;
        line-height: 1.5;
    }

    .skuRightItem {
        color: #1f1f1f;

        .skuRightValue {
            line-height: 54px;
        }

        .wiseCheckSelected {
            line-height: 54px;
        }
    }

    .skuPriceTitle {
        color: #1f1f1f;
        font-weight: bold;
        margin-top: 108px;
    }

    .priceItem {
        display: flex;
        width: 100%;
        color: #1f1f1f;
        align-items: center;
        justify-content: space-between;
        line-height: 1;

        .priceTitle {
            display: flex;
            align-items: center;

            .priceTitleIcon {
                display: inline-block;
                width: 45px;
                height: 45px;
                background-size: cover;
                background-repeat: no-repeat;
            }
        }

        .priceValue {
            line-height: 1;
            font-family: PingFangSC-Medium;
            font-weight: bold;
        }

        .discountValue {
            color: #fd503e;
            font-family: PingFangSC-Medium;
            font-weight: bold;
        }
    }

    .servicePriceTip {
        background: #f3f4f5;
        font-family: PingFangSC-Regular;
        color: #858585;
        line-height: 56px;
    }
}

.popupBtnContainer {
    width: 100%;
    height: 180px;
    border-top: 1px solid #e0e0e0;
    padding: 24px 51px;
    box-sizing: border-box;

    .priceContainer {
        flex: 1;
    }

    .salePrice {
        color: #1f1f1f;

        .redText {
            color: #fd503e;
            font-weight: bold;
        }

        .salePriceText {
            font-weight: bold;
        }

        .salePriceValue {
            font-weight: bold;
        }
    }

    .discountPrice {
        color: #858585;
    }

    .popBtn {
        width: 659px;
        height: 132px;
        background: #00c8c8;
        border-radius: 66px;
        color: #fff;
        font-weight: bold;
        line-height: 132px;
        text-align: center;
    }
}
