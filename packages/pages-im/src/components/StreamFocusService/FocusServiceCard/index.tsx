import cx from 'classnames';
import {View, Block} from '@tarojs/components';
import {memo, type FC, useState, useCallback, useMemo} from 'react';
import {Portal} from '@baidu/vita-ui-cards-common';
import {WImage} from '@baidu/wz-taro-tools-core';

import {showToast} from '../../../utils/customShowToast';
import CouponExplanation from '../../../components/pagePopup/CouponPopup/CouponExplanation';
import CouponPopup from '../../../components/pagePopup/CouponPopup';
import {imgUrlMap} from '../../../constants/resourcesOnBos';
import {getUserBizActionReq} from '../../../models/services/triageStream';
import type {ICouponList} from '../../../typings/service';
import {useMsgDataSetController} from '../../../hooks/triageStream/dataController';
import {useScrollControl} from '../../../hooks/triageStream/useScrollControl';
import {useCalcPriceAndCoupon} from '../hooks/useCalcPriceAndCoupon';

import TopDes from './components/TopDes';
import SkuModule from './components/SkuModule';
import CouponModule from './components/CouponModule';
import SkuDetailPopup from './components/SkuDetailPopup';
import styles from './index.module.less';
import type {StreamFocusServiceCardDataV2Props, FocusServiceSku} from './index.d';

/**
 *
 * @description 定向服务卡 v2 版本
 * @returns
 */
const FocusServiceCard: FC<StreamFocusServiceCardDataV2Props> = props => {
    const {
        skuData,
        coupon,
        selectedSkuItemData,
        updateSkuDetailData,
        isLogin,
        updateCollectedInfoAndSku,
        isSkuDisabled,
        msgId,
        sessionId,
        expertId,
        PhoneConsultModalNode,
        adjectiveDirectedSkuMsgId,
        isSkuDetailPopupShow,
        setIsSkuDetailPopupShow
    } = props;
    const {userCoupons, couponInfo, couponTips} = coupon || {};
    // 优惠劵部分 state
    const [selectedCouponData, setSelectedCouponData] = useState<ICouponList>(couponInfo || {}); // 当前选中的优惠券数据
    const [explanationData, setExplanationData] = useState<ICouponList['description'] | null>(null); // 优惠券说明数据
    // 弹窗控制部分 state
    // const [isSkuDetailPopupShow, setIsSkuDetailPopupShow] = useState(false); // 是否展示 sku 详情弹窗
    const [isShowExplanation, setIsShowExplanation] = useState<boolean>(false); // 是否展示优惠券说明
    const [showPoupon, setShowPoupon] = useState<boolean>(false); // 优惠券是否展示
    const {updateMsgData} = useMsgDataSetController({msgId});
    const [calingPriceState, setCalingPriceState] = useState<boolean>(false); // 是否正在计算价格
    const {scrollToMessage} = useScrollControl();

    // 计算价格 & 优惠券相关逻辑
    const {selectedSkuTotalPriceInfo} = useCalcPriceAndCoupon({
        selectedCouponData,
        selectedSkuItemData,
        defaultSkuListData: skuData?.skuList
    });

    /**
     *
     * @description 更新 sku 详情数据 & 打开 sku 详情弹窗
     */
    const handleUpdateSkuDetailData = useCallback(
        (detailData: FocusServiceSku) => {
            // 排查问题需要，保留
            // eslint-disable-next-line no-console
            console.info('更新 sku 详情数据', detailData);
            setIsSkuDetailPopupShow(true);
            updateSkuDetailData && updateSkuDetailData(detailData);
        },
        [updateSkuDetailData, setIsSkuDetailPopupShow]
    );

    /**
     *
     * @description 更新优惠券说明数据
     */
    const updateExplanationData = useCallback(
        (explanationDataForShow: ICouponList['description']) => {
            setExplanationData(explanationDataForShow);
            setIsShowExplanation(true);
        },
        []
    );

    /**
     *
     * @description 生成 sku 详情弹窗
     */
    const genSkuDetailPopup = useMemo(() => {
        return isSkuDetailPopupShow ? (
            <SkuDetailPopup
                open={isSkuDetailPopupShow}
                skuData={selectedSkuItemData}
                priceData={selectedSkuTotalPriceInfo}
                closeCallback={() => {
                    setIsSkuDetailPopupShow(false);
                }}
                isLogin={isLogin}
                updateCollectedInfoAndSku={updateCollectedInfoAndSku}
            />
        ) : (
            <></>
        );
    }, [
        isSkuDetailPopupShow,
        selectedSkuItemData,
        selectedSkuTotalPriceInfo,
        isLogin,
        updateCollectedInfoAndSku,
        setIsSkuDetailPopupShow
    ]);

    // 关闭优惠券
    const onCloseCoupon = useCallback(() => {
        setShowPoupon(false);
    }, []);

    /**
     *
     * @description 更新选中的优惠券数据
     */
    const updateSelectedCouponData = useCallback(
        async couponData => {
            const params = {
                bizActionType: 'switchCoupon' as const,
                chatData: {
                    sessionId,
                    expertId: Number(expertId || '')
                },
                bizActionData: {
                    switchCouponInfo: {
                        msgId,
                        couponId: couponData?.id || 0
                    }
                }
            };
            setCalingPriceState(true);
            // TODO 此处会获取到在优惠券弹窗中获取到的优惠券信息，根据优惠券id和msgid去请求消息接口，然后调用更新整个sku消息
            const [err, data] = await getUserBizActionReq<'switchCoupon'>(params);
            if (!err) {
                setCalingPriceState(false);
                data?.data?.message && updateMsgData(data?.data?.message[0]);
                setSelectedCouponData(couponData);
                onCloseCoupon();
            }
        },
        [expertId, msgId, onCloseCoupon, sessionId, updateMsgData]
    );

    /**
     *
     * @description 生成 sku 优惠劵弹窗
     */
    const genCouponDetailPopup = useMemo(() => {
        // eslint-disable-next-line max-len
        return (
            showPoupon &&
            ((userCoupons?.list && userCoupons?.list.length > 0) ||
                (userCoupons?.disableList && userCoupons?.disableList.length > 0)) && (
                <CouponPopup
                    calingLoading={calingPriceState}
                    disableCouponList={userCoupons?.disableList || []}
                    enableCouponList={userCoupons?.list || []}
                    defaultSelectCouponData={couponInfo || null}
                    showCouponExplanation={updateExplanationData}
                    changeSelectedCouponCallback={updateSelectedCouponData}
                    closeCallback={() => {
                        setShowPoupon(false);
                    }}
                    sourceVal='imRepurchase'
                />
            )
        );
    }, [
        showPoupon,
        userCoupons?.list,
        userCoupons?.disableList,
        calingPriceState,
        couponInfo,
        updateExplanationData,
        updateSelectedCouponData
    ]);

    /**
     *
     * @description 生成优惠券说明弹窗
     */
    const genCouponExplainPopup = useMemo(() => {
        if (isShowExplanation) {
            return (
                <CouponExplanation
                    data={explanationData}
                    open={isShowExplanation}
                    onClose={() => {
                        setIsShowExplanation(false);
                        setExplanationData(null);
                    }}
                />
            );
        }

        return null;
    }, [explanationData, isShowExplanation]);

    // 点击优惠券
    const onTabCoupon = useCallback(() => {
        setShowPoupon(true);
    }, [setShowPoupon]);

    /**
     *
     * @description 生成优惠券模块
     */
    const genCouponModule = useMemo(() => {
        if (userCoupons?.list && userCoupons?.list.length > 0) {
            return <CouponModule onTabCoupon={onTabCoupon} couponTips={couponTips || []} />;
        }
        // eslint-disable-next-line max-len
    }, [couponTips, onTabCoupon, userCoupons?.list]);

    return (
        <View className={cx(styles.container)}>
            <Block>
                <TopDes isGuaranteed={skuData?.isGuaranteed} data={skuData?.topDes} />
            </Block>
            {/* 优惠券模块 */}
            <Block>{genCouponModule}</Block>
            {/* sku列表模块 */}
            <Block>
                <SkuModule
                    data={skuData?.skuList}
                    updateSkuDetailData={handleUpdateSkuDetailData}
                    updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                    isLogin={isLogin}
                />
            </Block>
            {/* sku详情弹窗 */}
            <>{genSkuDetailPopup}</>
            <>{genCouponDetailPopup}</>
            <>{genCouponExplainPopup}</>
            <>{PhoneConsultModalNode}</>
            <Portal>{genSkuDetailPopup}</Portal>
            <Portal>{genCouponDetailPopup}</Portal>
            <Portal>{genCouponExplainPopup}</Portal>
            <Portal>{PhoneConsultModalNode}</Portal>
            {/* 非最新的sku卡，不可点击，有遮罩层 */}
            {isSkuDisabled && (
                <View
                    className={styles.isDisabledContainer}
                    onClick={() => {
                        showToast({
                            title: '请点击最新的服务卡',
                            icon: 'none'
                        });
                        adjectiveDirectedSkuMsgId &&
                            scrollToMessage(adjectiveDirectedSkuMsgId, 'FocusServiceCard');
                    }}
                >
                    <WImage src={imgUrlMap.ineffectiveIcon} className={styles.ineffectiveIcon} />
                </View>
            )}
        </View>
    );
};

export default memo(FocusServiceCard);
