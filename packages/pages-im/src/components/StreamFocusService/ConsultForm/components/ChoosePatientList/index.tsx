/*
 *  @Author: l<PERSON><PERSON>i
 * @Description: 选择已有咨询人
 */
import {memo, type FC, useMemo, useCallback, useState, useEffect} from 'react';
import {pxTransform} from '@tarojs/taro';
import {View, Text, Form} from '@tarojs/components';
import {WiseDownArrow, WiseCheckSelectedSolid, WiseSelected} from '@baidu/wz-taro-tools-icons';
import cx from 'classnames';
import {Tag, Input, FormCard} from '@baidu/wz-taro-tools-core';
import {formatAgeText} from '../../../common/utils';

import TelInFo from '../TelInFo';
import DiseaseDesc from '../DiseaseDesc';

import type {PatientList} from '../../patientList.d';

import type {IChoosePatientListProps} from './index.d';
import styles from './index.module.less';

const ChoosePatientList: FC<IChoosePatientListProps> = props => {
    const {
        collectedInfo,
        handleAddPatient,
        formType,
        handleSelectPatient,
        state,
        dispatch,
        openPatientPopType,
        selectPatientData
    } = props;
    const {patientList = [], servicePhone} = collectedInfo;
    const [patientListData, setPatientListData] = useState<PatientList[]>(
        patientList?.slice(0, 2) || []
    );
    const [showMoreBtn, setShowMoreBtn] = useState(false);
    const [focus, setFocus] = useState(false);

    useEffect(() => {
        patientList && patientList?.length > 2 && setShowMoreBtn(true);
        patientList && setPatientListData(patientList?.slice(0, 2) || []);
    }, [patientList]);

    const handleShowMore = useCallback(() => {
        setShowMoreBtn(false);
        setPatientListData(patientList);
    }, [patientList]);

    const getList = useMemo(() => {
        return (
            <View
                className={cx(
                    styles['patient-list-card'],
                    styles['form-card-radius-bottom'],
                    'wz-plr-39'
                )}
            >
                {patientListData?.map(item => {
                    return (
                        <View
                            key={item.contactId}
                            onTouchMove={e => {
                                e.stopPropagation();

                                return false;
                            }}
                            onClick={() => {
                                handleSelectPatient && handleSelectPatient(item);
                            }}
                        >
                            <View
                                className={cx(
                                    styles.listItem,
                                    'wz-flex wz-row-between wz-col-center'
                                )}
                            >
                                <View className='wz-flex wz-col-center'>
                                    <Text
                                        className={cx(
                                            styles.name,
                                            styles.line1,
                                            'wz-fs-48 wz-fw-500 wz-mr-27'
                                        )}
                                    >
                                        {item.name || '匿名'}
                                    </Text>
                                    <View className={cx(styles.genderAndAge, 'wz-fs-42')}>
                                        <Text>
                                            {item.gender || '男'}
                                            {item.age ? '/' : ''}
                                        </Text>
                                        {item.age && (
                                            <Text className='wz-mr-27'>
                                                {formatAgeText(item.age)}
                                            </Text>
                                        )}
                                    </View>
                                    {item.isCertified === 1 && (
                                        <Tag
                                            size='medium'
                                            shape='square'
                                            color='primary'
                                            variant='outlined'
                                            style={{
                                                padding: `${pxTransform(9)} ${pxTransform(14)} ${pxTransform(8)}`,
                                                fontSize: pxTransform(33),
                                                borderRadius: pxTransform(12)
                                            }}
                                            className='tagWrapper'
                                        >
                                            <View className='tagTxt wz-fw-500'>已实名</View>
                                        </Tag>
                                    )}
                                </View>
                                <View>
                                    {selectPatientData?.contactId === item.contactId ? (
                                        <WiseCheckSelectedSolid size={63} color='#00c8c8' />
                                    ) : (
                                        <WiseSelected size={63} color='#b7b9c1' />
                                    )}
                                </View>
                            </View>
                        </View>
                    );
                })}
                {/* 查看更多 */}
                {showMoreBtn && (
                    <View className='wz-flex wz-row-center wz-mb-48' onClick={handleShowMore}>
                        <View className={cx(styles.more, 'wz-fs-42')}>展开更多</View>
                        <WiseDownArrow size={45} color='#858585' />
                    </View>
                )}
            </View>
        );
    }, [
        handleSelectPatient,
        handleShowMore,
        patientListData,
        selectPatientData?.contactId,
        showMoreBtn
    ]);

    return (
        <View className={cx(styles.patientList, 'wz-mlr-30 wz-pb-54 wz-mb-24')}>
            {formType === 2 && openPatientPopType === 'sku' && (
                <>
                    <View className={cx(styles.telInfoWrapper, 'wz-pt-39 wz-plr-39')}>
                        <TelInFo className={cx('wz-br-27')} telPhone={servicePhone} />
                    </View>
                    <Form>
                        <FormCard className={styles.telFormWrapper}>
                            <View
                                className={cx(
                                    styles.telInputWrapper,
                                    'wz-flex wz-row-between wz-col-center',
                                    'c-color-prime wz-fs-48 wz-mlr-39',
                                    'wz-ptb-54'
                                )}
                            >
                                <View className={cx('form__item__label')}>
                                    电话
                                    <Text className={styles.form__item__label__icon}>*</Text>
                                </View>
                                <View className='wz-flex'>
                                    <Input
                                        focus={focus}
                                        className={cx(styles.inputTel, 'wz-text-right wz-fs-48')}
                                        placeholder='请输入患者手机号'
                                        placeholder-className='gray-font wz-fs-48'
                                        type='text'
                                        maxlength={11}
                                        value={state?.telPhone}
                                        onBlur={() => {
                                            setFocus(false);
                                        }}
                                        onInput={e =>
                                            dispatch({payload: {telPhone: e.detail.value}})
                                        }
                                    />
                                </View>
                            </View>
                        </FormCard>
                    </Form>
                </>
            )}
            {/* 选择患者 */}
            <View
                className={cx(
                    styles.titleWrapper,
                    'wz-flex wz-col-center wz-row-between wz-plr-39'
                )}
            >
                <View className='wz-fs-54 wz-fw-500'>选择患者</View>
                <View className={cx(styles.blue, 'wz-fs-45')} onClick={handleAddPatient}>
                    + 添加新患者
                </View>
            </View>
            {/* 患者列表 */}
            {getList}
            {/* 病情描述 */}
            <DiseaseDesc state={state} dispatch={dispatch} />
        </View>
    );
};

export default memo(ChoosePatientList);
