import type { CollectedInfoProps } from '../../../PatientCard/index.d';
import type { PatientInfo, IState, IAction } from '../../index.d';
import type { OpenPatientType } from '../../../index.d';

export interface IChoosePatientListProps {
    collectedInfo: CollectedInfoProps;
    onInputItemChange: (key: string, info: PatientInfo) => void;
    handleAddPatient?: () => void;
    formType?: number;
    selectPatientVal?: string;
    handleSelectPatient?: (info: PatientInfo) => void;
    state: IState;
    dispatch: (args: IAction) => void;
    openPatientPopType: OpenPatientType;
    selectPatientData?: PatientInfo;
}
