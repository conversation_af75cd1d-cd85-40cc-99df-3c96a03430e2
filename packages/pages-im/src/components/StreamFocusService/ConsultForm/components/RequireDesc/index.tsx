/*
 *  @Author: lijing106
 * @Description: 接诊要求组件
 */
import React, { memo, type FC } from 'react';
import { View } from '@tarojs/components';
import cx from 'classnames';
import { WiseTipSolid } from '@baidu/wz-taro-tools-icons';
import styles from './index.module.less';
import type { IRequireDescProps } from './index.d';

const RequireDesc: FC<IRequireDescProps> = props => {
    const { requireDesc } = props;

    return (
        <View className={cx(styles.reception, 'wz-pt-33 wz-pb-39 wz-mlr-30 wz-br-36')}>
            <View className='wz-pl-39 wz-flex wz-pb-18'>
                {requireDesc?.titleCon?.map((item, index) => {
                    return (
                        <View key={index} className='wz-flex'>
                            {item?.type === 'text' && (
                                <View className='wz-fs-48 wz-pb-23 wz-ml-30 wz-fw-500'>{item?.value}</View>
                            )}
                            {item?.type === 'icon' && (
                                <WiseTipSolid className='receptionIcon' color='#FF9138' size={48} />
                            )}
                        </View>
                    );
                })}
            </View>
            <View className={cx(styles.receptionBox)}>
                {requireDesc?.descList?.map((item, index) => {
                    return (
                        <View className={cx(styles.receptionBoxItem)} key={index}>
                            <View className={cx(styles.receptionBoxTitle, 'wz-fs-42 wz-fw-500 wz-mt-36 wz-mb-27')}>
                                {item?.title}
                            </View>
                            {item?.desc
                                && item?.desc.length > 0
                                && item?.desc.map((descItem, ind) => {
                                    return (
                                        <View key={ind} className={cx(styles.receptionBoxItem, 'wz-fs-42')}>
                                            {descItem}
                                        </View>
                                    );
                                })}
                        </View>
                    );
                })}
            </View>
        </View>
    );
};

export default memo(RequireDesc);
