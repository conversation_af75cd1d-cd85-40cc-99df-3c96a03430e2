// 家医 tabbar 集合
const JIAYI_WECHAT_TABBAR_PATH = [
    'pages/jiayi/home/<USER>',
    'pages/jiayi/message/index',
    'pages/jiayi/personCenter/index'
];

// 微信家医主包path集合
const WECHAT_JIAYI_PATH = [...JIAYI_WECHAT_TABBAR_PATH, 'pages/bdpass/index'];

// 家医path集合
const JIAYI_WECHAT_PATH = [
    ...JIAYI_WECHAT_TABBAR_PATH,
    'pages/jiayi/userFeatures/history/index', // 我的开通/续费
    // 我的赠送
    'pages/jiayi/userFeatures/sendRecord/index', // 新
    // 我的问医生
    'pages/jiayi/userFeatures/myDoctor/index',
    // 购买家医卡-微信支付成功页
    'pages/jiayi/paySuccessForPresent/index', // 新
    // 购买家医卡-售卖页
    'pages/purchase/index', // wechat 期望
    'pages/jiayi/purchase/index', // h5 & wechat
    // 购买家医卡-支付成功页
    'pages/jiayi/purchasePaySuccess/index', // h5 不能动
    // 购买家医卡-手百订单详情页
    'pages/jiayi/purchaseOrderDetail/index' // h5 不能动
];

// 家医分包
const WECHAT_JIAYI_PACKAGE = [
    {
        root: 'pages/purchase',
        name: 'purchaseWX',
        pages: ['index']
    },
    {
        root: 'pages/jiayi/purchase',
        name: 'purchase',
        pages: ['index']
    },
    {
        root: 'pages/jiayi/purchaseOrderDetail',
        name: 'purchaseOrderDetail',
        pages: ['index']
    },
    {
        root: 'pages/jiayi/paySuccessForPresent',
        name: 'paySuccessForPresent',
        pages: ['index']
    },
    {
        root: 'pages/jiayi/userFeatures',
        name: 'userFeatures',
        pages: ['history/index', 'myDoctor/index', 'sendRecord/index']
    },
    {
        root: 'pages/common', // 涉及到 pass 登录插件均放置在此分包
        name: 'common',
        pages: [
            'webpage/index',
            'agreementList/index',
            'settings/index',
            'userAgreement/index',
            'loginMiddlePage/index'
        ]
    },
    {
        root: 'pages/comment',
        name: 'comment',
        pages: ['index']
    },
    {
        root: 'pages/order', // 订单相关页面
        name: 'order',
        pages: ['detail/index', 'list/index']
    },
    {
        root: 'pages/triage',
        name: 'triage',
        pages: ['index']
    },
    {
        root: 'pages/chat',
        name: 'chat',
        pages: ['index']
    },
    {
        root: 'pages/agreement',
        name: 'agreement',
        pages: ['index']
    },
    {
        root: 'pages/recipeldetail',
        name: 'recipeldetail',
        pages: ['index']
    },
    {
        root: 'pages/wecom',
        name: 'launch',
        pages: ['launch/index']
    },
    {
        root: 'pages/middlePage', // 微信中间页面
        name: 'middlePage',
        pages: ['index']
    },
    {
        root: 'pages/feedback',
        name: 'feedback',
        pages: ['index']
    },
    {
        root: 'pages/reward',
        name: 'reward',
        pages: ['index']
    },
    // 用户授权患者档案
    {
        root: 'pages/patientAuth', // 用户授权开关相关
        name: 'patientAuth',
        pages: ['record/index']
    }
];

// 家医分包预加载逻辑
const WECHAT_JIAYI_PRE_LOAD = {
    'pages/jiayi/home/<USER>': {
        network: 'all',
        packages: ['purchase', 'common']
    },
    'pages/jiayi/personCenter/index': {
        network: 'all',
        packages: ['userFeatures', 'common', 'purchase']
    },
    'pages/triage/index': {
        network: 'all',
        packages: ['chat']
    },
    'pages/chat/index': {
        network: 'all',
        packages: ['triage', 'common']
    },
    'pages/order/list/index': {
        network: 'all',
        packages: ['chat', 'common']
    },
    'pages/order/detail/index': {
        network: 'all',
        packages: ['chat', 'common']
    },
    'pages/recipeldetail/index': {
        network: 'all',
        packages: ['chat', 'common']
    }
};

// 家医 tabbar 设置
const WECHAT_JIAYI_TABBAR = {
    color: '#A7A9AB',
    backgroundColor: '#FAFBFC',
    selectedColor: '#00C1C1',
    borderStyle: 'black',
    list: [
        {
            pagePath: 'pages/jiayi/home/<USER>',
            text: '首页',
            iconPath: 'common/assets/home1_gray.png',
            selectedIconPath: 'common/assets/home1_green.png'
        },
        {
            pagePath: 'pages/jiayi/message/index',
            text: '消息',
            iconPath: 'common/assets/message_gray.png',
            selectedIconPath: 'common/assets/message_green.png'
        },
        {
            pagePath: 'pages/jiayi/personCenter/index',
            text: '个人中心',
            iconPath: 'common/assets/man_gray.png',
            selectedIconPath: 'common/assets/man_green.png'
        }
    ]
};

// 获取家医中所有微信小程序页面路径(主包+分包)
const JIAYI_ALL_PATH = WECHAT_JIAYI_PACKAGE.reduce(
    (total, item) => {
        const temp = item.pages.map(i => `${item.root}/${i}`);

        return [...total, ...temp];
    },
    [...WECHAT_JIAYI_PATH]
);

module.exports = {
    WECHAT_JIAYI_PATH,
    JIAYI_WECHAT_PATH,
    WECHAT_JIAYI_TABBAR,
    WECHAT_JIAYI_PACKAGE,
    WECHAT_JIAYI_PRE_LOAD,
    JIAYI_WECHAT_TABBAR_PATH,
    JIAYI_ALL_PATH
};
