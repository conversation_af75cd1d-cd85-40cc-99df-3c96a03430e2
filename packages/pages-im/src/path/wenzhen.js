// 路由映射关系，mars中已经存在需要重构的页面 如url命中key则跳转value
const PATH_MAP = {
    // 购药前置
    // '/pages/wz/medicineclinicguide/index': '/pages/medicineclinicguide/index',
    // 免责声明
    '/pages/wz/agreement/index': '/pages/agreement/index',
    // 处方详情
    '/pages/mall-sub/recipel-detail/index': '/pages/recipeldetail/index',
    // 处方列表
    '/pages/mall-sub/recipel-list/index': '/pages/recipellist/index',
    // 订单详情
    '/pages/wz/order/index': '/pages/order/detail/index',
    // 投诉反馈
    '/pages/wz-sub/feedback/index': '/pages/feedback/index',
    // 我的医生列表
    '/pages/patient/mydoctor/index': '/pages/serviceRecord/list/index',
    // 我的医生详情-服务记录
    '/pages/patient/servicelist/index': '/pages/serviceRecord/detail/index',
    // 查看报道
    '/pages/patient/registerdetail/index': '/pages/patientRegistration/detail/index',
    // 医生团队
    '/pages/wz-sub/teamhome/index': '/pages/teamhome/index'
};

// 百度小程序path集合
const BAIDU_PATH = [
    // 诊前
    'pages/triage/index',
    'pages/triageStream/index',
    // 购药前置
    // 'pages/medicineclinicguide/index',
    // 免责声明
    'pages/agreement/index',
    // 特惠义诊
    'pages/freeclinic/index',
    // 处方详情
    'pages/recipeldetail/index',
    // 处方列表
    'pages/recipellist/index',
    // im流中我的处方列表页
    'pages/imRecipeLlist/index',
    // 打赏页
    'pages/reward/index',
    // 诊中
    'pages/chat/index',
    // 评价页
    'pages/comment/index',
    // 就诊信息页
    'pages/patientInfoDetail/index',
    // 表单收集页面
    'pages/formOrderDetail/orderDetail/index',
    // 一问一答详情页面
    'pages/askAnswerInterchange/askAnswerDetail/index',
    // 一问一答信息补充
    'pages/askAnswerInterchange/askAnswerFill/index',
    'pages/order/detail/index',
    'pages/order/list/index',
    'pages/common/validateLogin/index',
    'pages/common/agreementList/index',
    'pages/common/error/index',
    // 扫码购药
    'pages/buyMedicine/drugSuggest/index',
    'pages/buyMedicine/form/index',
    // 投诉反馈
    'pages/feedback/index',
    // AI自测
    // 'pages/aiTrial/index',
    // AI 皮肤检测自定义相机页
    'pages/aiskin/custcamera/index',
    // AI 皮肤检测结果页
    'pages/aiskin/skinfeedback/index',
    // AI 皮肤检测结果页
    'pages/aiskin/agreement/index',
    // 医生榜单排行详情页
    'pages/multipleClients/doctor/rank/detail/index',
    // 医生榜单列表页
    'pages/multipleClients/doctor/rank/list/index',
    // 影像资料列表
    'pages/dicom/list/index',
    // 添加影像资料
    'pages/dicom/form/index',
    // 病历公开设置
    'pages/privacySetting/index',
    // 我的医生列表
    'pages/serviceRecord/list/index',
    // 我的医生详情-服务记录
    'pages/serviceRecord/detail/index',
    // 查看报道
    'pages/patientRegistration/detail/index',
    // 自测答题
    'pages/selfTrial/trialQuestion/index',
    // 自测历史
    'pages/selfTrial/trialHistory/index',
    // 自测推荐列表
    'pages/selfTrial/trialList/index',
    // 自测详情
    'pages/selfTrial/trialDetail/index',
    // 精选医生
    'pages/doctor/jingxuan/index',
    // 医生团队
    'pages/teamhome/index'
];

// 问诊H5独有页面集合 不会打进mars独立分包
const H5_PATH = [
    // H5 PDF 中间页
    'pages/pdf/index',
    // 跳转中间页
    'pages/middlePage/index',
    // 登陆中间页
    'pages/common/wxauth/index',
    // 家医 H5
    'pages/jiayi/purchase/index', // h5 & wechat
    // 购买家医卡-支付成功页
    'pages/jiayi/purchasePaySuccess/index', // h5 不能动
    // 购买家医卡-手百订单详情页
    'pages/jiayi/purchaseOrderDetail/index', // h5 不能动
    // 用户协议服务列表页
    'pages/common/agreementList/index',
    'pages/common/webpage/index',
    'pages/wecom/launch/index',
    // 登录中间页
    'pages/common/loginMiddlePage/index',
    'pages/common/error/index',
    // 扫码购药页面
    'pages/buyMedicine/drugSuggest/index',
    'pages/buyMedicine/form/index',
    // 医生榜单排行详情页
    'pages/multipleClients/doctor/rank/detail/index',
    // 医生榜单列表页
    'pages/multipleClients/doctor/rank/list/index',
    'pages/patientAuth/record/index',
    'pages/doctor/jingxuan/index'
];

const WECHAT_TABBAR = {
    color: '#A7A9AB',
    backgroundColor: '#FAFBFC',
    selectedColor: '#00C1C1',
    borderStyle: 'white',
    list: [
        {
            pagePath: 'pages/wxHome/index',
            text: '首页',
            iconPath: 'common/assets/home_gray.png',
            selectedIconPath: 'common/assets/home_green.png'
        },
        {
            pagePath: 'pages/message/list/index',
            text: '消息',
            iconPath: 'common/assets/news_gray.png',
            selectedIconPath: 'common/assets/news_green.png'
        },
        {
            pagePath: 'pages/personalCenter/index',
            text: '我的',
            iconPath: 'common/assets/man_gray.png',
            selectedIconPath: 'common/assets/man_green.png'
        }
    ]
};

const WECHAT_TABBAR_PATH = WECHAT_TABBAR.list.map(item => item.pagePath);

// 微信问诊主包配置
const WECHAT_PATH = [
    'pages/wxHome/index',
    'pages/personalCenter/index',
    'pages/message/list/index',
    'pages/bdpass/index',
    'pages/middlePage/index'
];

// 微信问诊分包配置
const WECHAT_PACKAGE = [
    {
        root: 'pages/triage',
        name: 'triage',
        pages: ['index']
    },
    {
        root: 'pages/triageStream',
        name: 'triageStream',
        pages: ['index']
    },
    {
        root: 'pages/chat',
        name: 'chat',
        pages: ['index']
    },
    {
        root: 'pages/comment',
        name: 'comment',
        pages: ['index']
    },
    {
        root: 'pages/reward',
        name: 'reward',
        pages: ['index']
    },
    {
        root: 'pages/agreement',
        name: 'agreement',
        pages: ['index']
    },
    {
        root: 'pages/recipeldetail',
        name: 'recipeldetail',
        pages: ['index']
    },
    {
        root: 'pages/recipellist',
        name: 'recipellist',
        pages: ['index']
    },
    {
        root: 'pages/imRecipeLlist',
        name: 'imRecipeLlist',
        pages: ['index']
    },
    {
        root: 'pages/doctor', // 医生专家相关页面
        name: 'doctor',
        pages: [
            'info/index',
            'comment/index',
            'detail/index',
            'list/index',
            'home/index',
            'filter/index',
            'hospitallist/index',
            'search/index',
            'hospitalDepartment/index'
        ]
    },
    {
        root: 'pages/aiTrial',
        name: 'aitrial',
        pages: ['index']
    },
    // {
    //     root: 'pages/aiskin',
    //     name: 'aiskin',
    //     pages: ['custcamera/index', 'skinfeedback/index', 'agreement/index']
    // },
    {
        root: 'pages/selfTrial',
        name: 'selfTrial',
        pages: ['trialQuestion/index', 'trialHistory/index', 'trialList/index', 'trialDetail/index']
    },
    {
        root: 'pages/aiskin',
        name: 'aiskin',
        pages: ['custcamera/index', 'skinfeedback/index', 'agreement/index']
    },
    {
        root: 'pages/order', // 订单相关页面
        name: 'order',
        pages: ['detail/index', 'list/index']
    },
    {
        root: 'pages/common', // 涉及到 pass 登录插件均放置在此分包
        name: 'common',
        pages: [
            'webpage/index',
            'agreementList/index',
            'validateLogin/index',
            'settings/index',
            'userAgreement/index',
            'loginMiddlePage/index',
            'error/index'
        ]
    },
    {
        root: 'pages/patientRegistration', // 患者报到
        pages: ['detail/index', 'form/index', 'patientInfo/index']
    },
    {
        root: 'pages/serviceRecord', // 我的医生
        pages: ['detail/index', 'list/index']
    },
    // {
    //     root: 'pages/askAnswerInterchange', // 一问一答
    //     name: 'askAnswerInterchange',
    //     pages: ['askAnswerDetail/index', 'askAnswerFill/index']
    // },
    {
        root: 'pages/buyMedicine', // OTC 扫码购药相关
        name: 'buyMedicine',
        pages: ['drugSuggest/index', 'form/index']
    },
    {
        root: 'pages/patientInfoDetail',
        name: 'patientInfoDetail',
        pages: ['index']
    },
    {
        root: 'pages/formOrderDetail', // 专病表单提交订单详情
        name: 'formOrderDetail',
        pages: ['orderDetail/index']
    },
    // 引导添加企微落地页
    {
        root: 'pages/wecom',
        name: 'wecom',
        pages: ['launch/index']
    },
    // 视频问诊
    {
        root: 'pages/videoCall',
        name: 'videoCall',
        pages: ['index']
    },
    {
        root: 'pages/feedback',
        name: 'feedback',
        pages: ['index']
    },
    // 多端相关
    {
        root: 'pages/multipleClients',
        name: 'multipleClients',
        pages: ['doctor/rank/list/index', 'doctor/rank/detail/index']
    },
    // 义诊
    {
        root: 'pages/freeclinic',
        name: 'freeclinic',
        pages: ['index']
    },
    // 影像资料
    {
        root: 'pages/dicom',
        name: 'dicom',
        pages: ['list/index', 'form/index']
    },
    // 用户授权患者档案
    {
        root: 'pages/patientAuth', // 用户授权开关相关
        name: 'patientAuth',
        pages: ['record/index']
    },
    // 病历公开设置
    {
        root: 'pages/privacySetting',
        name: 'privacySetting',
        pages: ['index']
    }
    // 医生团队, 暂无微信页面
    // {
    //     root: 'pages/teamhome',
    //     name: 'teamhome',
    //     pages: ['index']
    // }
    // // test
    // {
    //     root: 'pages/test',
    //     name: 'test',
    //     pages: ['index']
    // }
];

// 向mars输出 独立分包配置
const WZ_TARO_FENBAO = BAIDU_PATH.map(item => {
    return item.slice(6);
});

// 获取问诊中所有微信小程序页面路径(主包+分包)
const WZ_ALL_PATH = WECHAT_PACKAGE.reduce(
    (total, item) => {
        const temp = item.pages.map(i => `${item.root}/${i}`);

        return [...total, ...temp];
    },
    [...WECHAT_PATH]
);

module.exports = {
    H5_PATH,
    PATH_MAP,
    BAIDU_PATH,
    WECHAT_TABBAR,
    WZ_TARO_FENBAO,
    WZ_ALL_PATH,
    WECHAT_TABBAR_PATH
};
