/* eslint-disable @typescript-eslint/no-explicit-any */
import type {
    CSSProperties,
    ReactNode,
    SVGAttributes,
    ComponentType,
    RefAttributes,
    DetailedHTMLProps,
    HTMLAttributes
} from 'react';

/// <reference types="@tarojs/taro" />
/// <reference path="../../../../typings/global.d.ts" />

declare module '*.png';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.styl';

declare namespace NodeJS {
    interface ProcessEnv {
        TARO_ENV: 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'quickapp' | 'qq' | 'jd';
    }
}

declare global {
    namespace JSX {
        interface IntrinsicElements {
            vcode: SwanComponentsProps;
        }
    }

    // 微信小程序全局对象声明
    const wx: {
        navigateToMiniProgram: (options: {
            appId: string;
            path?: string;
            extraData?: Record<string, any>;
            envVersion?: 'develop' | 'trial' | 'release';
            success?: (res: any) => void;
            fail?: (err: any) => void;
            complete?: () => void;
        }) => void;
        exit?: () => void;
        [key: string]: any;
    };

    // 百度小程序全局对象声明
    const swan: {
        navigateToMiniProgram?: (options: {
            appId: string;
            path?: string;
            extraData?: Record<string, any>;
            success?: (res: any) => void;
            fail?: (err: any) => void;
            complete?: () => void;
        }) => void;
        exit?: () => void;
        getEnvInfoSync: () => {
            appVersion?: string;
            [key: string]: any;
        };
        canIUse?: (api: string) => boolean;
        [key: string]: any;
    };

    interface Window {
        loginresult: (res: any) => void;
        __Weirwood: any;
        jWeixin?: any;
        sdkMachine?: any;
    }
}

declare module '*.less' {
    const content: {
        [className: string]: string;
        (
            ...names: Array<
                string | null | undefined | {[key: string]: string | boolean | undefined}
            >
        ): string;
    };
    export default content;
}

declare module '*.png' {
    const url: string;
    export default url;
}

declare module '*.svg' {
    const url: string;
    export default url;
}

declare module '*.svg?react' {
    const Component: ComponentType<SVGAttributes<SVGElement> & RefAttributes<SVGElement>>;
    export default Component;
}

export interface BaseComponent {
    className?: string;
    customStyle?: string | CSSProperties;
    children?: ReactNode | ReactNode[];
}
export interface TcLogExtra {
    /**
     * 点击模块唯一标示
     */
    mod: string;
}
export interface UrlParams {
    url?: string;
    path?: string;
    appKey?: string;
    urlType?: string | number;

    /**
     * 点击日志拓展字段
     */
    extra?: TcLogExtra;
}

/**
 * @description 上报事件方法类型
 * void
 * @param name: 事件名称
 * @param args: 上传的参数
 */
export interface handleEventFunc {
    (name: string, args: any): void;
}

interface SwanComponentsProps extends DetailedHTMLProps<HTMLAttributes<HTMLElement>, HTMLElement> {
    [key: string]: any;
}
