import {ImAudioCon} from '@baidu/wz-taro-tools-core/im-audio';
import {ImTextContent} from '@baidu/wz-taro-tools-core/im-text';
import {ImGuideWxCon} from '@baidu/wz-taro-tools-core/im-guide-wx';
import {ImSystemConProps} from '@baidu/wz-taro-tools-core/im-system';
import {ImRichTextCon} from '@baidu/wz-taro-tools-core/im-rich-text';
import {ImEducationCon} from '@baidu/wz-taro-tools-core/im-education';
import {IImGridCardCon} from '@baidu/wz-taro-tools-core/im-grid-card';
import {ICommonContentProps} from '@baidu/wz-taro-tools-core/im-common';
import {DrugsTipsContent} from '@baidu/wz-taro-tools-core/im-drugs-tips';
import {ImFaqCommentCon} from '@baidu/wz-taro-tools-core/im-faq-comment';
import {ImFaqContentCon} from '@baidu/wz-taro-tools-core/im-faq-content';
import {ImChatCommentCon} from '@baidu/wz-taro-tools-core/im-chat-comment';
import {DrugsChatContent} from '@baidu/wz-taro-tools-core/im-drugs-to-chat';
import {ImOpenQuestionCon} from '@baidu/wz-taro-tools-core/im-open-question';
import {IImFocusDoctorInfo} from '@baidu/wz-taro-tools-core/im-focus-doctor';
import {ImHighLiterTextCon} from '@baidu/wz-taro-tools-core/im-high-liter-text';
import {ImFocusRecommendCon} from '@baidu/wz-taro-tools-core/im-focus-recommend';
import {ImBannerCon} from '@baidu/wz-taro-tools-core/im-banner/im-banner.shared';
import {ImCouponCon} from '@baidu/wz-taro-tools-core/im-coupon/im-coupon.shared';
import {IImUndirectDoctorInfo} from '@baidu/wz-taro-tools-core/im-undirect-doctor';
import {PatientInfoContent} from '@baidu/wz-taro-tools-core/im-patient-info-collect';
import {ImMsgOtherContent} from '@baidu/wz-taro-tools-core/im-msg-other/im-msg-other.shared';
import {PatientInfoContent as MedicalRecordInfo} from '@baidu/wz-taro-tools-core/im-medical-record';

import {ImWxCodeData} from '@components/card/ImWxCode/imWxCode';
import {ImFocusServiceCon} from '@components/card/ImFocusService/index.d';
import {ConsultInfoContent} from '@components/card/ImConsultEdit/index.d';
import {ImNoFocusDoctorProps} from '@components/card/ImNoFocusDoctor/index.d';
import {ImUndirectServicetCon} from '@components/card/ImUndirectService/index.d';
import {ImAIRewriteTriagePortal} from '@components/card/ImAIRewriteTriage/index.d';
import {ImUndirectRecExpertCon} from '@components/card/ImUndirectRecExpert/index.d';
import {IMUndirectRecExpertHeadCon} from '@components/card/IMUndirectRecExpertHead/index.d';

// 文档地址 http://wiki.baidu.com/pages/viewpage.action?pageId=**********
import {SendTriageMsgThunkFullParams, SendMsgThunkParams} from '@src/store/imData/thunks';
import {WX_APP_KEY_MAP} from '@common/constants/common';
import {
    type MSG_CARDID_TYPE,
    MSG_CARDID_ENUM,
    type MSG_CARDID_STREAM_TYPE,
    MSG_CARDID_ENUM_STREAM
} from '../constants/msg';

import {OneOf} from './tsExtend';
import {GroupId, LoId, MsgId} from './common';
import {InteractionType, InteractionInfo} from './im';
import {PreconsultInfoValue, InfoCollectorFormProps, AiParamsInteraction} from './cui.d';

// ------------------ Card 相关 ------------------
export interface ICardProps<T> {
    cardId: MSG_CARDID_TYPE | MSG_CARDID_STREAM_TYPE;
    cardName: keyof typeof MSG_CARDID_ENUM | keyof typeof MSG_CARDID_ENUM_STREAM;
    data: CardsData<T>;
    version?: number;
}

export interface CardsData<T> {
    cardStyle?: CardStyle;
    actionInfo?: ActionInfo;
    content: T;
    ext?: {
        [key: string]: string | number;
    };
}

export interface CardStyle {
    needHead?: boolean;
    renderType?: number;
    isHidden?: boolean;
    oneByOne?: boolean; // InfoCollectorForm 特殊非通用字段
}

export interface ActionInfo {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

export interface DispatchEventCallbackParams {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

export interface ImAIRadio {
    name?: string;
    value?: string;
    extType?: 'input' | 'exclude' | 'submit';
    exclusion?: string;
    isChoose?: boolean;
}

export interface ImAICon {
    title?: string;
    optionsTitle?: string;
    optionsType?:
        | 'radio'
        | 'checkbox'
        | 'radioModal'
        | 'checkboxModal'
        | 'select'
        | 'input'
        | 'selectAge';
    optionsKey?: string;
    options: ImAIRadio[];
    extendedMsg?: ICardProps<ImSystemConProps>;
}

export interface ImPastProblemsCardData {
    cardId: MSG_CARDID_ENUM.ImPastProblems;
    cardName: 'ImPastProblems';
    data: {
        content: {
            title: string;
            subTitle: string;
            list: {
                value: string;
                aiParams: AiParamsInteraction;
            }[];
        };
    };
}

export interface ImHistoryMedicaItem {
    value: string;
    name?: string;
    gender?: string;
    age?: string;
    treatmentTime?: number;
    aiParams: AiParamsInteraction;
}

// 曾经问过历史病历
export interface ImHistoryMedicalData {
    cardId: MSG_CARDID_ENUM.ImHistoryMedical;
    cardName: 'ImHistoryMedical';
    data: {
        content: {
            title: string;
            list: ImHistoryMedicaItem[];
            showMaxLen?: number;
        };
    };
}

// 历史问诊卡
export interface ImHistoryConsultation {
    cardId: MSG_CARDID_ENUM.ImHistoryConsultation;
    cardName: 'ImHistoryConsultation';
    data: {
        content: {
            title: string;
            subTitle: string;
            list: ImHistoryConsultationItem[];
        };
    };
}

export interface ImHistoryConsultationItem {
    expert: ImHistoryConsultationExpert;
    age: string;
    gender: string;
    name: string;
    treatmentTime: number;
    btn: IButtonItem;
}

export interface ImHistoryConsultationExpert {
    name: string;
    pic: string;
    docId: string;
    isSameExpert: boolean;
    department: string;
    level: string;
}

export interface IButtonItem {
    value?: string;
    interaction?: InteractionType;
    interactionInfo?: InteractionInfo;
}

export interface IAiAgent {
    text?: {
        type?: string;
        value?: string;
    }[];
    buttons?: IButtonItem[];
}

// 诊前文字加语音
export interface ImTextAudio {
    text: string;
    audioUrl: string;
    duration: number;
    aiAgent?: IAiAgent;
}

// 新增卡片时需要扩展
export type CardInstanceCardDataType = OneOf<
    [
        ImSystemConProps,
        ImHighLiterTextCon,
        ImEducationCon,
        IImFocusDoctorInfo,
        IImGridCardCon,
        ImFocusServiceCon,
        ImUndirectServicetCon,
        IImUndirectDoctorInfo,
        PatientInfoContent,
        MedicalRecordInfo,
        ImAICon,
        ConsultInfoContent,
        ImTextContent,
        ImAudioCon,
        DrugsChatContent,
        ImChatCommentCon,
        ImFaqCommentCon,
        ImFaqContentCon,
        ImFocusRecommendCon,
        ImGuideWxCon,
        ImOpenQuestionCon,
        ImRichTextCon,
        ICommonContentProps,
        ImNoFocusDoctorProps,
        ImBannerCon,
        ImCouponCon,
        InfoCollectorFormProps,
        ImWxCodeData,
        ImPastProblemsCardData,
        ImUndirectRecExpertCon,
        IMUndirectRecExpertHeadCon,
        ImTextAudio,
        ImAIRewriteTriagePortal
    ]
>;

export type {
    ImSystemConProps,
    ImEducationCon,
    IImFocusDoctorInfo,
    IImGridCardCon,
    IImUndirectDoctorInfo,
    ImFocusServiceCon,
    ImUndirectServicetCon,
    ImHighLiterTextCon,
    PatientInfoContent,
    MedicalRecordInfo,
    DrugsTipsContent,
    ConsultInfoContent,
    ImMsgOtherContent,
    ImTextContent,
    ImAudioCon,
    DrugsChatContent,
    ImChatCommentCon,
    ImFaqCommentCon,
    ImFaqContentCon,
    ImFocusRecommendCon,
    ImGuideWxCon,
    ImOpenQuestionCon,
    ImRichTextCon,
    ICommonContentProps,
    ImNoFocusDoctorProps,
    ImBannerCon,
    ImCouponCon,
    ImWxCodeData,
    ImUndirectRecExpertCon,
    IMUndirectRecExpertHeadCon
};
// ------------------ Card 相关 ------------------

// ------------------ Message 相关 ------------------
// eslint-disable-next-line no-shadow
export enum OwnerTypeEnum {
    '系统' = 1,
    '服务方' = 2,
    '需求方' = 3
}
export type OwnerTypeValue = `${Extract<OwnerTypeEnum, number>}` extends `${infer N extends number}`
    ? N
    : never;

// eslint-disable-next-line no-shadow
export enum IsCancelEnum {
    '未取消' = 0,
    '已取消' = 1
}
export type IsCancelValue = `${Extract<IsCancelEnum, number>}` extends `${infer N extends number}`
    ? N
    : never;

export type MsgSendStatus = 'pending' | 'rejected' | 'fulfilled';

export interface IMsgProps {
    msgId: string;
    msgKey: string;
    sendTime: string;
    createTime: string;
    updateTime: string;
    contentMD5: string;
    loId: LoId;
    groupId: GroupId;
    isCancel: IsCancelEnum;
    posterInfo?: PosterInfo;
    ownerType: OwnerTypeValue;
    contentType?: number;
    // 前端增加的发送状态，用来处理消息发送中状态
    sendStatus?: MsgSendStatus;
    // 前端增加的是否可以修改住宿，用于诊前消息渲染修改主诉按钮
    canEditZhusu?: 1 | 0;
    // 前端增加的是否可以重新发送
    canReSend?: 1 | 0;
    reSendMsgInfo?: {
        canReSend: 1 | 0;
        preconsultStatus: PreconsultInfoValue;
    };
    content: ICardProps<CardInstanceCardDataType>;
    msgManagement?: ImMsgManagement;
    reSendTriageArgs?: SendTriageMsgThunkFullParams;
    reSendMsgArgs?: SendMsgThunkParams;
    cardStyle?: {
        renderType?: number;
    };
}

export interface PosterInfo {
    name?: string;
    avatar?: string;
    title?: string;
    link?: string;
    disableClickOut?: boolean; // 点击消息头像是否可以跳转到医生主页 | 我的问医生
}

type IUserProps = {
    name: string;
    avatar: string;
};

export interface ImMsgManagement {
    features: MsgManagementFeature[];
    contentForCopy: {
        value: string;
    };
    quotation: ImMsgQuotation;
    contentForQuoted: ImMsgQuotation;
}

export interface ImMsgQuotation {
    cardId: MSG_CARDID_TYPE;
    msgId: MsgId;
    content: {
        value: string;
        mediaTime: string;
        preUrl: string;
    };
    isCancel?: boolean;
}

export type MsgManagementFeatureType = 'copy' | 'quotation' | 'transformAudioToText';
export interface MsgManagementFeature {
    icon: string;
    text: string;
    type: MsgManagementFeatureType;
}

// 头像相关
export interface IRoleProps {
    user?: IUserProps;
    sys?: IUserProps;
    expert?: IUserProps;
}

export interface EventCallbackParamsInfo extends InteractionInfo {
    appName?: keyof typeof WX_APP_KEY_MAP;
    controlExtra?: {disabledFailedJump?: number};
    url?: string;
    resolveInfo?: {
        updateList: {
            key: keyof ICardProps<CardInstanceCardDataType>;
            value: unknown;
        }[];
        asyncUpdateKeyList: {
            key: keyof ICardProps<CardInstanceCardDataType>;
            fn: (arg: unknown) => unknown;
        }[];
    };
    modalContent?: {
        title?: string;
    };
}

export type PageType = 'triage' | 'chat';
