import {InteractionInfo, InteractionType, LoId} from '@src/typings';

import {IDoctorInfoProps, ISkuInfoProps} from '@components/pagePopup/RepurchasePopup/index.d';
import type {PatientFormContent, IState} from '@src/components/card/ImPatientForm/index.d';
import {ActionInfo, IMsgProps, IRoleProps} from './msg';

import {
    RecipelType,
    RecipelStatus,
    Prescription,
    Patient,
    TraditionalChineseMedicine,
    WesternMedicine
} from './recipel';

type IBoxInfoProps = {
    // 接口回调地址
    callbackUrl?: string;
    // 类型 上传图片 、投诉反馈等
    type?: string;
    // 上传图片最多数量
    limit?: number;
    // 跳转url
    url?: string;
    // 跳转url（小程序内）
    msUrl?: string;
};

export interface IReqConfProps {
    url: string;
    method?: 'GET' | 'POST';
    // 超时时间
    timeout?: number;
    // 是否需要登录
    isNeedLogin?: boolean;
    // 是否为首屏数据, 主要是用于首屏打点
    isFirstScreen?: boolean;
    // 是否是预下载请求
    isPrefetch?: boolean;
    // 是否页面内置未登录模块
    isInnerLogin?: boolean;
    header?: {[key: string]: string};
    data?: {[key: string]: string | number | object | boolean | unknown};
    cb?: () => void;
}

export interface ITooLItemProps {
    /**
     * 工具icon
     */
    icon: string;

    /**
     * 工具名称
     */
    title: string;

    /**
     * 工具类型
     */
    type: string;
}

export interface ImActionData {
    interactionInfo: InteractionInfo;
    interaction: InteractionType;
}

// 诊前顶部进度条
export interface INavProps {
    txt: string;
    selected: number;
}

export interface IRadioProps {
    text: string;
    value: string;
    exclude: string;
}

export interface IBoxProps {
    txt?: string;
    icon?: string;
    info: IBoxInfoProps;
}

export interface IToolsProps {
    // 消息提交接口
    messageCallback?: string;
    // 图片提交接口
    picsCallback?: string;
    box2?: IBoxProps[];
    box?: IBoxProps[];
    placeholder?: string;
    btnTxt?: string;
    // 单选选择 很奇怪 老问诊数据没有和消息绑定把单选数据单独拎出来了
    select?: {
        callbackUrl?: string;
        type: string;
        list: IRadioProps[];
    };
}

// 诊前获取页面配置 & 消息列表
export interface IAibotTriageProps {
    /**
     * 诊前顶部进度条
     */
    topNavBar: INavProps[];

    /**
     * 底部工具栏数据
     */
    tools: IToolsProps;

    /**
     * ai ID
     */
    questionId?: number;

    /**
     * 双方头像信息
     */
    role?: IRoleProps;
}

// 诊前发送消息返回数据
export interface ISendTriageProps {
    /**
     * 消息列表
     */
    medMsgList?: string[];

    /**
     * 消息列表
     */
    msgList?: string[];

    /**
     * 选择的数据
     */
    serviceGrid?: {
        appeal?: string;
    };
}

/**
 * 咨询中台初始化返回数据
 */
export interface IMsgInitProps {
    response?: object;
    talkGroupId?: string;
    talkId?: string;
    showQuery: boolean;
}

// 获取处方详情接口入参
export interface GetPrescriptionParams {
    riskInfo?: string;
    city?: string;
    province?: string;
    lng?: string;
    lat?: string;
    lid?: string;
    referlid?: string;
    bdPresCode?: string;
}

// eslint-disable-next-line no-shadow
export enum MAIN_NAVS_ENUM {
    all = 0,
    // 原医生
    prescription = 1,
    // 平台医生
    recommend = 2
}

export type MAIN_NAVS_TYPE =
    `${Extract<MAIN_NAVS_ENUM, number>}` extends `${infer N extends number}` ? N : never;

// 获取处方列表接口入参
export interface GetRecipelListParams {
    tag: 'all' | 'usable' | 'used';
    type: MAIN_NAVS_TYPE;
    dataSortTime?: Date | string;
    page?: number;
    pageSize?: number;
    referlid?: string;
    lid?: string;
}

// 删除处方接口入参
export interface IRecielListProps {
    bdPresCode?: string;
}

// 获取医患处方列表接口入参
export interface GetImRecipelListParams {
    loId?: string;
}

// 删除处方接口响应参数
// 待后端给出数据后补充
// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface IRecielListResProps {}

// 药品详情
export interface DrugsDetail {
    spuType: number;
    skuId: string;
    oldGoodsId: string;
    headImages: Array<string>;
    title: string;
    commonName: string;
    spec: string;
    price: number;
    stock: number;
    state: number;
    num: number;
}

// 提醒医生开方接口入参
export interface GetRemindDoctorParams {
    pres_code?: string;
    qid?: string;
}

// 提醒医生开方接口
// 待后端给出数据后补充
// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface GetRemindDoctorRes {}

// 收获地址预加载接口入参
export interface GetAddressParams {
    pres_code?: string;
    qid?: string;
}

// 收获地址预加载接口
export interface GetAddressRes {
    sync?: boolean;
}

// 评论详情
export interface CommentList {
    content: string;
    headPhoto: string;
    userName: string;
}

export type PrescriptionEnumType =
    `${Extract<MAIN_NAVS_ENUM, 1 | 2>}` extends `${infer N extends number}` ? N : never;

// 获取处方详情接口响应
export interface GetPrescriptionRes {
    itemType: PrescriptionEnumType;
    detailUrl: string;
    presCode: string;
    loId: LoId;
    qid: string;
    sealImg: string;
    version: string;
    patient: Patient;
    statusImg: string;
    coupon: {
        couponInstId: string;
        couponName: string;
        subTitle: string;
        price: number;
        show: number;
        reachNum: number;
        useStartTime: number;
        useEndTime: number;
    } | null;
    // 根据代码逻辑梳理，待有真实数据后确认
    coupons?: {
        price: {
            amount: number | string | null;
        } | null;
    }[];
    disclaimer: string;
    resourceId: number;
    status: RecipelStatus;
    presType: RecipelType;
    expertCommentCount: number;
    expertCommentRate: number;
    orderId: string | null;
    payStatus: number | null;
    sellerType: string | null;
    preOrderId: string | null;
    orderStatus: number | null;
    commentList: CommentList[];
    prescription: Prescription;
    allowCheckStoreEnable: number;
    drugsInfo: WesternMedicine[] | null;
    drugsInfoTcm: TraditionalChineseMedicine | null;
    drugs: {
        storeId: string;
        storeName: string;
        storeType: number;
        list: DrugsDetail[];
    };
    exPageUrl?: string;
    bdPresCode: string;
    realNaming?: RealNaming;
    // 处方当前user加密id
    presTok?: string;
    showRenewalBtn: boolean; // 控制显示续方按钮
    hasSuifang?: boolean; // 是否有随访，控制显示赠送随访包
}

// 处方详情-准备续方接口返回

export interface PostPrerepurchaseRes {
    drugPlanUniqueId: string;
    actionId: number;
    confirm: Confirm;
    showPatientPopup: boolean;
    popup_info: PatientFormContent;
}

interface Confirm {
    type: string;
    title: string;
    content: Content[][];
    confirmBtns: ConfirmBtn[];
}

interface ConfirmBtn {
    text: string;
    type: string;
    interaction: InteractionType;
    interactionInfo?: InteractionInfo;
}

interface Content {
    type: 'redText' | 'blackText';
    text: string;
}

// 申请续方接口参数
export interface PostApplyrepurchaseParams {
    bd_pres_code?: string;
    prescription_code: string;
    doctor_choose?: DOCTOR_CHOOSE_TYPE_VALUE;
    req_submit_params?: string;
    sf_ref?: string;
}

export interface MedicalRecord
    extends Omit<IState, 'history_diag_struct' | 'allergy_diagnosis_struct'> {
    history_diag_struct: string;
    allergy_diagnosis_struct?: string;
}

// eslint-disable-next-line no-shadow
export enum DOCTOR_CHOOSE_TYPE {
    // 原医生
    originalDoctor = 1,
    // 平台医生
    platformDoctor = 2
}

export type DOCTOR_CHOOSE_TYPE_VALUE =
    `${Extract<DOCTOR_CHOOSE_TYPE, number>}` extends `${infer N extends number}` ? N : never;

// 申请续方接口返回
export interface PostApplyrepurchaseRes {
    jumpMsUrl: string;
}

interface recipelItemData {
    list?: GetPrescriptionRes[];
    dataSortTime: Date;
}

// 获取处方列表接口详情
export interface GetRecipelListRes {
    data?: recipelItemData;
}

export interface GetRecipelNewList {
    data: {
        dataSortTime: Date;
        list: recipelResultItem[];
    };
}

export interface recipelResultItem {
    itemType: PrescriptionEnumType;
    bdPresCode: string;
    url?: string;
    titleInfo?: {
        title?: string;
        value?: string;
    };
    status: {text: string; color: string};
    info?: (
        | {
              type: string;
              title: string;
              value: {
                  drugId: string;
                  titleName: string;
              }[];
          }
        | {
              title: string;
              value: string;
              type?: undefined;
          }
    )[];
    op?: RecipelListBtn[];
}

export interface RecipelListBtn {
    type: string;
    text: string;
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

// 医患处方列表 处方Item
export interface imRecipelResultItem {
    url?: string;
    titleInfo?: {
        title?: string;
        value?: string;
    };
    status?: number | string;
    drugsInfo: {
        comment?: string;
        commonName?: string;
        dosage?: string;
        frequency?: string;
        spec?: string;
        unitNum?: number;
        usage?: string;
    } | null;
    drugsInfoTcm?: string | null;
    op?: {[key: string]: string};
}

// 实名认证信息
export interface RealNaming {
    idcard?: string;
    name?: string;
    phone?: string;
}

// 提交实名认证信息参数
export interface SubmitRealNameParams {
    name?: string;
    idcard?: string;
    // 1用户手动填写 2就诊人中台获取身份证
    idcardFillType?: number;
    phone?: string;
    // 1用户手动填写 2就诊人中台获取手机号
    phoneFillType?: number;
    bdPresCode?: string;
}

// 诊中获取消息列表返回数据
export interface IGetMsgListProps {
    /**
     * 消息列表
     */
    msgList?: IMsgProps[];

    /**
     * 是否有更多消息
     */
    hasMore?: number;
}

/**
 * 就诊人信息填写输入元素
 */
export interface PatientInputProps {
    key: ADD_PATIENT_KEY;
    placeholder: string;
    label: string;
    value?: string;
    defaultValue?: string;
}

/**
 * 添加就诊人key
 */
// eslint-disable-next-line no-shadow
export enum ADD_PATIENT_KEY {
    name = 'name',
    idcard = 'idcard',
    phone = 'phone'
}

// 复购弹层
export interface IBottomExpertProps {
    /**
     * 医生信息
     */
    docInfo?: IDoctorInfoProps;

    /**
     * 服务包列表:医生SKU营销卡
     */
    serviceInfo?: ISkuInfoProps;

    /**
     * 其他SKU营销卡
     */
    otherServiceInfo?: ISkuInfoProps;
}

// 曾经问过历史列表
export interface IHistoryListProps {
    age?: number;
    age_month?: number;
    editUrl?: string;
    qid?: string;
    sex?: string;
    text?: string;
    text2?: string;
    wz_patient_name?: string;
}

// 曾经问过
export interface IQuestionHistoryProps {
    deleteUrl?: string;
    historyList?: IHistoryListProps[];
    tips?: string;
}

/** 待支付页面相关接口开始 */
// 待支付消息列表
export interface IChatMsgListProps {
    /**
     * 顶部提示
     */
    whiteTip: {
        text?: string;
    };

    /**
     * 创建订单时间
     */
    createTime?: string;

    /**
     * 消息列表
     */
    msgList?: IMsgProps[];
}

export interface ICommitProps {
    [key: string]: string | number;
}

/** 待支付页面相关接口结束 */

export interface IConsultsummaryProps {
    pageTitle?: string;
    info?: {
        patientInfo: {
            title?: string;
            info?: string;
        };
        description?: {
            title?: string;
            content?: string;
        };
        drugs?: {
            name?: string;
            options?: string[];
        };
        multidrugs?: {
            drugs?: [
                {
                    name?: string;
                    options?: string[];
                }
            ];
        };
        guidance?: {
            title?: string;
            contents?: string[];
        };
        notice?: string;
    };
}

export interface IUndirectServiceProps {
    jumpMsUrl: string;
    payUrl: string;
    qid: number;
}

export interface IPatientInfoCommitProps {
    url?: string;
    toast?: string;
    type?: string;
}

export interface IChangephoneProps {
    lid: number;
    msg: string;
    status: number;
    toast: string;
}

export interface IRequestDataProps {
    status: number;
    data: object;
    msg: string;
    update?: () => void;
}

export interface IDecription {
    list: string[];
    title: string;
}

export interface ICouponTip {
    type?: string;
    value?: string;
}

export interface ICouponList {
    id?: number;
    cid?: number;
    name?: string;
    startTime?: number;
    endTime?: number;
    subTitle?: string;
    worth?: string;
    description?: IDecription[];
    category?: number;
    maxPrice?: number;
    fullPrice?: number;
    salePrice?: number;
    current?: number;
    usable?: number;
}

// 打赏页参数
export interface GetRewardParams {
    soId?: string;
    loId?: string;
}

export interface IPriceList {
    price: string;
    isSelected: number;
}

export interface IServicerInfo {
    id: string;
    name: string;
    avatar: string;
    url: string;
}

export interface IRewardBtn {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

// 打赏页数据
export interface GetRewardRes {
    priceList: IPriceList[];
    servicerInfo: IServicerInfo;
    actionInfo?: IRewardBtn;
    isReward?: 0 | 1; // 0 没有送过心意，1 送过心意
}

// 优惠劵信息
export interface SkuCouponInfo {
    id?: number;
    cid?: number;
    name?: string;
    endTime?: number;
    subTitle?: string;
    category?: number;
    maxPrice?: number;
    fullPrice?: number;
    salePrice?: number;
    startTime?: number;
}

export interface CouponInfo {
    cid: number;
    url: string;
    name: string;
    worth: string;
    end_time: number;
    subtitle: string;
    category: number;
    max_price: number;
    sale_price: number;
    full_price: number;
    start_time: number;
    description: string;
    currentSend?: boolean;
}

// 领取优惠劵入参
export interface GetCouponsByCodeParams {
    couponCode: string;
}

export interface CouponParamsType {
    recordId: number;
    skuId: string;
}

// 算券请求入参
export interface GetCalcCouponsParams {
    qid: number;
    skuCouponMap: CouponParamsType[];
    formResult?: number;
}

// 领取优惠劵返回值
export interface GetCouponsByCodeResponse {
    current_sent: number;
    coupon_info_map: {
        [k in string]: CouponInfo;
    };
    coupon_record_info: {
        id: number;
        cid: number;
        end_time: number;
        start_time: number;
        current_sent: number;
    }[];
}

// eslint-disable-next-line no-shadow
export enum GetPlanedCouponCodeSendType {
    被动领取 = 1,
    主动领取 = 2
}
export type GetPlanedCouponCodeSendTypeVal = `${Extract<
    GetPlanedCouponCodeSendType,
    number
>}` extends `${infer N extends number}`
    ? N
    : never;

export interface GetPlanedCouponCodeParams {
    trigger: string;
    qid?: string;
    groupId?: string;
}

export interface GetPatientBlockParams {
    expert_id: number;
    qid?: string;
    loId?: string;
    doc_id?: string;
}

export interface GetPatientBlockResponse {
    is_black: boolean;
    word?: string;
}

export interface couponInfoItemProps {
    title?: string;
    subtitle?: string;
    startTime?: number;
    endTime?: number;
    salePrice?: number;
    fullPrice?: number;
    worth?: string;
}

// 根据触发点请求发券码
export interface GetPlanedCouponCodeResponse {
    isLogin?: boolean;
    couponInfos?: couponInfoItemProps[];
}

// 就诊人页参数
export interface GetPatinentParams {
    loId: string;
}

export interface IExaminationPic {
    icon: string;
    origin: string;
    picID: string;
}

// 就诊人页数据
export interface GetPatinentRes {
    qid: number;
    sexStr: string;
    patientName: string;
    mobileStr: string;
    idCardStr: string;
    birthday: string;
    age: number;
    familyDiagnosis: string;
    historyDiagnosis: string;
    allergyDiagnosis: string;
    gestationStageStr: string;
    liverStr: string;
    kidneyStr: string;
    examinationPics: IExaminationPic[];
    loId: string;
    confirmedDisease: string;
}

export interface PatinentItemInfo {
    key?: string;
    label?: string;
}

export interface FormSectionProps {
    title?: string;
    type?: string;
    list?: PatinentItemInfo[];
}

// 微信用户-登录绑定响应
export interface UserBindWXResp {
    status?: number;
    msg?: string;
}

// 支付
export interface PayResponse {
    payUrl?: string;
    wxPayInfo?: {[key: string]: string};
    jumpMsUrl?: string;
    toast?: string;
    freeJumpUrl?: string;
    jumpUrl?: string;
    pay_url?: string;
    isExpertUnavailable?: boolean;
    isUserUnavailable?: boolean;
    successJumpUrl?: string;
    phone?: string;
    sceneType?: string; // makeOrderInplace原地调起支付
    cancelJumpUrl?: string; // 取消后跳转页面
    actionInfo?: ActionInfo;
}

// Location
export interface LocationRes {
    lat?: string;
    lng?: string;
    province?: string;
    name?: string;
}

export interface MenuListItem {
    text?: string;
    interaction?: string;
    interactionInfo?: {
        url?: string;
    };
}

// 通用引导浮层相关开始
export interface FloatMenuItem {
    icon?: string;
    menuList?: MenuListItem[];
}

export type IFloatMenuProps = {
    [k in string]?: FloatMenuItem;
};

export interface ITipGuideItem {
    type?: string;
    content?: string;
}

export interface IPouponGuideList {
    title?: string;
    img?: string;
}

export interface IPouponGuideItem {
    title?: string;
    list?: IPouponGuideList[];
}

export interface GuideItem {
    tipGuide?: ITipGuideItem[];
    pouponGuide?: IPouponGuideItem;
}

export type IGuideProps = {
    [k in string]?: GuideItem;
};
