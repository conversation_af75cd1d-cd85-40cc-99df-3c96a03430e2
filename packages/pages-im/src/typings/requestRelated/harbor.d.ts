import { GroupId } from '../common';

// eslint-disable-next-line no-shadow
export enum HarborMsgTypeEnum {
    TypeWenZhenImHistory = 1,
    // TypeWenZhen 消息类型：问诊IM
    TypeWenZhen = 33,
    // TypeXiTong 消息类型：服务通知
    TypeXiTong = 2,
    // TypeGouYao 消息类型：交易物流
    TypeGouYao = 3,
    // TypeGuaHao 消息类型：挂号消息
    TypeGuaHao = 4,
    // TypeUFO 消息类型：投诉反馈
    TypeUFO = 5,
    // TypeDaKa 消息类型：活动小助手
    TypeDaKa = 7,
    // TypePatient 消息类型：患者报道
    TypePatient = 9,
    // TypeCompany 消息类型：百度健康团队
    TypeCompany = 10,
    // TypeCommunity 消息类型：社区助手
    TypeCommunity = 11,
    // TypeShop 消息类型：店铺客服
    TypeShop = 12,
    // TypeWenZhenMiniProgram 消息类型：问诊微信小程序
    TypeWenZhenMiniProgram = 30,
    // TypeWenZhenMiniPrograFamilyDoctormNotify 消息类型：问诊微信小程序中家医相关 与 TypeFamilyDoctorMiniProgramIM 家医隔离
    TypeWenZhenMiniPrograFamilyDoctormNotify = 31,
    // TypeFamilyDoctorMiniProgramIM 消息类型：家医微信小程序，仅服务于诊中对话
    TypeFamilyDoctorMiniProgramIM = 32
}

export type HarborMsgType = `${Extract<HarborMsgTypeEnum, number>}` extends `${infer N extends number}` ? N : never;

export interface UpdateMsgReadReqParams {
    createTime: string;
    relationId: GroupId;
    type?: HarborMsgType;
    msgTypes: string; // 使用 , 拼接的 HarborMsgType 字符串
}
