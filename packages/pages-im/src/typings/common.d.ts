export type SoId = string; // 订单 id
export type LoId = string; // 履约 id
export type MsgId = string; // 消息 id
export type traceId = string; // 长链接信号传递值，用于链路追踪，不影响业务逻辑
export type GroupId = string; // talkGroup id
export type Qid = string; // talkGroup id

export type ValueOf<T> = T[keyof T];

export type WzSceneType =
    | 'wx_jiayi'
    | 'web_xuexiqiangguo'
    | 'hy_zixun'
    | 'wzWecom'
    | 'wzWechat'
    | 'zbWecom'
    | 'zbYizhenWecom'
    | 'hzbdWecom'
    | undefined;

// 快捷建议场景值：随访、患者报道、im发送
export type DpSceneType = 'follow' | 'patientreprt' | 'im' | '';

// url 参数
export interface UrlParamsType {
    channel_code?: string;
    scene?: WzSceneType; // 新增流程场景值
    qid?: Qid; // 主诉 id
    aiQid?: Qid; // 自测id
    questionId?: Qid; // 兼容老主诉 id
    question_id?: Qid; // 兼容老主诉 id
    soId?: SoId; // 订单 id
    loId?: LoId; // 履约单 id
    from?: string;
    lid?: string;
    url?: string; // webview 页面地址
    word?: string; // 输入框文字
    query?: string; // 搜索词
    path?: string;
    title?: string;
    bd_vid?: string;
    referlid?: string;
    expert_id?: string; // 医生 id
    expertId?: string; // 医生id
    department?: string; // 医生科室
    bdPresCode?: string; // 处方 id
    hideIMBtn?: string; // 是否隐藏 IM 按钮
    applyPopup?: string; // 处方相关，商城使用
    backUrl?: string; // 处方相关，商城使用
    isDirected?: string; // 是否为定向问诊
    unShowNewUserTag?: string; // 是否在ImLoginPopup登陆模块展示新人专享优惠
    webView?: string; // webview 页面链接
    sf_ref?: string; // 来源
    hdhome?: string; // 隐藏 home 按钮
    hdheader?: string; // 隐藏 header 返回按钮
    loType?: string; // 问诊类型 1：图文 2：电话问诊
    skip?: string; // 家医卡购买页 1：不自动跳转 其他自动跳转
    realFee?: number | undefined; // 家医卡订单详情金额
    popactMatid?: string; // 健康建议卡携带绑定商详入参
    doc_id?: string; // 新的医生id
    ref?: string;
    daoyuanPlan?: string; // 专病属性
    jingshaiUser?: string; // 专病属性
    hosCode?: string; // （专病）医院编码
    stddocid?: string; // （专病）医生id
    depCode?: string; // （专病）科室编码
    statistics?: string; // （专病）统计
    diseaseCode?: string; // （专病）疾病编码
    formResult?: string; // （专病）表单结果
    paysync?: string; // 控制订单详情页
    realPay?: string; // 是否0元单
    pageApi?: string; // 厨房详情页 pageApi请求老56需要
    $taroTimestamp?: string; // taro 内部跳转拼接的时间戳
    unNeedLogin?: string; // 小程序 webview 页面需要，判断是否需要同步登录状态
    jiahao?: string; // 号源类型 1: 加号 2: 公立
    order_id?: string; // 订单ID
    data?: string;
    filterSndDept?: string; //  科室高亮
    filterDisease?: string; // 疾病高亮
    filterCityCode?: string; // 地区高亮
    disease?: string; // 疾病高亮
    filterConds?: string; // 医生列表页 screen 筛选高亮
    locCityCode?: string; // 医生列表页 城市高亮
    filterDocLevel?: string; // 医生列表页 医生职称高亮
    filterConsultPrice?: string; // 医生列表页 价格区间高亮
    filterConsultType?: string; //  医生列表页 服务类型高亮
    filterGuahaoType?: string; //  医生列表页 挂号类型高亮
    filterHospitalType?: string; //  医生列表页 医院类型高亮
    filterGuahaoTime?: string; //  医生列表页 挂号时间高亮
    hospital?: string; // 医院名称
    hospital_id?: string; // 医院名称id
    q_department?: string;
    isPresent?: string; // 家医卡购买介绍页 1：赠送亲友 其他普通购买
    back_route?: string; // 家医场景参数
    source_expert_id?: string; // 医生首页没有 expert_id 时，需要 source_expert_id resource_id rs_id
    resource_id?: string; // 医生首页没有 expert_id 时，需要 source_expert_id resource_id rs_id
    rs_id?: string; // 医生首页没有 expert_id 时，需要 source_expert_id resource_id rs_id
    patientId?: string; // 报到患者 id
    patient_id?: string; // 报到患者 id
    preInputPatientId?: string; // 报到患者预填写 id
    coreid?: string;
    docId?: string; // 我的医生列表、服务记录页，医生id
    channel?: string; // 渠道，preinput为助理填写
    skuId?: string;
    entrance?: string; // sc用来判断来源的
    classId?: number | string; // 用药建议组合id
    recomId?: string; // 用药建议药品id
    webviewsign?: string; // webview 页面签名标识，携带该标识才具备分享，否则不能分享
    city?: string;
    targetUrl?: string; // h5目标跳转地址
    failUrl?: string; // h5失败跳转地址
    type?: string; // 拉起地址类型 h5 weapp swan
    targetVersion?: string; // 拉起小程序的版本 release,trial,develop
    callUrl?: string; // 校验页重新登录成功之后的回调地址
    sceneId?: string; // 微信二维码-短参数换取长参数id
    disabledFailedJump?: string; // 禁用跳转失败跳转链接
    isReload?: string; // 是否重新加载
    name?: string; // 企微名字
    renderType?: string; // 渲染方式
    content?: string; // 加密内容
    buyMedicineEdit?: boolean; // 内跳的扫码购药是否能编辑
    realCode?: string; // 定位城市code
    realName?: string; // 定位城市名称
    filterCity?: string; // 医院列表高亮字段
    rankType?: string; // 医院列表高亮字段
    localDoctor?: string; // 医生列表页筛选本地医生
    authorityDoctor?: string; // 医生列表页筛选权威医生
    skeleton?: string; // 骨架屏名称
    preOrderId?: string; // 预下单id
    skuOpenType?: string; // 登录后置中诊前 sku 登录的跳转类型
    hospitalCity?: string; // 医院所在城市名称
    hospitalLogo?: string; // 医院Logo
    // 视频问诊  start
    videoCallInfo?: string;
    // 视频问诊 end
    msg?: string; // 登录页需要的相关参数
    serviceId?: string; // 号源id
    accurate?: string;
    taskItemId?: string; // 患者的任务项ID;
    projectId?: string; // 项目ID
    isAutoLogin?: string; // 自动触发登录
    preRecordBizType?: string; // 预记录业务类型
    preRecordBizId?: string; // 预记录业务ID
    picID?: string; // 图片 bosID
    skinToken?: string; // AI皮肤检测权限 token
    aiSkinFrom?: string; // AI皮肤检测来源
    timestamp?: string; // 医生榜单列表页-月份时间戳
    instCode?: string; // 医生榜单列表页-榜单类型
    cid?: string; // 医生榜单列表页-科室
    province?: string; // 医生榜单列表页-省份
    date?: string; // 预约挂号日期
    isCms?: string; // 是否是CMS页面
    isAutoCreateOrder?: number | string; // 是否自动下单
    userSymptom?: string; // ai皮肤病-用户症状
    isCloseFugou?: string; // 是否关闭复购弹层
    botSessionID?: string; // 是否有Ai分导诊会话
    botBizID?: string; // 是否Ai分导诊key
    drugType?: 'recipel'; // 扫码购药, 处方传 'recipel'
    teamId?: number; // 患者档案传 teamId
    toastId?: 'floatingBall'; // 订单列表页，进入后提示内容 id
    addPatientStatus?: string; // 患者报到，1为展示新增就诊人
    autoRefund?: string; // 投诉反馈页面，触发自动退款
    top_expert_ids?: string; // 医生列表推荐医生
    customParams?: string; // 医生列表， 自定义参数
    step?: string; // 患者报到页面，1为报到流程，2为完善信息
    drClassId?: string; // 扫码购药，快捷建议商品组id
    drUid?: string; // 扫码购药uid
    dpScene?: DpSceneType; // 扫码购药快捷建议场景值
    drugPlanId?: string; // 处方药计划id
    drugPlanUniqueId?: string; // 处方药计划唯一id
    noAlreadyPopupJiayi?: string; // 家医卡购买介绍页 1：不弹已购买弹窗 其他弹窗,
    dpLevel1?: string; // 一级科室
    dpLevel2?: string; // 二级科室
    realUserPhone?: string; // 处方详情错误兜底页面-真实用户手机号
    pageType?: string; // 精选医生页，透传类型
    taskInfo?: string; // 春节活动任务配置信息
    clk_loc?: string;
    pagefrom?: string; // 页面来源，用于区分from
    tabStatus?: string; // 问诊订单页，透传状态
    originalQuery?: string; // 透传原始查询参数
}

// 长链接注册相关参数
export interface RegisterLcpParams {
    cuid: string;
    userKey: string;
    qid?: number;
}

// 主诉收集分享
export interface IShareProps {
    title?: string;
    shareUrl?: string;
    imageUrl?: string;
}

// 副标题信息
export interface SubtitleInfoProps {
    subtitle?: string;
    subtitleUrl?: string;
}

export interface ISceneParams {
    from?: string;
    expertId?: string;
    id?: string;
}
