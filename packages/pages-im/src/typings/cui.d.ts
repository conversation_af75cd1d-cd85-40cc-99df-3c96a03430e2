/* eslint-disable no-shadow */
import {CommonEventFunction} from '@tarojs/components';

import {MSG_CARDID_TYPE} from '@common/constants/msg';

import {ImWxCodeData} from '@components/card/ImWxCode/imWxCode';
import type {WhiteTipsProps} from '@components/card/ImWhiteTips/index.d';
import {
    IHeaderBtnListProps,
    ISkuInfoProps,
    IDoctorInfoProps
} from '@components/pagePopup/RepurchasePopup/index.d';
import type {LocationDetail} from '@common/utils/basicAbility/location/index.d';
import type {INoticeBar} from '@components/card/ImNoticeBar/index.d';

import {CouponInfo} from './service.d';
import {SkuData} from './repurchaseAndSku';
import {LoId, traceId, MsgId, GroupId, SoId, Qid} from './common';
import {IMsgProps, ICardProps, CardInstanceCardDataType} from './msg';
import {TimeCountDownData, DirectedTimerData} from './topDisplayArea';
import {ImFeatureTool, InteractionType, ImServicerData, InteractionInfo} from './im';

// cui 通用请求参数
export interface CuiCommonReqParams {
    loId?: LoId;
    qid?: Qid;
    traceId?: traceId;
}

export enum ViewTypeEnum {
    '获取历史' = 'pre',
    '获取最新' = 'next'
}
export type ViewType = `${ViewTypeEnum}`;

export interface GetMsgListParams extends CuiCommonReqParams {
    mediaInfos?: MediaInfoArg[];
    page: {
        msgCreateTime: string;
        size: number;
        viewType: ViewType;
    };
    location?: LocationDetail;
}

export interface MediaInfoArg {
    msgKey: string;
}

export interface GetMsgListResponse {
    groupId: GroupId;
    msgIds: MsgId[];
    userMsgIds: MsgId[];
    servicerMsgIds: MsgId[];
    msgData: {
        [key in MsgId]: IMsgProps;
    };
    inputData: ImInputData;
    hasMore: boolean;
    mediaInfos?: {
        [k in string]: {
            mediaType: SendMsgContentType;
            coverHandle: {
                [key in string]: {
                    procStatus: 2 | 3;
                    width: number;
                    height: number;
                    duration: number;
                    size: number;
                    bosKey: string;
                    previewURL: string;
                };
            };
            videoHandle: {
                [key in string]: {
                    procStatus: 2 | 3;
                    width: number;
                    height: number;
                    duration: number;
                    size: number;
                    bosKey: string;
                    previewURL: string;
                };
            };
        };
    };
    topFlowWindow: TopFlowWindow;
    globalModelWindow: GlobalModelWindow;
    videoRoomData: ImVideoRoomDataType;
}

export interface GuideTips {
    title: string;
    durtion: number;
}

export interface ImInputData {
    placeholder?: string;
    // 输入框引导提示
    guideTips?: GuideTips;
    sendBtn: {
        // 发送按钮控制
        disable?: boolean;
        value?: string;
    };
    status: 0 | 1; // 处理输入框 disable 状态
    optionsKey?: string;
    aiParams?: AiParamsInteraction;
    extTool?: {
        // 扩展工具控制
        disable?: boolean;
    };
    audioTool?: {
        disable?: boolean;
    };
}

// 消息发送类型
export enum SendMsgContentTypeEnum {
    '文本' = 1,
    '图片' = 2,
    '语音' = 3,
    '视频' = 4,
    '多张图片' = 6,
    '影像资料' = 228
}

// 语音发送参数
export interface AudioInfo {
    playTime: number;
    bosId: string;
    text: string;
}

export type SendMsgContentType =
    `${Extract<SendMsgContentTypeEnum, number>}` extends `${infer N extends number}` ? N : never;

export interface VideoInfoOfSendMsg {
    width: number;
    height: number;
    thumb: string;
    duration: number;
    size: number;
}

export interface SendMsgParams extends CuiCommonReqParams {
    msgKey: string;
    content: string;
    contentType: SendMsgContentType;
    blobValue?: string;
    isHidden?: boolean;
    needMockMsg?: boolean;
    payload?: SendMsgPayload;
    videoInfo?: VideoInfoOfSendMsg;
    quoteInfo?: {
        msgId: MsgId;
    };
}

// 发送 ai 预问诊消息所需参数
export interface SendMsgPayload {
    entities: {
        [key in string]: string | number;
    };
    intent: string;
    msgKey: string;
    sessionID?: string;
}

export interface SendMsgResponse {
    msgIds: MsgId[];
    userMsgIds: MsgId[];
    servicerMsgIds: MsgId[];
    msgData: {
        [key in MsgId]: IMsgProps;
    };
}

export type GetFrameDataParams = CuiCommonReqParams;

// 首屏必要接口返回值
export interface GetFrameCoreDataResponse {
    actionData: ImActionData;
    toolData: ImToolData;
    statusData: ImStatusData;
    keyInfo: ImKeyInfoData;
}

// 首屏非必要接口返回值
export interface GetFrameOtherDataResponse {
    servicerData: ImServicerData;
    adsData: ImAdsData;
    fwData: ImFwData;
    progressData: ImProgressData;
    teamData?: ImTeamData;
}

// 诊中轮询数据返回值
export interface GetFrameDynamicDataResponse {
    toolData: ImToolData;
    fwData?: ImFwData;
    progressData: ImProgressData;
    statusData: ImStatusData;
    keyInfo: ImKeyInfoData;
    actionData?: ImActionData;
}

export interface GetFrameAdsDataParams extends CuiCommonReqParams {
    couponCode: number; // 优惠劵 id
}

export interface GetFrameAdsDataResponse {
    adsData: {
        skuData: TopSkuData;
        repurchaseData: ImRepurchaseData;
    };
}

// Im 页面进度条相关信息
export interface ImProgressData {
    timeCountDown: TimeCountDownData;
    doctorCountDown: DirectedTimerData;
}

// IM 医生团队信息
export interface ImTeamData {
    introduce?: string;
    url?: string;
    expertAvatar?: string[];
}

// Im 页面进入后的自动操作
export interface ImActionData {
    interactionInfo: InteractionInfo;
    interaction: InteractionType;
}

// 顶部广告数据
export interface ImAdsData {
    skuData?: TopSkuData;
    repurchaseData?: ImRepurchaseData;
    newCustomerCouponInfo?: newCustomerCouponInfoProps;
    wxData?: {publicGuideData: ImWxCodeData};
    popData?: popCouponProps;
}

// 微信复购按钮
export interface IWxInfoProps {
    title?: string;
    subTitle?: string;
    tags?: string[];
    avatar?: string;
    btn?: {
        // 更多福利按钮
        text?: string;
        actionInfo?: ActionInfo;
    };
}

// 复购数据
export interface ImRepurchaseData {
    headerBtnList?: IHeaderBtnListProps[];
    skuInfo?: ISkuInfoProps;
    doctorInfo?: IDoctorInfoProps;
    othersInfo?: ISkuInfoProps;
    bottomTips?: BottomTipsProps;
    wxInfo?: IWxInfoProps;
    guahaoInfo?: IWxInfoProps;
    renderActionType?: 1 | 0; // 1：收起 0：默认
}

export interface BottomTipsProps {
    text?: string;
    icon?: string;
    type?: string;
    interaction?: InteractionType | 'autoPopup';
    interactionInfo?: InteractionInfo;
}

// 底部工具栏
export interface ImToolData {
    bottomTips?: BottomTipsProps;
    lineTools?: ImFeatureTool[];
    shortcutTools?: ImFeatureTool[];
    bottomTools?: ImFeatureTool[];
}

// 顶部 sku 数据
export interface TopSkuData {
    isOpen: boolean;
    type?: string;
    list: SkuData[];
}

// 顶部 步骤进度条
export interface INavTopTips {
    avatar?: string;
    title?: string;
    renderType: 'progress' | 'orderGuideTip'; // progress 线上对照组数据 ； orderGuideTip 新实验数据
    progress: INavPb[];
    orderGuideTip?: OrderGuideTip;
}

export interface OrderGuideTip {
    hide: boolean;
    titleInfo: GuideTitleInfo;
    viewOrderInfo: ViewOrderInfo;
    ongoingToast: OngoingToast;
}

export interface GuideTitleInfo {
    avatar: string;
    title: string;
    actionInfo: ActionInfo;
}

export interface ViewOrderInfo {
    title: string;
    avatar: string;
    actionInfo: ActionInfo;
}

export interface OngoingToast {
    hasOngoing: boolean;
    actionInfo: ActionInfo;
}

export interface INavPb {
    text: string;
    status: 0 | 1; // 0 未选中 1选中
}

// IM 氛围相关数据
export interface ImFwData {
    marqueeTips: string[];
}

// IM 关键数据
export interface ImKeyInfoData {
    groupId: GroupId;
    soId: SoId;
    loId: LoId;
    qid: Qid;
    loStatus: LoStatusValue;
    userData: ImUserInfo;
    doctorData?: ImDoctorInfo;
    loMainStatus: LoMainStatusValue;
    preconsultStatus?: PreconsultInfoValue;
    preconsultType?: 'AIGCYuWenZhen' | 'yuWenZhen';
}

// 问诊状态
export enum LoMainStatusEnum {
    '待服务' = 1,
    '服务中' = 2,
    '服务结束' = 3
}

export enum LoStatusEnum {
    '待分发' = 11,
    '分发中' = 12,
    '已分发资源方' = 13,
    '已分发医生' = 14,
    '诊前回收' = 15,
    '拒诊回收[非定向]' = 16,
    '已接诊待回复' = 21,
    '医生首回开始服务' = 22,
    '正常关单' = 31,
    '异常关单' = 32
}

export type LoMainStatusValue =
    `${Extract<LoMainStatusEnum, number>}` extends `${infer N extends number}` ? N : never;

export type LoStatusValue = `${Extract<LoStatusEnum, number>}` extends `${infer N extends number}`
    ? N
    : never;

// 预问诊状态
export enum PreconsultInfoEnum {
    '无预问诊' = 0,
    '进行中' = 1,
    '已结束' = 2
}
export type PreconsultInfoValue =
    `${Extract<PreconsultInfoEnum, number>}` extends `${infer N extends number}` ? N : never;

// IM 视频问诊房间数据
export interface ImVideoRoomDataType {
    icon: string;
    text: string;
    btns: {
        value: string;
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    }[];
    actionInfo: {
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}

// IM 用户相关数据
export interface ImUserInfo {
    name?: string;
    avatar?: string;
    isLogin?: boolean;
    link?: string;
    // 是否强制登录
    shouldLogin?: boolean;
}

// 医生相关数据
export interface ImDoctorInfo {
    name?: string;
    expert_id?: string;
    head_portrait?: string;
}

export interface ImchatTipsProps {
    content: string | number;
    type: 'countDown' | 'text' | 'round';
    status: number;
}

export interface ImChatTipsListProps {
    skuId: string;
    status: number;
    statusText: string;
    skuFormType: number;
    skuFormTypeText: string;
    chatTips: ImchatTipsProps[];
}

export interface FormItemPreviewProps {
    title: string;
    description: string;
    buttonText?: string;
    showButton?: boolean;
    hideButton?: boolean;
    onConfirm?: CommonEventFunction;
}

export interface FormCardStyle {
    cardName?: number;
    oneByOne?: boolean;
}

export interface FormCardType {
    data: object;
    cardName: string;
}

export interface InfoCollectorFormProps {
    data: {
        cardStyle: FormCardStyle;
        content: {
            cardList: ICardProps<CardInstanceCardDataType>[];
            needLogin?: boolean;
        };
    };
    msgId: string;
    msgKey?: string;
    msgData: IMsgProps;
}

export interface ImStatusData {
    chatTips: ImchatTipsProps[];
    chatTipsList: ImChatTipsListProps[];
    chatPageTitle: string;
    chatProgress: {
        status: 0 | 1;
        text: string;
    }[];
}

// --------------------------- 诊前 ---------------------------

// cui 诊前通用请求参数
export interface CuiTriageCommonReqParams {
    qid: string;
    traceId?: traceId;
    isDirected?: string;
    riskInfo?: unknown;
    noFirstLeave?: boolean;
}

export interface AiParamsInteraction {
    intent: string;
    entities: {
        [key in string]: string;
    };
}

export interface SendMsgParamsProps {
    value?: string;
    content: string;
    isHidden?: boolean;
    blobValue?: string;
    optionsKey?: string;
    needMockMsg?: boolean;
    needMockBInputing?: boolean;
    aiParams?: AiParamsInteraction;
    type?: keyof typeof SendMsgContentTypeEnum;
}

export interface SendTriageMsgForkParams extends CuiTriageCommonReqParams {
    msgKey: string;
    content: string;
    contentType: SendMsgContentType;
    qid: Qid;
}

export interface SendTriageMsgParams {
    isDirected: string;
    msg: {
        msgId: string;
        payload: AiParamsInteraction[];
        preMsgId: MsgId;
    };
    chatData: TriageChatData;
    // 前端卡片渲染能力
    cardSupportedVersion: {
        cardID: MSG_CARDID_TYPE;
        version: number;
    }[];
}

export interface SendTriageMsgResponse {
    msgIds: MsgId[];
    servicerMsgIds: MsgId[];
    msgKey: string;
    msgData: {
        [key in MsgId]: IMsgProps;
    };
    toolData?: ImToolData;
    fwData?: TriageFwData;
    adsData?: TriageAdsData;
    inputData?: ImInputData;
    statusData?: ImStatusData;
    keyInfo?: TriageKeyInfo;
    chatData?: TriageChatData;
    sessionData?: TriageSessionData;
    imActionData?: TriageImActionData;
    callbackAction?: TriageCallbackAction[];
}

export interface GetTriageMsgListParams extends CuiTriageCommonReqParams {
    page: {
        msgCreateTime?: string;
        size: number;
        viewType: ViewType;
    };
    change_zhusu?: number;
    doc_id?: string;
    qid: Qid;
    noFirstLeave?: boolean;
    // 前端卡片渲染能力
    cardSupportedVersion: {
        cardID: MSG_CARDID_TYPE;
        version: number;
    }[];
}

export interface TriageCallbackAction {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

export interface GetTriageMsgListResponse {
    msgIds: MsgId[];
    userMsgIds: MsgId[];
    servicerMsgIds: MsgId[];
    chatData: TriageChatData;
    msgData: {
        [key in MsgId]: IMsgProps;
    };
    toolData?: ImToolData;
    fwData?: TriageFwData;
    keyInfo?: TriageKeyInfo;
    adsData?: TriageAdsData;
    inputData?: ImInputData;
    actionData?: TriageActionData; // 优先级较高
    statusData?: TriageStatusData;
    imActionData?: TriageImActionData;
    sessionData?: TriageSessionData;
    callbackAction?: TriageCallbackAction[];
}

export interface TriageSessionData {
    [key: string]: unknown; // 添加索引签名
    control?: {
        __delete__?: boolean;
        gotoWxLogin?: number;
        loginPlanTrigger?: string;
        gotoWxLoginAction?: {
            interaction: InteractionType;
            interactionInfo: InteractionInfo;
        };
    };
}

export type GetTriageCoreDataParams = CuiTriageCommonReqParams;

export interface GetTriageCoreDataResponse {
    toolData: ImToolData;
    adsData: TriageAdsData;
    statusData: TriageStatusData;
    msgIds: MsgId[];
    msgData: {
        [key in MsgId]: IMsgProps;
    };
}

export type SceneType = 'direct' | 'undirect';
export type GetTriageOtherDataParams = CuiTriageCommonReqParams;

export interface GetTriageOtherDataResponse {
    adsData?: TriageAdsData;
    fwData?: TriageFwData;
    keyInfo: TriageKeyInfo;
    anticipationPopup?: AnticipationPopup;
    // actiton
    actionData?: {
        [k in string]: TriageActionData | IDoctorVisitPopupProps | QuickReAskDoctor;
    };
}

export interface TriageActionData {
    interaction?: InteractionType;
    interactionInfo?: InteractionInfo;
    actionType?: string;
}

export interface TriageImActionData {
    patientAuthorize?: PatientAuthorizeInfo;
    toast?: {
        title?: string;
        icon?: string;
    };
}

export interface PatientAuthorizeInfo {
    avatar?: string;
    name?: string;
    realName?: string;
    genderText?: string;
    ageText?: string;
    actionList?: ActionInfo[];
    actionClose?: ActionClose;
}

interface ActionClose {
    text: string;
    interaction: string;
    interactionInfo: InteractionInfo;
}

interface ActionInfo {
    value: string;
    type?: string;
    interaction: string;
    interactionInfo: InteractionInfo;
}

interface Params {
    aiParams?: AiParamsInteraction;
}

export interface AnticipationPopup {
    expertName?: string;
    expertPic?: string;
    monthOrderCnt?: string;
    monthNonReplyCnt?: string;
    monthReplySpeedMedium?: string;
}

export interface IDoctorVisitPopupProps {
    expertName: string;
    title: string;
    btns: {
        confirm: {
            text: string;
        };
        cancel: {
            text: string;
        };
    };
    pointType: string;
    rules: {
        content: {
            title: string;
            value: {
                title: string;
                desc: string[];
            }[];
        }[];
        warn: string;
    };
}

// 诊前快捷追问弹窗
export interface QuickReAskDoctor {
    title: string;
    description: string;
    doctorInfo: DoctorInfo;
    skuInfo: SkuInfo;
    btnsInfo: BtnsInfo[];
}

interface DoctorInfo {
    expertName: string;
    docId: string;
    isSameExpert: boolean;
    expertPic: string;
    expertDepartment: string;
    expertLevel: string;
    expertHospital: string;
    goodAt: string;
    hospitalTags: HospitalTag[];
}

interface HospitalTag {
    key: string;
    text: string;
}

interface SkuInfo {
    list: List[];
}

interface BtnsInfo {
    text: string;
    type: string;
    interaction: InteractionType;
    interactionInfo?: InteractionInfo;
}

interface List {
    title: string;
    subTitle: string;
    description: string;
    rightDesc: string;
    skuId: string;
    formType: number;
    referencePrice: number;
    promotionPrice: number;
    salePrice: number;
    totalPrice: number;
    discount: number;
    couponReductionPrice: number;
    promotionReductionPrice: number;
    selectedCouponId: number;
    btn: BtnsInfo;
}

export type TriageActionInfo = {
    [key in string]: TriageActionData;
};

export type TriageImActionInfo = {
    [key in string]: TriageImActionData;
};

export enum CouponDialogEnum {
    '服务推荐' = 'coupon_recommend',
    '免费问诊' = 'coupon_consult',
    '新人券' = 'coupon_newuser',
    '天降红包' = 'coupon_realtime',
    '定向复诊' = 'coupon_direct',
    '医生信息' = 'doctor_modal',
    '自测' = 'zice',
    '健康助手' = 'jkbot',
    '平台保障' = 'baozhang',
    '好评' = 'doctor_comment'
}

export type CouponDialogType = `${CouponDialogEnum}`;

export interface newCustomerCouponInfoProps {
    title?: string;
    subTitle?: string;
    fullPrice?: number;
    maxprice?: number;
    startTime?: string;
    endTime?: string;
    isLogin?: boolean;
    currentSend?: boolean;
    salePrice?: number;
    popType?: string;
    trigger?: string;
    buttonElement?: {
        value?: string;
        interaction: InteractionType;
        interactionInfo?: InteractionInfo;
    };
    mini?: {
        title?: string;
        salePrice?: number;
    };
}

export interface DoctoreItem {
    hospital?: string;
    img?: string;
    level?: string;
    name?: string;
}

export interface couponInfoItemProps {
    title?: string;
    subTitle?: string;
    worth?: string;
    afterPrice?: string;
    originPrice?: string;
    maxPriceStr?: string;
    limitStr?: string;
    condition?: string;
    period?: string;
}

export interface richItem {
    value?: string;
    type?: string;
}

export interface FooterItem {
    value: string;
    type: string;
    interaction: InteractionType;
}

export interface questionItemProps {
    title?: string;
    list?: [
        {
            text?: string;
        }
    ];
}

export interface ziceDataProps {
    title?: string;
    imgUrl?: string;
    description?: richItem[];
    labels?: string[];
    questions?: questionItemProps[];
    carousels?: richItem[];
    actionInfo?: {
        interaction: InteractionType;
        interactionInfo?: InteractionInfo;
    };
}

export interface commentTagItem {
    tagText?: string;
    tagNum?: number;
}
export interface currentDoctorData {
    userInfo?: {
        isOnline?: boolean;
        avatar?: string;
    };
    commentTags?: commentTagItem[];
    pennantCount?: number;
}

export interface popCouponProps {
    type: CouponDialogType;
    title?: string;
    titleRich?: richItem[];
    subTitle?: string;
    subTitleRich?: richItem[];
    adsImg?: string;
    wxUrl?: string;
    doctorData?: {
        questionNum?: string;
        doctorList?: DoctoreItem[];
        currentDoctor?: currentDoctorData;
    };
    couponData?: couponInfoItemProps;
    ziceData?: ziceDataProps;
    buttonElement?: {
        value?: string;
        interaction: InteractionType;
        interactionInfo?: InteractionInfo;
    };
    mini?: {
        title?: string;
        salePrice?: number;
    };
    trigger?: string;
    popType?: string;
    isLogin?: boolean;
    couponInfos?: couponInfoItemProps;
    footer?: FooterItem[];
    forceShow?: boolean; // 是否强制展示挽留弹窗（不管是否是首次）
}

export interface TriageAdsData {
    popData?: popCouponProps;
    popCoupon?: popCouponProps;
    newCustomerCouponInfo?: newCustomerCouponInfoProps;
    newPopCouponInfo?: newCustomerCouponInfoProps;
    wxData?: {publicGuideData: ImWxCodeData};
    topData?: {whiteTips?: WhiteTipsProps};
    closeDialog?: (isStay: boolean) => void;
    topBanner?: ITopBanner;
}

export interface TriageStatusData {
    topTips: INavTopTips;
}

export interface TriageKeyInfo {
    userData: ImUserInfo;
    doctorData?: ImDoctorInfo;
    control?: {
        gotoWxLogin?: number;
        loginPlanTrigger?: string;
    };
}

export interface TriageChatData {
    qid: string;
    chatType: string;
    chatId: string;
    sessionId: string;
    expertId: string;
    cityCode: string;
    provCode: string;
    content?: string; // 主诉信息
}

export interface TriageFwData {
    marqueeTips: string[];
}

export interface CalcCouponsReqParams {
    qid: Qid;
    msg: {
        msgId: MsgId;
    };
    chatData: TriageChatData;
    skuCouponMap: {
        recordId: number;
        skuId: string;
    }[];
}

export interface CalcCouponsReqResponse {
    msgIds: MsgId[];
    userMsgIds: MsgId[];
    servicerMsgIds: MsgId[];
    chatData: TriageChatData;
    msgData: {
        [key in MsgId]: IMsgProps;
    };
    inputData?: ImInputData;
}

export interface GetGridCardRedDotResponse {
    redDotShow: {
        type: string;
        isShow: boolean;
        msgNum: number;
    };
}

export interface TransTextResponse {
    text?: string;
}

export interface ServiceSkuProps {
    title?: string;
    subTitle?: string;
    skuId?: string;
    skuCode?: string;
    salePrice?: number;
    referencePrice?: number;
    // 原价
    promotionPrice?: number;
    btn?: {
        interaction?: InteractionType;
        interactionInfo?: InteractionInfo;
        text?: string;
    };
}

export interface SpecifiedExpertProps {
    avatar?: string;
    btn?: {
        interaction?: InteractionType;
        interactionInfo?: InteractionInfo;
        text?: string;
    };
    subTitle?: string;
    title?: string;
}

export interface GetUpgradeServiceResponse {
    specifiedExpert?: SpecifiedExpertProps;
    skus?: ServiceSkuProps[];
}

export interface GetFrontQuesitionResponse {
    list: string[];
}

export interface GetFrontQuesitionProps {
    editAll: number;
    content: string[];
}

export interface GetTriageCouponParams {
    trigger: string;
    version?: number;
    loId?: LoId;
    soId?: SoId;
    expertId?: string;
    isDirected?: string;
    doc_id?: string;
}

export interface GetTriageCouponResponse {
    toast: string;
    isLogin: boolean;
    couponInfos: CouponInfo[];
    buttonElement?: {
        value?: string;
        interaction: InteractionType;
        interactionInfo?: InteractionInfo;
    };
    mini?: {
        title?: string;
        salePrice?: number;
    };
    popType?: string;
    trigger?: string;
}

//  GetHomeActionListRes 首页导航信息响应体
export interface GetHomeActionListRes {
    location: string;
    goldLocation: goldLocation[];
    goldLocationNew?: goldLocation[];
    aiServices: IAiServices;
    quickNavigation: IQuickNavigation[];
    banner: {
        isAutoPlay: boolean;
        autoPlayInterval: number;
        list: banner[];
    };
    services: IServices;
    kepu: IKepuRes;
    zice: IZiceRes;
}

// IZiceRes 健康自测响应参数
export interface IZiceRes {
    title: string;
    moreInfo: IBottom;
    tips: {
        icon: string;
        interaction: InteractionType;
        interactionInfo: {
            url: string;
        };
        needLogin: true;
        value: string;
    };
    ziceList: IZiceList[];
}

// IZiceList 健康自测列表响应参数
export interface IZiceList {
    img: string;
    interaction: InteractionType;
    interactionInfo: {
        url: string;
    };
    key: string;
    subtitle: string;
    title: string;
}

// IKepuRes  健康医典响应参数
export interface IKepuRes {
    title: string;
    moduleList: {
        list: IKepuListRes[];
    };
}

// IKepuListRes 健康医典list响应参数
export interface IKepuListRes {
    backgroundUrl: string;
    icon: string;
    title: string;
    interaction: InteractionType;
    interactionInfo: {
        url: string;
    };
}

// IServices 专家问诊挂号响应参数
export interface IServices {
    backgroundUrl: string;
    subTitle: string;
    title: string;
    moduleList: RegistrationInfo[];
}

// RegistrationInfo 专家问诊挂号tab模块
export interface RegistrationInfo {
    title: string;
    subtitle: string;
    type: string;
    list: RegistrationDetailInfo[];
    bottom: IBottom;
}
// RegistrationInfo 专家问诊挂号tab具体模块展示列表数据
export interface RegistrationDetailInfo {
    content: string;
    title: string;
    interaction: InteractionType;
    interactionInfo?: InteractionInfo;
    value?: string;
    icon?: string;
}

// action
export interface IBottom {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
    value?: string;
    icon?: string;
    title?: string;
}

// 微信小程序首页医生列表 响应参数
export interface GetHomePageHospRes {
    hospital: IHospital;
}

//  首页医生列表具体结构
export interface IHospital {
    title: string;
    subTitle: string;
    moduleList: {
        type: string;
        title: string;
        subTitle: string;
        list: IBottom[];
        expertList: IExpertListRes[];
    };
}

// 首页医生具体信息
export interface IExpertListRes extends IBottom {
    expertId: string;
    docId: string;
    coreId: string;
    expertName: string;
    expertPic: string;
    expertLevel: string;
    expertHospital: string;
    expertDepartment: string;
    hospitalLevel: string;
    localTag: string;
}

// goldLocation 首页金刚位 list
export interface goldLocation {
    value: string;
    desc: string;
    backgroundUrl: string;
    renderType: number; // 导航背景类型 1: 大块导航栏；2：小块导航栏
    interaction: InteractionType; // 跳换类型， openLink 地址， openOtherApp 小程序类型
    needLogin: boolean;
    interactionInfo: {
        url: string;
        params?: object;
        appName?: string;
    };
}

export interface IModuleList {
    title?: string;
    subTitle?: string;
    renderType?: number;
    needLogin?: boolean;
    name?: string;
    backgroundUrl?: string;
    interaction?: InteractionType;
    interactionInfo?: InteractionInfo;
}

// aiServices ai服务相关
export interface IAiServices {
    title?: string;
    subtitle?: string;
    moduleList?: IModuleList[];
}

// quickNavigation 首页新增金刚位字段
export interface IQuickNavigation {
    title: string;
    icon: string;
    needLogin: boolean;
    interaction: InteractionType; // 跳换类型， openLink 地址， openOtherApp 小程序类型
    interactionInfo: {
        url: string;
        params?: object;
        appName?: string;
    };
}
// banner 首页banner
export interface banner {
    backgroundUrl: string;
    interaction: InteractionType; // 跳换类型， openLink 地址， openOtherApp 小程序类型
    needLogin: boolean;
    interactionInfo: {
        url: string;
        params?: object;
        appName?: string;
    };
}

//  GetHomeNoticeRes 首页通知消息响应体
export interface GetHomeNoticeRes {
    reddot: number;
    noticeList: INoticeProps[];
}

//  noticeList 首页通知 list 响应体
export interface INoticeProps {
    icon?: string;
    title: string;
    desc?: string;
    interaction: InteractionType;
    interactionInfo: {
        url: string;
        params?: object;
        appName?: string;
    };
    type: number;
    relationId: string;
    clickNotExpire: number;
}

export interface ProcessMediaReqParams {
    loId: LoId;
    mediaInfos: ProcessMediaInfo[];
}

export interface ProcessMediaInfo {
    msgKey: string;
    fileKey: string;
    contentType: number;
    coverHandle: string;
    videoHandle: string;
}

export interface ProcessMediaReqResp {
    mediaInfos: {
        [k in string]: {
            needCover: 0 | 1;
            needVideo: 0 | 1;
        };
    };
}

export interface GetGlobalNoticeReqParamsType {
    pagePath: string; // 当前页面路径；
    globalConf: 'videoCallData'[];
}

type MaterialTrigger = 'floatingBall';

type WenzhenPageConfModuleName = 'fePlanMaterial';

export interface MaterialFollowingBallData {
    content: {
        data: {
            list: {
                skuInfo: {
                    title: string;
                };
                soId: string;
                statusText: string;
            }[];
            actionInfo: {
                interactionInfo: InteractionInfo;
                interaction: InteractionType;
            };
            title: string;
            type: 'orderFloatingBall';
        };
    };
    trigger: 'floatingBall';
}

export interface PatientItem {
    contactId: string;
    name: string;
    sex?: string;
    age: string;
    birthday?: string;
    cardType?: string;
    idcardFormatStar?: string;
    sscardFormatStar?: string;
    province?: string;
    city?: string;
    area?: string;
    address?: string;
    mobileFormatStar?: string;
    marriage?: string;
    relationship?: string;
    createTime?: string;
    updateTime?: string;
    createTimeStamp?: number;
    updateTimeStamp?: number;
    improved?: number;
    improvedDesc?: string;
    isCertified?: number;
    isCertifiedDesc?: string;
    medicalHistory?: string;
    allergyHistory?: string;
    familyHistory?: string;
    gestation?: number;
    liver?: number;
    kidney?: number;
    weight?: number;
    month?: string;
    haveHypertension?: number;
    haveDiabetes?: number;
}
export interface TopFlowWindow {
    videoCallData?: VideoCallData;
}
export interface VideoCallData {
    room: {
        name: string;
    };
    caller: {
        name: string;
        avatar: string;
    };
    btns: {
        icon: string;
        value: string;
        actionType: string;
        interactionInfo: {
            url: string;
        };
    }[];
}

export interface GlobalNoticeDataType {
    global: {
        videoCallData: VideoCallData;
    };
    control: {
        intervalTime: number; // 单位：毫秒；
        enable: boolean; // 是否继续轮询；
    };
}

export interface GlobalModelWindow {
    title: string;
    content: string;
    btns: {
        value: string;
        interaction: string;
        interactionInfo: {
            url: string;
            appName: string;
            params: {
                loId: string;
            };
        };
    }[];
}

export interface AnswerVideoCallResponse {
    room: {
        name: string;
        token: string;
        status: number;
        teamMemberCount: number;
        remainingCallTime: number;
        endReason?: {
            code: number;
            msg: string;
        };
    };
    user: {
        rtcUID: number;
        name: string;
        avatar: string;
        role?: number;
        status: number;
    };
    memberList: RTCUser[];
}

export interface videoCallRoomInfo {
    room: {
        name: string;
        status: number;
        endReason?: {
            code: number;
            msg: string;
        };
    };
    memberList: RTCUser[];
}

export interface RTCUser {
    rtcUID: number;
    name?: string;
    avatar?: string;
    status: number;
    endReason?: {
        code: number;
        msg: string;
    };
    role?: number;
}

export interface GetZhenqianGetAdsDataResponse {
    adsData?: ImAdsData;
}

export interface AdsDataReqParams extends InteractionInfo {
    qid: Qid;
}

export interface CalcSkuPriceListParams {
    qid: Qid;
    entrance: string;
    skuIds: string[];
    couponSelect: number;
}

export interface CalcSkuPriceListResponse {
    ResultMap: {
        [k in `2000${string}`]: {
            salePrice: number; // 商品原价
            promotionReductionPrice: number; // 平台活动减价
            couponReductionPrice: number; // 优惠券减价
            promotionPrice: number; // 最终优惠价
            selectedCouponId: number; // 选中优惠券id(用户券rid)
            errcode: string;
            errmsg: string;
            totalReductionPrice?: number; // 优惠总金额
        };
    };
}

export interface ITopBanner {
    type: string;
    data?: INoticeBar;
}
// 医生团队授权关系-通用请求参数
export interface TeamPatientBizReqParams {
    teamId?: number;
    patientId?: string;
    authorizeMedicalRecords?: number;
}

export interface MedicalRecordsTitle {
    type: string;
    value: string;
}
export interface MedicalRecords {
    isAuthorize: number;
    title: MedicalRecordsTitle[];
    subTitle: MedicalRecordsTitle[];
}
