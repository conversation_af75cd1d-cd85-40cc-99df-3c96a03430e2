import {IM_FEATURES_TYPE} from '@common/constants';
import {PopupInfo} from '@baidu/wz-taro-tools-core/im-drugs-tips/im-drugs-tips.shared';
import {WX_APP_KEY_MAP} from '@common/constants/common';
import {type FeedbackPopupDataProps} from '@baidu/vita-ui-cards-common/ImFlow';

import {AiParamsInteraction} from './cui';

export type {MsgInstanceData} from '../store/triageStreamAtom/index.type';
export type {ICardProps} from './msg.d';

// eslint-disable-next-line no-shadow
export enum InteractionEnum {
    '请求' = 'request',
    '打开链接' = 'openLink',
    '底部弹窗' = 'popup',
    '拉起支付' = 'payment',
    '关闭' = 'close',
    '全局 modal' = 'modal',
    '跳过 ai 问题' = 'skipAi',
    '发送ubc日志' = 'sendUbc',
    '重新加载' = 'reload',
    '打开pdf' = 'openPdf',
    '打开第三方小程序' = 'openOtherApp',
    '打开小程序' = 'openMiniApp',
    '请求后打开微信小程序' = 'requestWexinJump',
    '拨打电话' = 'callPhone',
    '异步请求' = 'asyncRequest',
    'toast弹层' = 'toast',
    '渲染物料数据' = 'renderPlanMaterial',
    'ai历史记录弹窗' = 'historyMsgPop'
}

export type InteractionType = `${InteractionEnum}`;

export interface IKVProps {
    type?: string;
    value?: string;
}

export interface contentItem {
    type?: string;
    value?: string;
    content?: IKVProps[];
}

export interface IBtnItem {
    value?: string;
    disabled?: boolean;
    interaction?: InteractionType;
    interactionInfo?: InteractionInfo;
}
export interface ModalContentProps {
    content?: contentItem[];
    buttonList?: IBtnItem[];
    title?: string;
    contentList?: contentItem[];
}

export interface IContentProps {
    btnInfo?: IBtnItem[];
    description?: IKVProps[];
    title?: string;
}

export interface IPopupInfoProps {
    type?: string;
    content?: IContentProps;
}

interface FeedbackPopupData {
    input: Input;
    title: string;
    content: Content[];
    submitBtn: {
        value: string;
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}

interface Content {
    items: Item[];
    subTitle: string;
}

interface Item {
    value: string;
}

interface Input {
    placeholder: string;
    value: string;
}

export interface InteractionInfo {
    intent?: string;
    url?: string;
    sceneType?: string;
    method?: 'GET' | 'POST';
    contentType?: string;
    wxPayConfig?: unknown;
    wxPayInfo?: {[key: string]: string};
    inlinePaySign?: string;
    type?: string;
    version?: string;
    appName?: keyof typeof WX_APP_KEY_MAP;
    params?: {
        [key in string]: string | number | AiParamsInteraction | unknown | PopupInfo;
    };
    fail?: () => void;
    success?: () => void;
    modalContent?: ModalContentProps;
    popupInfo?: IPopupInfoProps | FeedbackPopupDataProps;
}

// Im 工具数据
export interface ImFeatureTool {
    type: IM_FEATURES_TYPE;
    optionsKey?: string;
    aiParams?: AiParamsInteraction;
    clickDisable?: 0 | 1;
    redDot?: number;
    actionInfo?: {
        interaction?: InteractionType;
        interactionInfo?: InteractionInfo;
    };
}

export interface ImServicerData {
    introduce: string;
    url: string;
    expertAvatar: string[];
}
