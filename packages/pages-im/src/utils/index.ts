/* eslint-disable */
// @ts-nocheck
/**
 * @file 公共函数封装
 * <AUTHOR>
 */
import Taro, {getSystemInfoSync, canIUse, setStorageSync} from '@tarojs/taro';
import {cloneDeep} from 'lodash-es';
import dayjs from 'dayjs';
import {getCommonSysInfo, getSystemInfo} from './taro';
import {cookie4h5 as cookie} from './basicAbility/cookie';
import {
    isUrlEncoded,
    getCurrentUrlQuery,
    isObject,
    versionCompare,
    getCurrentSystem
} from '@baidu/vita-utils-shared';
import {IMsgProps} from './typings/msg.d';
import {UBC_PAGE_MAP} from '../constants/index';
import {WZ_NOT_PAGE_RENDER} from '../constants/storageEnv';

export interface IfObject {
    [propName: string]: any;
}

// 控制页面渲染
const controlPageRender = () => {
    try {
        setStorageSync(WZ_NOT_PAGE_RENDER, 1);
    } catch (error) {}
};

/**
 * 从本地相册选择图片或使用相机拍照。(由于taro不支持h5端相机与相册分开调用，又因为跨端文件的特性，所以小程序端也要重新个)
 * @param {Object} object 参数
 * @param {string[]} [object.sourceType=['album', 'camera']] 选择图片的来源（h5端未实现）
 * @param {number} [object.count=9] 最多可以选择的图片张数
 */
export const chooseImage = options => {
    return new Promise((resolve, reject) => {
        controlPageRender();
        Taro.chooseImage({
            success: resolve,
            fail: reject,
            ...options
        });
    });
};

/**
 *
 * @description 删除元素中的指定元素并返回新对象
 * @param obj
 * @param keys
 * @returns
 */
export function delObjectKey(obj: any, keys: any[]) {
    const res = Object.assign({}, obj);
    keys.forEach(i => {
        delete res[i];
    });

    return res;
}

/**
 * @Description: await 函数调用异常捕获
 * @param {Promise} promise
 * @return {*}
 */
export function awaitWrap<T, U = any>(promise: Promise<T>): Promise<[U | null, T | null]> {
    return promise.then<[null, T]>((data: T) => [null, data]).catch<[U, null]>(err => [err, null]);
}

/**
 * @Description: px转rem
 * @param {size} number
 * @return {*}
 */
export function pxTransform(size: number): string {
    if (!size) return '';
    const designWidth = 1242;
    const deviceRatio = {
        1242: 750 / 1242
    };

    return process.env.TARO_ENV === 'h5'
        ? `${((size * deviceRatio[designWidth]) / 100) * 2.10526315}rem`
        : `${size * deviceRatio[designWidth]}rpx`;
}

/**
 * @Description 对象转字符串
 */
export function objectToString(style: object | string): string {
    if (style && typeof style === 'object') {
        let styleStr = '';
        Object.keys(style).forEach(key => {
            const lowerCaseKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
            styleStr += `${lowerCaseKey}:${style[key]};`;
        });

        return styleStr;
    } else if (style && typeof style === 'string') {
        return style;
    }

    return '';
}

/**
 * @Description 合并style
 * @param {Object|String} style1
 * @param {Object|String} style2
 * @returns {String}
 */
export function mergeStyle(
    style1: object | string | undefined,
    style2: object | string | undefined
): object | string {
    if (style1 && typeof style1 === 'object' && style2 && typeof style2 === 'object') {
        return Object.assign({}, style1, style2);
    }

    return !style1
        ? style2 || {}
        : !style2
          ? style1 || {}
          : objectToString(style1) + objectToString(style2);
}

// 判断是否是iphoneX系列
export const validateIsIphoneX = () => {
    const systemInfo = getSystemInfoSync();
    if (!(systemInfo instanceof Error)) {
        // 判断是否为iphoneX以及以上系列 x及以上的异形屏top为44，非异形屏为20
        // 当前理论上基于刘海来判断 不走下面基于机型具体型号判断的逻辑
        if (
            systemInfo.platform === 'ios' &&
            systemInfo.safeArea &&
            systemInfo.safeArea.top &&
            systemInfo.safeArea.top > 20
        ) {
            return true;
        }
        if (
            (systemInfo.model &&
                (systemInfo.model.indexOf('iPhone X') > -1 ||
                    systemInfo.model.indexOf('iPhone 11') > -1 ||
                    systemInfo.model.indexOf('iPhone 12') > -1 ||
                    systemInfo.model.indexOf('iPhone 13') > -1)) ||
            (systemInfo.model === 'iPhone Simulator <x86-64>' && systemInfo.screenWidth === 375)
        ) {
            return true;
        }

        return false;
    }
};

export const validateIsIOSWechat = () => {
    const systemInfo = getSystemInfoSync();
    let android = false;
    if (
        (systemInfo?.system && systemInfo?.system?.includes('Android')) ||
        systemInfo?.system?.includes('android')
    ) {
        android = true;
    }
    return process.env.TARO_ENV === 'weapp' && !android;
};

export const validateIsAndrodSwan = () => {
    const systemInfo = getSystemInfoSync();
    let android = false;
    if (
        (systemInfo?.system && systemInfo?.system?.includes('Android')) ||
        systemInfo?.system?.includes('android')
    ) {
        android = true;
    }
    return process.env.TARO_ENV === 'swan' && android;
};

export const validateIsAndrodWechat = () => {
    const systemInfo = getSystemInfoSync();
    let android = false;
    if (
        (systemInfo?.system && systemInfo?.system?.includes('Android')) ||
        systemInfo?.system?.includes('android')
    ) {
        android = true;
    }
    return process.env.TARO_ENV === 'weapp' && android;
};

export const validateIsAndrodH5 = () => {
    const systemInfo = getSystemInfoSync();
    let android = false;
    if (
        (systemInfo?.system && systemInfo?.system?.includes('Android')) ||
        systemInfo?.system?.includes('android')
    ) {
        android = true;
    }
    return process.env.TARO_ENV === 'h5' && android;
};

/**
 * @Description Promise Taro api
 * @param {String} api
 * @param {Object} data
 * @returns {Promise}
 */

export const taroPromise = async (api: string, obj = {}): Promise<[Error | null, any]> => {
    const [error, data]: [Error | null, any] = (await awaitWrap(Taro[api](obj))) as [
        Error | null,
        any
    ];

    return [error, data];
};

export function checkType(data: any): string {
    const type = Object.prototype.toString.call(data);

    return type.slice(8, -1).toLowerCase();
}

/**
 * 检测传入的数据是否为空值
 * 空值包括：'' / [] / {} / Map{0} / Set{0} / null / undefined
 * @param data 待检测的数据
 */
export const isEmpty = (data: any) => {
    const dataType = checkType(data);
    switch (dataType) {
        case 'array':
            return !data.length;
        case 'object':
            return !Object.keys(data).length;
        case 'map':
        case 'set':
            return !data.size;
        case 'boolean':
        case 'number':
        case 'symbol':
        case 'function':
            return false;
        default:
            return !data;
    }
};

/**
 * sleep 函数
 * @param {number} delay 等待时间
 */
export const sleep = delay => {
    return new Promise(resolve => {
        setTimeout(resolve, delay);
    });
};

/**
 *
 * @param 参数对象
 * @returns url参数字符串
 */
export const objToUrlParams = obj => {
    let urlParamsStr = '';
    for (const key in obj) {
        /* eslint-disable-next-line */
        if (obj.hasOwnProperty(key) && obj[key]) {
            urlParamsStr = `${urlParamsStr}&${key}=${obj[key]}`;
        }
    }
    urlParamsStr.length > 0 && (urlParamsStr = urlParamsStr.replace(/\&/, ''));

    return urlParamsStr;
};

/**
 * @param 要去重的数据
 * @returns 去重后的数组
 */
export const uniqueList = (arr: IMsgProps[]): IMsgProps[] => {
    const pool = new Set();

    return arr.filter(item => {
        const key = `${item.msgId || item.msgKey}`;

        return !pool.has(key) && pool.add(key);
    });
};

/**
 * 基于msgKey获取数组索引
 */

export const getMsgIndexFromMsgKey = (msgList: IMsgProps[], msgKey: string): number => {
    const msgIndex = msgList.findIndex(item => {
        return item.msgKey === msgKey;
    });

    return msgIndex;
};

export const getCurrentPathName = () => {
    const pages = Taro.getCurrentPages();

    return pages[pages.length - 1].route;
};

export const fitLowSwanSystem = (fitVersion: string) => {
    const systemInfo = getSystemInfo();
    const {version = '13.58.0.0'} = systemInfo;
    if (versionCompare(version, fitVersion) > -1 && validateIsAndrodSwan()) {
        return true;
    }
    return false;
};

// 判断是否可以使用属性or方法
// 这里的方法我临时用来判断是否是 > 13.58版本以上了，所以不通用！！
export const isCanUseKey = (keys?: Array<string> | string) => {
    if (typeof keys === 'string') {
        return canIUse(keys) || fitLowSwanSystem('13.58.0.0');
    }
    if (!Array.isArray(keys)) {
        return false || fitLowSwanSystem('13.58.0.0');
    }

    return keys?.every(item => canIUse(item)) || fitLowSwanSystem('13.58.0.0');
};

/**
 * 埋点用，页面名称
 */
export const getUbcPage = (path?: string): string => {
    const pathName = path || getCurrentPathName();
    const pageUBC = UBC_PAGE_MAP[pathName!];
    // 找不到映射就返回页面路径

    return pageUBC || getCurrentPathName();
};

/**
 * 获取H5当前的协议+域名
 */
export function getPageBaseUrl() {
    let baseURL = '';
    if (!window.location.origin) {
        // 兼容IE，IE11版本下location.origin为undefined
        baseURL = `${window.location.protocol}//${window.location.hostname}${
            window.location.port ? `:${window.location.port}` : ''
        }`;
    } else {
        baseURL = window.location.origin;
    }

    return baseURL;
}

/**
 * 将数组n等分
 * @param {Array} 被等分的数组
 * @param {Number} 分为n 分
 */
export const splitArray = (arr, n) => {
    const rest = arr.length % n;
    let restUsed = rest;
    const partLength = Math.floor(arr.length / n);
    const result = [];
    for (let i = 0; i < arr.length; i += partLength) {
        let end = partLength + i;
        let add = false;
        if (rest !== 0 && restUsed) {
            end++;
            restUsed--;
            add = true;
        }
        result.push(arr.slice(i, end) as never);

        if (add) {
            i++;
        }
    }

    return result;
};

/**
 *
 * @description 获取url上需要透传的参数
 * @returns
 */
export const getNeedTransParams = () => {
    const commonParams = {};
    const query = getCurrentUrlQuery();
    const needTransParamsKeys: string[] = [
        'ref',
        'sf_ref',
        'user_card_id',
        'addrId',
        'fromPush',
        'bd_vid',
        'from'
    ];

    needTransParamsKeys.forEach(i => {
        query[i] && (commonParams[i] = query[i]);
    });

    return commonParams;
};

// h5复制
export const h5CopyText = text => {
    const textarea = document.createElement('textarea');
    const currentFocus = document.activeElement;
    document.body.appendChild(textarea);
    textarea.value = text;
    textarea.focus();
    if (textarea.setSelectionRange) {
        textarea.setSelectionRange(0, textarea.value.length);
    } else {
        textarea.select();
    }
    try {
        document.execCommand('copy');
    } catch (err) {}
    document.body.removeChild(textarea);
    (currentFocus as HTMLElement).focus();
};

/**
 * 秒数转剩余时间
 *
 * @param {number} sec 秒数
 * @return {string} 剩余时间
 */
// eslint-disable-next-line no-unused-vars
export const fmtCountDown = (sec: number): string => {
    let second: string | number = Math.floor(sec);
    let hour: string | number = Math.floor(second / 3600);
    hour < 10 && (hour = `0${hour}`);
    second %= 3600;
    let minute: string | number = Math.floor(second / 60);
    minute < 10 && (minute = `0${minute}`);
    second %= 60;
    second < 10 && (second = `0${second}`);

    return `${hour}:${minute}:${second}`;
};

export function decodeObj(data?: object | string): any {
    if (data && typeof data === 'object') {
        const deData = {};
        Object.keys(data).forEach(key => {
            deData[decodeURIComponent(key)] = decodeURIComponent(data[key]);
        });

        return deData;
    }

    return {};
}

/**
 * 处理参数， 主要是为了兼容微信小程序
 * @param data  query，页面上参数
 * @returns
 */
export function decodeQueryObj(data?: object | string): any {
    if (data && typeof data === 'object') {
        if (process.env.TARO_ENV === 'swan') {
            // 百度小程序会解码一次, 因此直接返回
            return data;
        }

        return decodeObj(data);
    }

    return {};
}

/**
 * 对象转url query
 *
 * @param  {object} obj- 输入对象
 * @returns {string} 输出query
 * @example
 */
export function queryString(obj, isEncode = true) {
    if (!obj) {
        return '';
    }

    const params = Object.keys(obj)
        .map(k => {
            return isEncode && !isUrlEncoded(obj[k])
                ? [encodeURIComponent(k), encodeURIComponent(obj[k])].join('=')
                : `${k}=${obj[k]}`;
        })
        .join('&');

    return `${params}`;
}

/**
 * url拼接对象query
 *
 * @param  {string} url - 输入url
 *          {object} obj - 输入query对象
 *          {boolean} isEncode - 参数是否编码，默认为编码
 * @returns {string} 输出url
 * @example
 */
export function addObjectToUrlQuery(url: string, obj: any, isEncode = true) {
    const query = queryString(obj, isEncode);
    if (!query) {
        return url;
    }

    if (url.indexOf('?') > -1) {
        return url?.endsWith('&') ? `${url}${query}` : `${url}&${query}`;
    }

    return `${url}?${query}`;
}

export function genImMsgKey(length) {
    const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = length; i > 0; --i) {
        result += chars[Math.floor(Math.random() * chars.length)];
    }

    return result;
}

/**
 * 一维数组转成指定条数的二维数据
 * @param {Array}
 * @param {Number} 二维数组中的一条有几个数据
 */
export const change2Array = (arr, n) => {
    const len = arr.length;
    const lineNum = len % n === 0 ? len / n : Math.floor(len / n + 1);
    const res = [];
    for (let i = 0; i < lineNum; i++) {
        const temp = arr.slice(i * n, i * n + n);
        res.push(temp as never);
    }

    return res;
};

/**
 * 扩展对象
 *
 * @param {...Object} args 扩展对象参数列表
 * @return {Object} 扩展后的对象
 */
export function extend(...args: IfObject[]) {
    const arr = Array.prototype.slice.call(args);
    const org = arr.shift();
    arr.forEach(function (obj: IfObject) {
        const o = obj || {};
        Object.getOwnPropertyNames(o).forEach(function (key: string) {
            if (isObject(o[key]) && isObject(org[key])) {
                extend(org[key], o[key]);
            } else {
                if (o[key] !== undefined) {
                    org[key] = o[key];
                }
            }
        });
    });

    return org;
}

/**
 *
 * @description 深层 merge 对象数据
 * @param keyName a.b.c
 * @param val
 * @param defaultObj
 * @returns
 */
export function deepMergeObj(keyName, val, defaultObj) {
    try {
        const keyArr = typeof keyName === 'string' ? keyName.split('.') : [];
        const initObj = cloneDeep(defaultObj);

        const obj = Object.assign({}, initObj);
        keyArr.reduce((cur, key, index) => {
            if (!cur[key]) {
                cur[key] = {};
            }
            if (index === keyArr.length - 1) {
                cur[key] = val;
            }

            return cur[key];
        }, obj);

        return obj;
    } catch (err) {
        console.error('deepMergeObj 出错：', err);
    }
}

/**
 * 格式化api
 *
 * @param {string} api 原始api接口
 * @return {string} 格式化后的api，作为webb报表的key(异步接口：接口名）
 */
export const getApi = (api: string) => {
    const regx = /^https?:\/\/([\w-:.]+)([/\w]+).*/;
    const group = api.match(regx) || [];
    if (group.length < 3) {
        return;
    }
    const path = group[2] && group[2].replace('/med/', '');

    return path;
};

/**
 *
 * @description 拼接参数到 url
 * @param param0
 * @returns
 */
export const addParamsOfUrl = ({
    urlArg,
    params,
    ignoreParamKeys = ['$taroTimestamp']
}: {
    urlArg: string;
    params: {
        [k in string]: string | number | undefined;
    };
    ignoreParamKeys?: string[];
}): string => {
    try {
        let resUrl = urlArg;

        if (params && Object?.keys(params)?.length && urlArg) {
            resUrl = Object?.keys(params)?.reduce((pre, cur, index) => {
                if (ignoreParamKeys.includes(cur)) {
                    return pre;
                }

                const enParam = isUrlEncoded(params[cur])
                    ? params[cur] || ''
                    : encodeURIComponent(params[cur] as string);

                if (urlArg?.includes('?')) {
                    return urlArg?.endsWith('&')
                        ? `${pre}${cur}=${enParam}`
                        : `${pre}&${cur}=${enParam}`;
                }

                return index > 0 ? `${pre}&${cur}=${enParam}` : `${pre}?${cur}=${enParam}`;
            }, urlArg);
        }

        return resUrl;
    } catch (err) {
        console.error('urlDecorateFn 出错：', err);

        return urlArg;
    }
};

type handlerType = 'get' | 'add' | 'del' | 'set';

/**
 * 缓存数组，控制数组长度，可以添加和删除
 * @param storageKey 缓存的key
 * @param maxStoreNum 数组最大缓存length
 * @returns (type: handlerType = 'get', _value?: string) => wenzhenGuideStorage
 */

export const handlerWenzhenStorage = (storageKey, maxStoreNum = 6) => {
    function wenzhenGuideStorage<T>(type: handlerType = 'get', _value?: T) {
        try {
            const MAX_STORE_NUMBER = maxStoreNum;

            if (type === 'del') {
                Taro.removeStorageSync(storageKey);

                return [];
            }

            const _recentlyAddedString: string = Taro.getStorageSync(storageKey) || '[]';
            const _recentlyAdded: Array<string> = JSON.parse(_recentlyAddedString);

            if (type === 'add') {
                const strVal = typeof _value === 'object' ? JSON.stringify(_value) : _value;
                _value && !_recentlyAdded.includes(strVal) && _recentlyAdded.unshift(_value);
                if (_recentlyAdded.length > MAX_STORE_NUMBER) {
                    _recentlyAdded.pop();
                }
                Taro.setStorageSync(storageKey, JSON.stringify(_recentlyAdded));
            }

            if (type === 'set') {
                Taro.setStorageSync(storageKey, _value);
            }

            return _recentlyAdded;
        } catch (e) {
            return [];
        }
    }

    return wenzhenGuideStorage;
};

/**
 * 处理jsonObj参数转换成encodeURIComponent对象
 * @param jsonObj json对象
 * @param type decode | encode // 编码 | 解码
 * @returns 新的encodeURIComponent| decodeURIComponent 对象
 */
export const urlDecode = (jsonObj, type = 'decode') => {
    for (const k in jsonObj) {
        if (typeof jsonObj[k] === 'string') {
            jsonObj[k] =
                type === 'decode' ? decodeURIComponent(jsonObj[k]) : encodeURIComponent(jsonObj[k]);
        } else if (typeof jsonObj[k] === 'object') {
            jsonObj[k] = urlDecode(jsonObj[k]);
        }
    }

    return jsonObj;
};

/**
 * 获取实验Sid 值
 * @returns Promise<string>
 */
export const getCookieSid = (): Promise<string> => {
    return new Promise(async resolve => {
        try {
            const env = process.env.TARO_ENV;
            const sid = env === 'h5' ? cookie('H_WISE_SIDS') : (await getCommonSysInfo()).sid;
            resolve(sid || '');
        } catch (error) {
            resolve('');
        }
    });
};

/**
 *
 * @description adsData 合并到 stateData 中，过滤掉指定 key
 * @param stateData state中adsData最新数据
 * @param adsData 接口新获取adsDta数据
 * @param filterData 为空时不需要覆盖的key
 * @returns * <T> 合并后的对象
 */
export function updateObjExcludeSpecifyKeys<T extends Record<string | unknown, unknown>>(
    stateData: T,
    adsData: T,
    filterData?: string[]
): T {
    const compStateData = {};
    filterData?.forEach((key: string) => {
        if (!Object.keys(adsData).includes(key) && stateData[key]) {
            compStateData[key] = {...stateData[key]};
        }
    });
    stateData = {...adsData, ...compStateData};

    return stateData;
}

/**
 * @Description 验证是否大于等于ios17系统
 * @returns {Boolean}
 */
export const validateGtOrEqualIOS17 = () => {
    let {system} = getSystemInfoSync();
    const systemVersion = parseFloat(system?.replace(/[^0-9|_.]/gi, '')?.replace(/_/gi, '.')); // 提取系统版本号并转为数字;
    return system.toLocaleLowerCase().includes('ios') && systemVersion >= 17;
};

// url 参数解析
export const paramsDecode = (url: string) => {
    const urlOnFn = decodeURIComponent(url);
    const result = {};
    if (urlOnFn) {
        const params = urlOnFn.split('&');
        params.forEach(item => {
            const [key, value] = item.split('=');
            result[key] = value;
        });
    }

    return result;
};

/**
 * 关键词高亮
 *
 * @param {string} 需要替换的富文本
 * @return {string} 替换后含有高亮样式的富文本
 */
export const highlightKeyword = (text = '') => {
    if (text) {
        return text
            .replace(new RegExp('<b>', 'g'), '<span style="color: #00c8c8;font-weight: bold;">')
            .replace(new RegExp('</b>', 'g'), '</span>');
    }

    return '';
};

/**
 * 判断展示行数是否超过限制
 *
 */
export const getDomHeight = (text = '', limit: number) => {
    const BASIC_WIDTH = 375;
    const BASIC_STR_WIDTH = 283;
    const {screenWidth = BASIC_WIDTH} = getSystemInfo();
    let totalChatLength = 0;

    // 匹配中文
    const matchRegRules = [
        {
            reg: /[\u4e00-\u9fa5]/g,
            size: 15
        },
        // 匹配数字
        {
            reg: /\d/g,
            size: 12
        },
        // 匹配标点符号
        {
            reg: /[^\u4e00-\u9fa5a-zA-Z\d\s<>\\/\\-]/g,
            size: 15
        }
    ];

    matchRegRules.forEach(item => {
        const _matches = text.match(item.reg);
        const _length = _matches?.length || 0;
        if (_length) {
            totalChatLength += _length * ((item.size / BASIC_WIDTH) * screenWidth);
        }
    });

    const _totalLine = Math.ceil(totalChatLength / ((screenWidth * BASIC_STR_WIDTH) / BASIC_WIDTH));

    return _totalLine > limit;
};

/**
 *  判断是否为有效日期格式
 * @param dateString 日期字符串
 * @returns 是否为有效日期
 */
export const isValidDate = dateString => {
    const date = dayjs(dateString);

    return !isNaN(date?.valueOf());
};

/**
 * @description: 判断是否在微信小程序webview环境中
 * @return {Boolean}
 */
export const isWxMiniProgramWebView = () => {
    let result = false;
    if (process.env.TARO_ENV === 'h5') {
        const ua = window.navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i)?.includes('micromessenger')) {
            // 微信环境
            if (ua.match(/miniProgram/i)?.includes('miniprogram')) {
                // 微信小程序
                result = true;
            }
        }
    }

    return result;
};
