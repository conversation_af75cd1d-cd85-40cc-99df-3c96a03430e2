import {getSystemInfo} from '../utils/taro';
import {getCurrentPage, isUrlEncoded, versionCompare} from '@baidu/vita-utils-shared';
import {showModal} from '@tarojs/taro';
import {ubcCommonViewSend} from '../utils/generalFunction/ubc';

import {H5_HOST} from '../models/apis/host';
import {navigate} from '../utils/basicAbility/commonNavigate';

// 当前系统信息
const systemInfo = getSystemInfo();

// 当前系统版本
const system = systemInfo?.system;

export const getCurrentSystem = () => {
    const systemVersion = parseFloat(system?.replace(/[^0-9|_.]/gi, '')?.replace(/_/gi, '.')); // 提取系统版本号并转为数字

    return systemVersion;
};

export const isIos = () => {
    return system?.toLowerCase()?.indexOf('ios') !== -1;
};

// 限制ios系统版本
/**
 *
 * @param version ios系统版本
 * @description 适配低版本ios
 * @returns boolean
 */
export const fitLowIosSystemVersion = (version = 18) => {
    // const isIos = system?.toLowerCase()?.indexOf('ios') !== -1;
    if (isIos() && process.env.TARO_ENV === 'swan' && getCurrentSystem() >= version) {
        return false;
    }

    return true;
};

// 兼容ios-17.4系统 手百跳转到微信小程序
export const handleJumpWx = () => {
    try {
        const systemVersion = getCurrentSystem();
        // 版本分布 http://yq01-sys-hsc08-643ac.yq01.baidu.com:8080/mini_sdkversion/data/sdkversion-latest.md.txt
        const {version = '*********'} = systemInfo;
        // 匹配 iOS 17.4 + 系统
        if (
            process.env.TARO_ENV === 'swan' &&
            system?.toLowerCase()?.indexOf('ios') !== -1 &&
            systemVersion >= 17.4 &&
            versionCompare(version, '*********') < 0
        ) {
            showModal({
                title: '提示',
                content: '当前版本功能异常，请跳转百度健康微信小程序后使用',
                confirmText: '跳转微信',
                success: res => {
                    if (res.confirm) {
                        ubcCommonViewSend({
                            value: 'ios_input_jumpWx'
                        });
                        const {path} = getCurrentPage();
                        const _path = isUrlEncoded(path) ? decodeURIComponent(path) : path;
                        // eslint-disable-next-line max-len
                        const shareUrl = `${H5_HOST}/wenzhen/pages/middlePage/index?type=wx_wenzhen&targetVersion=release&targetUrl=${encodeURIComponent(
                            _path
                        )}&disabledFailedJump=1`;
                        navigate({
                            url: shareUrl,
                            openType: 'easybrowse'
                        });
                    }
                }
            });

            return;
        }
    } catch (error) {
        console.error(error);
    }
};

export const checkWxRouteMap = (targetUrl: string, routesMap: object) => {
    let url = '';
    const routeKeys = Object.keys(routesMap || {});
    routeKeys.find(key => {
        if (targetUrl.indexOf(key) > -1) {
            url = routesMap[key];
        }
    });

    return url;
};
