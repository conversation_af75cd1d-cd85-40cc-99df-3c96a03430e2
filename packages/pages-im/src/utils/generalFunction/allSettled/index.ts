type SettledResult<T> = {status: 'fulfilled'; value: T} | {status: 'rejected'; reason: unknown};

type AllSettledReturn<T extends Iterable<unknown>> =
    T extends Iterable<infer U> ? SettledResult<Awaited<U>>[] : never;

export function allSettled<T extends Iterable<unknown>>(iterable: T): Promise<AllSettledReturn<T>> {
    const arr = Array.isArray(iterable) ? iterable : Array.from(iterable);
    const wrapped = arr.map(item =>
        Promise.resolve(item)
            .then<SettledResult<unknown>>(v => ({status: 'fulfilled', value: v}))
            .catch<SettledResult<unknown>>(r => ({status: 'rejected', reason: r}))
    );

    return Promise.all(wrapped) as Promise<AllSettledReturn<T>>;
}
