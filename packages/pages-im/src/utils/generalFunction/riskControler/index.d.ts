export interface CommonSysInfo {
    cuid: string;
    zid: string;
    imei: string;
}
export interface SystemInfo {
    version: string;
    platform: string;
    model: string;
    brand: string;
    system: string;
    screenWidth: number;
    screenHeight: number;
}

export interface GetFkParamsArgs {
    ak: string; // userConf.ak 渠道号（安全实验室分配app key）
    o?: unknown; // 活动场景值
    dataApp: unknown;
    ev: string;
}

export interface SwanFkInfo {
    zid: string;
    info: CommonSysInfo;
    sys: SystemInfo
}

export interface FkParamsVal {
    aid?: string;
    caller?: string;
    c?: string;
    i?: string;
    z?: string;
    app: string;
    ver?: string;
    model?: string;
    brand: string;
    ev: string;
    to?: string;
    vw?: string;
    view?: string;
    os_version?: string;
    mode?: number;
    zid?: string;
    v?: string;
    reso: string;
    os?: number;
    jt?: string;
    js_env?: string;
}
