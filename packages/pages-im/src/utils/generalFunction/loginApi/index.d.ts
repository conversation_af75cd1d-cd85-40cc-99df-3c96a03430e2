import type {OpenType} from '@common/utils/basicAbility/commonNavigate';

export interface UserConfParams {
    params?: {
        detail?: {
            encryptedData: string;
            appid: string;
            wxCode: string;
        };
        wxCode?: string;
        src?: string;
    };
    href?: string;
    backUrl?: string;
    openType?: OpenType;
    type?: number; // 登录类型
    isReplaceUrl?: boolean;
    loginStatus?: boolean | number;
    interactionType?: 'popup' | 'page'; // 登录交互类型，主要针对微信小程序场景
    successCallback?: () => void;
    failCallback?: () => void;
}

interface PassPluginInitParams {
    tpl: string;
    appid: string;
    backu: string;
    supportGuestAccount: 0 | 1;
    authsite: 0 | 1;
    isAgree: 0 | 1;
}

type SuccessCallback = () => void;
type FailCallback = (error: {page: string; jump: number}) => void;

export interface WxPassPluginInstance {
    initPass: (args: PassPluginInitParams) => void;
    getUserInfo: () => void;
    clearUserInfo: () => void;
    mobileDirectLogin: (params: unknown, success: SuccessCallback, fail: FailCallback) => void;
}

export interface PassInitFnParams {
    backUrl: string;
}

declare const swan: {
    navigateToMiniProgram?: (options: {
        appId: string;
        path?: string;
        extraData?: Record<string, any>;
        success?: (res: any) => void;
        fail?: (err: any) => void;
        complete?: () => void;
    }) => void;
    exit?: () => void;
    [key: string]: any;
};
