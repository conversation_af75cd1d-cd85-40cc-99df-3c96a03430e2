import {getQueryStr, extend} from '@baidu/vita-utils-shared';

import type {UserConfParams, WxPassPluginInstance, PassInitFnParams} from './index.d';

/**
 * H5调起登录页
 *
 * @param {Object} userConf 用户配置，详见：http://wiki.baidu.com/pages/viewpage.action?pageId=*********
 * @param {number} userConf.type        登录类型：1-账号密码；2-手机号验证码
 * @param {Object} userConf.params      统计参数：
 * @param {number} userConf.href        登录成功后跳转地址（为空则跳转触发登录的页面）
 * @param {number} userConf.isReplaceUrl 进入登录页是否replaceState
 */
export const loginApi: (userConf?: UserConfParams) => Promise<null> = (userConf = {}) => {
    return new Promise((resolve, reject) => {
        try {
            const host = 'https://wappass.baidu.com';

            const conf = extend(
                {
                    type: 1,
                    params: {
                        src: 'se_020003',
                        from: 'common'
                    },
                    href: '',
                    isReplaceUrl: false
                },
                userConf
            );

            let query: {[k in string]: unknown} = {};
            query = extend(
                {
                    adapter: 3, // 隐藏titlebar
                    tpl: 'askdoctor',
                    overseas: 1,
                    extrajson: JSON.stringify(conf.params),
                    u: conf.href
                        ? encodeURIComponent(window.location.origin + conf.href)
                        : encodeURIComponent(window.location.href)
                },
                conf.type === 2
                    ? {
                        sms: 1
                    }
                    : {}
            );
            if (conf.href.indexOf('http') === 0) {
                // 如果是完整的跳转地址直接封装url跳转
                query.u = encodeURIComponent(conf.href);
            }
            const url = `${host}?${getQueryStr(query)}`;
            if (conf.isReplaceUrl) {
                window.location.replace(url);
            } else {
                window.location.href = url;
            }
            resolve(null);
        } catch (err) {
            reject(err);
        }
    });
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const initPlugin = (initParams?: PassInitFnParams): WxPassPluginInstance => {
    console.info('该方法仅支持在微信小程序使用', initParams);

    return {} as WxPassPluginInstance;
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const bindWxWithPass = (arg?: {bduss: string}): Promise<null> => {
    return new Promise(resolve => {
        resolve(null);
    });
};

export const checkWxAccountBindStatus = (): Promise<unknown> => {
    return new Promise(resolve => {
        resolve(null);
    });
};

/**
 * 当前百度账号是否已经关注了百度健康微信公众号
 * @returns 返回Promise对象，包含isBind字段表示是否绑定成功
 */
export const checkWxAccountBindStatusOfNetHospitalService = (): Promise<{isBind: boolean}> => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async resolve => {
        resolve({
            isBind: false
        });
    });
};

export const checkWxAccountBindingStatusBeforePayment = (): Promise<unknown> => {
    return new Promise(resolve => {
        resolve(null);
    });
};
