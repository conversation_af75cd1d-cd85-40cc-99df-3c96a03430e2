import { setStorage, getStorageSync } from '@tarojs/taro';

const storeKey = 'wxLoginToken';

interface WxTokenValType {
    tokenName: string;
    token: string
}

export const setWxLoginToken = (v: WxTokenValType) => {
    setStorage({
        key: storeKey,
        data: JSON.stringify(v)
    });
};

export const getWxLoginToken = (): Promise<WxTokenValType> => {
    return new Promise(async (res, rej) => {
        try {
            const resp = await getStorageSync(storeKey);
            res(resp ? JSON.parse(resp) : null);
        } catch (err) {
            rej(err);
        }
    });

};
