/**
 * 自定义 showToast
 */
import { getSystemInfo } from './taro';
import { showToast as taroShowToast } from '@tarojs/taro';

export function showToast(options) {
    const sysInfo = getSystemInfo();
    const elderDuration = 7000; // 长辈模式状态下的duration
    const durationData = sysInfo?.isElderMode ? elderDuration : options.duration || 1500;

    return taroShowToast({
        ...options,
        duration: durationData
    });
}
