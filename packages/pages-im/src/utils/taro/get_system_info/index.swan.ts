import Taro from '@tarojs/taro';
import globalData from '../../../globalDataStore/globalData';
import { SystemInfoProps } from './index.d';

export const getSystemInfo = (): SystemInfoProps => {
    if (globalData.get('systemInfo')) {
        return globalData.get('systemInfo');
    }

    const sysInfo = Taro.getSystemInfoSync();
    const { statusBarHeight = 0, fontSizeSetting = 0, isElderMode } = sysInfo;
    const navigationBarHeight = sysInfo.navigationBarHeight || 44; // 百度小程序直接返回此数据
    const bottomSafeAreaHeight = sysInfo.bottomSafeHeight || 0; // 百度小程序直接返回此数据

    const info = {
        ...sysInfo,
        navigationBarHeight,
        bottomSafeAreaHeight,
        barHeight: navigationBarHeight + statusBarHeight,
        bigFontSizeClass: fontSizeSetting >= 4 ? 'big-fontsize' : '',
        isElderMode
    };
    globalData.set('systemInfo', info);

    return info;
};
