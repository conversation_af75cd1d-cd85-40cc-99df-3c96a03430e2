import globalData from '../../../globalDataStore/globalData';
import { getApi } from '../../index';
import { getSystemInfo } from '../get_system_info';

/**
 * 获取bduss
 *
 * @public
 * @return {Promise.<Object>} 返回bduss值（Promise）
 */
export const getBduss = url =>
    new Promise(resolve => {
        const bduss = (globalData.get('login') && globalData.get('login').bduss) || '';
        // 浏览器厂商联合账号
        const unionBduss = (globalData.get('login') && globalData.get('login').unionBduss) || '';
        if (bduss || unionBduss) {
            resolve({
                bduss,
                unionBduss
            });
        } else {
            // bduss请求数
            swan.reportAnalytics('bduss_sum_count', {
                url: getApi(url || '')
            });
            if (swan.canIUse('getBDUSS')) {
                swan.getBDUSS({
                    success(res) {
                        if (res && res.bduss) {
                            globalData.set('login', { bduss: res.bduss });
                            resolve({
                                bduss: res.bduss
                            });
                        } else {
                            resolve({
                                bduss: ''
                            });
                        }
                    },
                    fail(e) {
                        // bduss失败数
                        const sys = getSystemInfo();
                        swan.reportAnalytics('bduss_error_count', {
                            code: e && e.errCode,
                            url: getApi(url || ''),
                            sys: (sys.system || '').toLowerCase().replace(/[\d.]/g, ''),
                            host: sys.host,
                            version: sys.version
                        });
                        resolve({
                            bduss: ''
                        });
                    }
                });
            } else if (swan.canIUse('getUnionBDUSS')) {
                swan.getUnionBDUSS({
                    success(res) {
                        const sys = getSystemInfo();
                        if (res && res.bduss) {
                            globalData.set('login', { unionBduss: res.bduss });
                            resolve({
                                bduss: '',
                                unionBduss: res.bduss,
                                hostName: sys.host
                            });
                        } else {
                            resolve({
                                bduss: '',
                                unionBduss: '',
                                hostName: sys.host
                            });
                        }
                    },
                    fail(e) {
                        // bduss失败数
                        const sys = getSystemInfo();
                        swan.reportAnalytics('bduss_error_count', {
                            code: e && e.errCode,
                            url: getApi(url || ''),
                            sys: (sys.system || '').toLowerCase().replace(/[\d.]/g, ''),
                            host: sys.host,
                            version: sys.version
                        });
                        resolve({
                            bduss: '',
                            unionBduss: '',
                            hostName: sys.host
                        });
                    }
                });
            } else {
                resolve({
                    bduss: ''
                });
            }
        }
    });
