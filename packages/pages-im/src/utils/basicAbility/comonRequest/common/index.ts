import {
    HttpRequest,
    type RequestConf,
    type RequestReturnValType,
    type AfterRequestReturnVal,
    type RejectErrorVal,
    type UnLoginReturnVal
} from '@baidu/vita-utils-shared';

import {
    unLoginCallback,
    afterRequestCallback,
    requestSucceedCallback,
    requestFailedCallback
    // beforeRequestCallback
} from './requestCallback';
import editConf from '../utils/editConf';

import {SUCCESSFUL_STATUS, DEFAULT_TIME_OUT} from '../../../../constants/common';

interface RequestArgs extends RequestConf {
    needTransparentWhiteListParams?: boolean;
}

export default async function <T> (args: RequestArgs): Promise<RequestReturnValType<T, null>> {
    const instance = new HttpRequest<T, null>({
        enableHttp2: true,
        timeout: DEFAULT_TIME_OUT,
        // 设置请求头类型
        header: {
            'Content-Type': 'application/json'
        },
        statusVariable: 'status',
        confDecorateFn: editConf as (conf: RequestConf) => Promise<RequestConf>,
        successfulStatus: SUCCESSFUL_STATUS,
        unLoginStatus: [2, 40001, 30002],
        unLoginCallback: (args: UnLoginReturnVal) => unLoginCallback(args as any),
        // beforeRequestCallback: (conf: RequestConf) => beforeRequestCallback(conf),
        afterRequestCallback: (args: AfterRequestReturnVal<T, null>) =>
            afterRequestCallback(args as any),
        requestSucceedCallback: (args: AfterRequestReturnVal<T, null>) =>
            requestSucceedCallback(args as any),
        requestFailedCallback: (args: RejectErrorVal) => requestFailedCallback(args as any)
    });

    return instance.request(args);
}
