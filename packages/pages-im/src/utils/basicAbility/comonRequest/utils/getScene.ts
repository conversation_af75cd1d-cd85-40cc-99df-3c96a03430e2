import {WzSceneType} from '../../../../typings/common';
import {getQueryObject, getCurrentUrlQuery} from '@baidu/vita-utils-shared';

/**
 * 匹配学习强国webview环境
 * */
export const isAliApp = () => {
    if (process.env.TARO_ENV === 'h5') {
        const ua = navigator.userAgent;
        const isWebview = ua.includes('MiniProgram');

        return isWebview ? /(AliApp|XuexiClient|Client)/i.test(ua) : false;
    }

    return false;
};

/**
 *
 * @description 获取场景值
 * @param param0 url 页面 href
 * @returns
 */
export const getScene = ({url}: {url: string}): WzSceneType => {
    try {
        if (!url) {
            return undefined;
        }

        const query = getQueryObject(url);
        const pageQuery = getCurrentUrlQuery();

        // cui 沟通去掉 scene 赋值，scene 为透传参数，前端修改 channel_code
        // return isAliApp() ? 'web_xuexiqiangguo' : pageQuery?.scene || query?.scene;
        return pageQuery?.scene || query?.scene;
    } catch (err) {
        console.error('getScene 出错：', err);

        return undefined;
    }
};
