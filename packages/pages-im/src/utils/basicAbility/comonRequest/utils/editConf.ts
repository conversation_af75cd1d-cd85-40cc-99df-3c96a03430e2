import {random} from '@baidu/health-utils';
import {getStorageSync} from '@tarojs/taro';
import {
    RequestConf,
    getAppInfo,
    isWxMiniProgramWebView,
    uuid,
    isUrlEncoded,
    getCurrentUrlQuery,
    decodeUntilFullyDecoded
} from '@baidu/vita-utils-shared';
import {getSystemInfo, getCommonSysInfo, getBduss} from '../../../taro';

import store from '../../../../globalDataStore/index';
import globalData from '../../../../globalDataStore/globalData';
import {API_HOST} from '../../../../models/apis/host';

import {getWechatPassUserInfo} from '../../../../utils/generalFunction/login';

import {
    PASSED_URL_PARAMS,
    COMMON_CONF,
    COMMON_PARAMS,
    APP_KEY_MAP
} from '../../../../constants/common';

import {extend, addObjectToUrlQuery} from '../../../index';

import {getScene} from './getScene';
import {getVitaVersion} from './getVersion';
import {getChannelCode} from './getChannelCode';

// 扩展 RequestConf 类型以支持额外的参数
interface ExtendedRequestConf extends RequestConf {
    needTransparentWhiteListParams?: boolean;
}

type CommonConf = {
    [k in string]: string | number;
};

interface PlatformAddFnParams {
    conf: ExtendedRequestConf;
    headers?: Headers;
}

// Cookie 相关工具函数
const createCookieString = (cookies: Record<string, string>): string => {
    return Object.entries(cookies)
        .filter(([_, value]) => value)
        .map(([key, value]) => `${key}=${value};`)
        .join(' ');
};

const mergeCookies = (existingCookie: string = '', newCookie: string): string => {
    return existingCookie
        ? `${existingCookie}${existingCookie.endsWith(';') ? '' : ';'}${newCookie}`
        : newCookie;
};

// 获取系统信息
const getSystemInfoData = async (): Promise<{
    bdu: any;
    sys: any;
    sysT: any;
}> => {
    const [bdu, sys, sysT] = await Promise.all([getBduss(), getCommonSysInfo(), getSystemInfo()]);
    return {bdu, sys, sysT};
};

// 处理百度小程序 header
const addSwanPublicHeaders = async ({
    conf,
    headers
}: PlatformAddFnParams): Promise<ExtendedRequestConf> => {
    const {bdu, sys, sysT} = await getSystemInfoData();

    const cookieComponents = {
        BAIDUID: sys.baiduid || sys.baidu_id || '',
        H_WISE_SIDS: sys.sid || '',
        BDUSS: bdu.bduss || '',
        OPENBDUSS: bdu.unionBduss || '',
        BAIDUCUID: sys.cuid || '',
        UNION_BDUSS: bdu.unionBduss || '',
        HOST_NAME: sysT.host || ''
    };

    const cookieString = createCookieString(cookieComponents);
    const finalCookie = mergeCookies(conf.header?.Cookie, cookieString);

    return {
        ...conf,
        header: {
            ...conf.header,
            Cookie: finalCookie,
            ...headers
        }
    };
};

// 处理微信小程序 header
const addWeappPublicHeaders = async ({
    conf,
    headers
}: PlatformAddFnParams): Promise<ExtendedRequestConf> => {
    const userInfo = getWechatPassUserInfo();
    const jkPlatform = getAppInfo();
    const weappSids = getCommonSysInfo();
    const baiduId = getStorageSync('BAIDUID') || '';

    const cookieComponents = {
        BDUSS: String(userInfo?.bduss || ''),
        jkPlatform: String(jkPlatform.platformType || ''),
        H_WISE_SIDS: weappSids.sid || '',
        BAIDUID: String(baiduId)
    };

    const cookieString = createCookieString(cookieComponents);
    const finalCookie = mergeCookies(conf.header?.Cookie, cookieString);

    return {
        ...conf,
        header: {
            ...conf.header,
            Cookie: finalCookie,
            ...headers
        }
    };
};

// 处理 H5 header
const addH5Header = async ({conf, headers}: PlatformAddFnParams): Promise<ExtendedRequestConf> => {
    if (!isWxMiniProgramWebView()) {
        document.cookie = 'jkFrom=;Path=/;domain=.baidu.com;expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    }

    return {
        ...conf,
        header: {
            ...conf.header,
            ...headers
        }
    };
};

// 处理通用数据
const addCommonData = (conf: ExtendedRequestConf): Partial<ExtendedRequestConf> => {
    const query = getCurrentUrlQuery();
    const data = {...conf.data};

    // 处理通用参数
    COMMON_PARAMS.forEach(key => {
        if (!query[key]) {
            let value = '';
            if (process.env.TARO_ENV === 'swan') {
                value = globalData.get(key) || '';
            } else if (process.env.TARO_ENV === 'h5') {
                value = window.sessionStorage.getItem(key) || '';
            }
            if (value) {
                data[key] = value;
            }
        }
    });

    const globalDataLog = globalData.get('log') || {};
    const lid = globalDataLog.lid || query.lid;
    const clkValue = globalDataLog.clk_loc || query.clk_loc || '';
    const clk_loc = isUrlEncoded(clkValue) ? decodeURIComponent(clkValue) : clkValue;
    const applid = conf.isFirstScreen ? '' : globalDataLog.applid || '';
    const referlid = conf.isFirstScreen
        ? String(globalDataLog.applid || globalDataLog.referlid || query.referlid || random())
        : String(globalDataLog.referlid || query.referlid || random());

    const v = process.env.TARO_ENV === 'swan' ? store.get('appVersion') : '';

    const vitaVersion = getVitaVersion();
    const reqId = uuid(10);
    const scene = conf?.url ? getScene({url: conf.url}) : '';
    const channel_code = getChannelCode();

    delete data.fclk;

    return {
        data: {
            ...data,
            referlid,
            applid,
            lid,
            v,
            reqId,
            scene,
            channel_code,
            clk_loc,
            vitaVersion,
            appKey: APP_KEY_MAP['wenzhen']?.[process.env.TARO_ENV || '']
        }
    };
};

// 处理 URL 参数
const addParamsToUrl = (conf: ExtendedRequestConf): Partial<ExtendedRequestConf> => {
    if (!conf.needTransparentWhiteListParams) {
        return conf;
    }

    const pageQuery = getCurrentUrlQuery();
    const channel_code = getChannelCode();
    const assignParams: Record<string, string> = channel_code ? {channel_code} : {};

    PASSED_URL_PARAMS.forEach(key => {
        const value = pageQuery[key];
        if (value) {
            const finalValue = Array.isArray(value) ? value[value.length - 1] : value;
            if (key === 'word') {
                const decodedWord = isUrlEncoded(finalValue)
                    ? decodeUntilFullyDecoded(finalValue)
                    : finalValue;
                assignParams[key] = decodedWord.trim().slice(0, 500);
            } else {
                assignParams[key] = finalValue;
            }
        }
    });

    const url = addObjectToUrlQuery(conf.url, assignParams, true);
    return {
        url: url.replace(/;/g, '')
    };
};

// 处理 URL host
const addUrlHost = (conf: ExtendedRequestConf): ExtendedRequestConf => {
    if (conf.url && !conf.url.startsWith('http')) {
        return {
            ...conf,
            url: conf.url.startsWith('/') ? `${API_HOST}${conf.url}` : `${API_HOST}/${conf.url}`
        };
    }
    return conf;
};

// 处理通用配置
const addCommonConf = (
    conf: ExtendedRequestConf,
    commonConfObj: CommonConf
): ExtendedRequestConf => {
    const baseConf = extend({}, conf, {...commonConfObj});
    return {
        ...baseConf,
        ...addCommonData(baseConf),
        ...addParamsToUrl(baseConf)
    };
};

// 主函数
export default async function editConf(conf: ExtendedRequestConf): Promise<ExtendedRequestConf> {
    try {
        const platformHandlers: Record<
            string,
            (args: PlatformAddFnParams) => Promise<ExtendedRequestConf>
        > = {
            swan: addSwanPublicHeaders,
            weapp: addWeappPublicHeaders,
            h5: addH5Header
        };

        const handler = platformHandlers[process.env.TARO_ENV || 'h5'];
        const headers: Record<string, string> = process.env.TARO_APP_HEADER_BAGGAGE
            ? {Baggage: `x-mesh-traffic-lane=${process.env.TARO_APP_HEADER_BAGGAGE}`}
            : {};

        const processedConf = await handler({
            conf: addCommonConf(conf, COMMON_CONF || {}),
            headers: headers as unknown as Headers
        });

        if (processedConf.data) {
            delete processedConf.data.$taroTimestamp;
        }

        return addUrlHost(processedConf);
    } catch (err) {
        console.error('addPublicParams 函数出错, 公共参数加载失败。错误：', err);
        return conf;
    }
}
