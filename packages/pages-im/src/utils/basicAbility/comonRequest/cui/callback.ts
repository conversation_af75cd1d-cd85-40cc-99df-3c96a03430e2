import {hideLoading} from '@tarojs/taro';
import {
    AfterRequestReturnVal,
    RejectErrorVal,
    RequestConf,
    getCurrentPage,
    weirwoodReqFailedReport
} from '@baidu/vita-utils-shared';

import {showToast} from '../../../../utils/customShowToast';
import globalData from '../../../../globalDataStore/globalData';
import {addObjectToUrlQuery} from '../../../../utils';
import {pageNetworkStatus} from '../../../../constants/common';
import {navigate} from '../../../../utils/basicAbility/commonNavigate';
import {isFilterErrorUrl} from '../common/callbackUrl';

export const beforeRequestCallback = (conf: RequestConf): Promise<null> => {
    return new Promise((resolve, reject) => {
        try {
            if (conf.isFirstScreen) {
                setNetworkStatus(pageNetworkStatus.loading);
            }
            resolve(null);
        } catch (err) {
            reject(err);
        }
    });
};

export const afterRequestCallback = <T, U>(params: AfterRequestReturnVal<T, U>) => {
    const {conf, response} = params;

    if (conf.isFirstScreen && response) {
        const log = globalData.get('log') || {};
        globalData.set('log', {
            ...log,
            applid: response?.applid,
            // applid为空则不更新referlid
            referlid: log.applid || log.referlid
        });
    }
};

// @ts-expect-error 后续修复
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const requestSucceedCallback = <T, U>(params: AfterRequestReturnVal<T, U>) => {
    setNetworkStatus(pageNetworkStatus.success);
};

export const requestFailedCallback = (params: RejectErrorVal) => {
    const {toast, conf, response, status} = params;
    const {errorToast = true, isFirstScreen} = conf;

    const callUrl = getCurrentPage();
    hideLoading();
    if (status === 40001) {
        // 权限错误跳转
        navigate({
            url: '/pages/common/validateLogin/index',
            openType: 'redirect',
            params: {
                // @ts-expect-error 后续修复
                soId: conf?.soId || '',
                // @ts-expect-error 后续修复
                loId: conf?.loId || '',
                // @ts-expect-error 后续修复
                qid: conf?.qid || '',
                callUrl: encodeURIComponent(callUrl?.path) || ''
            }
        });
    } else if (status === 30002) {
        // 履约单查询出现异常，跳转订单详情页；
        const curPage = getCurrentPage();

        const paramsOfUrl = curPage?.options;
        if (paramsOfUrl.questionId || paramsOfUrl.question_id) {
            paramsOfUrl.qid = paramsOfUrl.questionId || paramsOfUrl.question_id;
        }
        navigate({
            url: '/pages/wz/order/index',
            openType: 'redirect',
            params: paramsOfUrl
        });

        return;
    } else {
        // 通用错误toast提示; errorToast默认是true；flase 则不进行error提示
        const hideToastStatus = [508003];
        if (errorToast && !hideToastStatus.includes(+(status || 0))) {
            toast &&
                showToast({
                    title: toast || '服务异常',
                    icon: 'none',
                    duration: 3000
                });
        }
        // 首屏接口错误时, 将网络状态标示置为error
        if (isFirstScreen) {
            setNetworkStatus(pageNetworkStatus.error);
        }
    }

    if (process.env.TARO_ENV !== 'h5') {
        weirwoodReqFailedReport({
            url: conf?.url,
            params: conf?.data,
            // @ts-expect-error wanghaoyu08 fullResponse 为全部 response 值
            method: conf.method,
            // @ts-expect-error wanghaoyu08 fullResponse 为全部 response 值
            responseHeaders: response.header,
            response: response || {},
            // @ts-expect-error wanghaoyu08 fullResponse 为全部 response 值
            status: response.status
        });
    }
};

export const unLoginCallback = e => {
    try {
        const {response, conf} = e;
        const curPage = getCurrentPage();
        const to = `/${curPage?.path}`;

        // 屏蔽错误页，以免两次鉴权请求太快，跳转2次，传递给pass登录错误的callbackUrl
        if (isFilterErrorUrl(curPage?.path)) {
            return;
        }

        const path =
            process.env.TARO_ENV === 'weapp'
                ? '/wenzhen/pages/bdpass/index'
                : '/wenzhen/pages/common/error/index';
        const pageConfig = {
            PageStatus: 1,
            loginSource: 'se_020003'
        };
        const callUrl = getCurrentPage();

        // cui 和 blackTea 返回错误码冲突，cui 40001 权限错误，blackTea 40001
        if (response?.status === 40001 && conf.url.includes('/wzcui/uiservice')) {
            // 权限错误跳转
            navigate({
                url: '/wenzhen/pages/common/validateLogin/index',
                openType: 'redirect',
                params: {
                    soId: conf?.soId || '',
                    loId: conf?.loId || '',
                    qid: conf?.qid || '',
                    callUrl: encodeURIComponent(callUrl?.path) || ''
                }
            });

            return;
        }

        const params = JSON.stringify({
            ...pageConfig,
            to,
            sourcePage: encodeURIComponent(curPage?.path)
        });
        const url = addObjectToUrlQuery(path, {msg: encodeURIComponent(params)});

        navigate({
            url,
            openType: 'redirect'
        });
    } catch (err) {
        console.error('unLoginCallback 出错：', e);
    }
};

function setNetworkStatus(status = -1) {
    try {
        const network = globalData.get('network');
        if (network) {
            network.status = status;
            globalData.set('network', network);
        }
    } catch (error) {
        console.error(error);
        throw error;
    }
}
