import {type RequestConf, HttpRequest} from '@baidu/vita-utils-shared';

import editConf from '../utils/editConf';
import {SUCCESSFUL_STATUS, DEFAULT_TIME_OUT} from '../../../../constants/common';

import {
    unLoginCallback,
    afterRequestCallback,
    requestSucceedCallback,
    requestFailedCallback,
    beforeRequestCallback
} from './callback';

interface RequestArgs extends RequestConf {
    needTransparentWhiteListParams?: boolean;
}

export default async function <T> (args: RequestArgs) {
    const instance = new HttpRequest<T, null>({
        enableHttp2: true,
        timeout: DEFAULT_TIME_OUT,
        // 设置请求头类型
        header: {
            'Content-Type': 'application/json'
        },
        statusVariable: 'status',
        confDecorateFn: editConf,
        successfulStatus: SUCCESSFUL_STATUS,
        unLoginStatus: [2, 40001],
        unLogin<PERSON>allback,
        beforeRequestCallback,
        afterRequestCallback: afterRequestCallback<T, null>,
        requestSucceedCallback: requestSucceedCallback<T, null>,
        requestFailedCallback
    });

    return instance.request(args);
}
