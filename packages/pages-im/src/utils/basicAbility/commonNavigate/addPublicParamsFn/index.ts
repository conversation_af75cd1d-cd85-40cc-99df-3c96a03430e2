import {getCurrentPage} from '@baidu/vita-utils-shared';
import {WECHAT_TABBAR} from '../../../../path/index';
import {PASSED_URL_PARAMS_FOR_HREF} from '../../../../constants/common';

import {UrlParams} from '../main';

const wenzhenWxTabs = WECHAT_TABBAR.list.map(i => i.pagePath);

export const addPublicParamsFn = ({
    url,
    initialParams,
    curParams
}): {
    params: UrlParams | null;
    extraDataParams: {[k in string]: string | number};
} => {
    try {
        // 增加通用透传的 url params
        const passedParams = {};
        const page = getCurrentPage();

        if (!page.path.includes('pages/middlePage/index')) {
            PASSED_URL_PARAMS_FOR_HREF.forEach(i => {
                if (initialParams[i]) {
                    passedParams[i] = initialParams[i];
                }
            });
        }

        // lid：搜索id，结果页带入，重新搜索更新一次
        // 前一个页面的applid，若当前页面为第一个页面，则referlid=lid
        const resParams = Object.assign({}, {...passedParams, ...curParams});

        // 解决跳转bos的体检报告多拼参数 校验失败的问题
        if (url.indexOf('bcebos.com') > -1) {
            delete resParams.lid;
            delete resParams.referlid;
        }

        // 微信小程序 tab 页，使用 switchTab 不支持传参，故不拼接对应参数
        let isTabBar = false;
        [...wenzhenWxTabs].forEach(path => {
            url.includes(path) && (isTabBar = true);
        });

        return isTabBar && process.env.TARO_ENV === 'weapp'
            ? {
                params: {},
                extraDataParams: resParams
            }
            : {
                params: resParams,
                extraDataParams: {}
            };
    } catch (err) {
        console.error('addPublicParamsFn 出错：', err);

        return {
            params: curParams,
            extraDataParams: {}
        };
    }
};
