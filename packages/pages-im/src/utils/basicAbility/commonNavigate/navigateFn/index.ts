import {navigateTo} from '@tarojs/taro';

import {NavigateFnParams} from './index.d';

export const navigateFn = ({url, openType = 'navigate'}: NavigateFnParams) => {
    if (openType === 'navigateBack') {
        window.history.go(-1);

        return;
    }

    if (url?.startsWith('http') || url?.startsWith('https') || openType === 'otherMiniApp') {
        url && (window.location.href = url);
    } else if (url) {
        navigateTo({
            url
        });
    }
};
