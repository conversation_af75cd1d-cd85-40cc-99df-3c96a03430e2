import {showToast} from '../../customShowToast';

// 不支持的图片格式, 手百中h5禁止上传视频文件格式
const IMAGE_TYPES = ['heic', 'heif', 'mp4', 'mpv', 'avi'];
// 图片格式验证
const imgTypeCheck = tempFiles => {
    return new Promise((resolve, reject) => {
        try {
            const findHeic = tempFiles?.some(file => {
                const fileType = file?.type ? file.type.toLowerCase() : '';
                const _isFind = IMAGE_TYPES.some(type => fileType.includes(type));
                const _imgType = (file?.type || '此') as string;
                const _title = `暂不支持${_imgType}格式图片,请修改后再试`;
                if (_isFind) {
                    showToast({
                        title: _title,
                        icon: 'none'
                    });
                }

                return _isFind;
            });
            if (findHeic) {
                return reject('不支持图片格式,请修改后再试');
            }

            return resolve('success');
        } catch (error) {
            return resolve('success');
        }
    });
};

export const imageVerification = tempFiles => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
        try {
            // await controlPageRender();
            await imgTypeCheck(tempFiles);
            // 其他验证规则
            resolve('success');
        } catch (error) {
            reject(error);
        }
    });
};
