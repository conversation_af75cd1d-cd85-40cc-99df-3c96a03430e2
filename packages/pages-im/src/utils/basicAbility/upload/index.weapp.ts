import {uploadToBos} from '@baidu/health-bos-uploader/lib/index.swan.esm';
import {chooseImage, chooseVideo, getImageInfo} from '@tarojs/taro';
import {ubcCommonViewSend} from '../../generalFunction/ubc';
import {uploadFileToApi} from '../../basicAbility/upload/uploadFileToApi';
import {showToast} from '../../customShowToast';
import {DEFAULT_VIDEO_THUMB} from '../../../constants/msg';
import {getBosToken} from '../../../models/services/common';
import {getSystemInfo} from '../../taro';
import type {IPicConfProps, IPicProps} from '../../../typings/upload';
import {convertBtoMB} from '../../generalFunction';
import {imageVerification} from './imageVerification';

const systemInfo = getSystemInfo();

/**
 * @Description 预览图片
 * @param {Object} Conf 用户配置
 * @param {number} Conf.count 图片数量
 * @param {number} Conf.quality 压缩质量，范围0～100，数值越小，质量越低，压缩率越高（仅对jpg有效）
 * @returns {Promise}
 */
export const previewPic = async (Conf: IPicConfProps) => {
    try {
        const sizeType: Array<'original' | 'compressed'> =
            systemInfo?.platform === 'ios' ? ['original', 'compressed'] : ['original'];
        const data = await chooseImage({
            count: Conf.count || 1,
            sizeType,
            sourceType: Conf?.btnType ? [Conf?.btnType] : ['album', 'camera', 'user', 'environment']
        });

        const {tempFiles} = data;

        // 图片提示拦截
        const picInterception = tempFiles?.some(file => {
            const fileType = file?.type ? file.type.toLowerCase() : '';
            // 如果图片是heic格式，则进行提示拦截
            const _isFindHeic = fileType.includes('heic') || fileType.includes('heif');
            if (_isFindHeic) {
                showToast({
                    title: `暂不支持${file?.type || '此'}格式图片,请修改后再试`,
                    icon: 'none'
                });

                return true;
            }
            // 如果图片尺寸大于imageMaxSize参数，则进行提示拦截
            if (Conf?.imageMaxSize) {
                if (file?.size && Conf.imageMaxSize < convertBtoMB(file.size)) {
                    showToast({
                        title: Conf?.imageOversizeToast
                            ? Conf.imageOversizeToast
                            : `图片尺寸过大，请上传小于${Conf.imageMaxSize}M的图片。`,
                        icon: 'none'
                    });

                    return true;
                }
            }

            return false;
        });
        if (picInterception) {
            return false;
        }
        let resultData: IPicProps[] = [];
        await Promise.all(
            tempFiles.map(async item => {
                // 获取图片宽高
                const infoData = await getImageInfo({src: item?.path});
                // merge数据
                const res = Object.assign({}, item, {
                    width: infoData.width,
                    height: infoData.height,
                    filePath: infoData?.path
                });
                resultData.push(res);
            })
        );
        // 如果限制了上传图片的数量 剔除掉最后数据（H5的 chooseImg在Chrome无法限制数量）
        if ((Conf?.count || 1) < resultData.length) {
            resultData = resultData.slice(0, Conf?.count || 1);
        }

        return resultData;
    } catch (error) {
        console.error(error);

        return [];
    }
};

/**
 * 上传图片
 * @param tempFiles
 * @returns
 */
export const uploadFileToBos = async (
    tempFiles: IPicProps[],
    Conf: IPicConfProps,
    uploadFileType = 'pic'
): Promise<IPicProps[]> => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (res, rej) => {
        try {
            await imageVerification(tempFiles);
            const [bosErr, bosData] = await getBosToken({
                bucketConfName: Conf.bucketConfName || 'muzhi-pic',
                fileNum: Conf.count || 1,
                ...(uploadFileType === 'video' ? {pointTime: 1} : {})
            });
            if (!bosErr && bosData?.status === 0) {
                const {
                    accessKeyID,
                    fileNames = [],
                    secretAccessKey,
                    sessionToken,
                    bucket
                } = bosData.data;
                const resultData: IPicProps[] = [];
                await Promise.all(
                    tempFiles.map(async (item, index) => {
                        const suffix =
                            item?.filePath.substring(item?.filePath.lastIndexOf('.') + 1) || 'jpg';
                        const fileName = `${fileNames[index]}.${suffix}`;
                        try {
                            await uploadToBos({
                                // file 文件
                                filePath: item.path || item.filePath,
                                // bucket 名称
                                bucketName: bucket,
                                // 文件名
                                fileName,
                                // STS 临时秘钥
                                accessKeyID,
                                secretAccessKey,
                                sessionToken
                            });
                            resultData.push(Object.assign({}, item, {fileName}));
                        } catch (e) {
                            // 兜底接口上传
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            const resUpload: any = await uploadFileToApi(item, Conf.bucketConfName);
                            if (resUpload.data && resUpload.data.picID) {
                                const result = Object.assign(
                                    {},
                                    item,
                                    {...resUpload?.data},
                                    {fileName: resUpload?.data?.picID}
                                );
                                resultData.push(result);
                            }
                            ubcCommonViewSend({
                                value: 'uploadToBosErr',
                                ext: {
                                    info: JSON.stringify(e || '')
                                }
                            });
                        }
                    })
                );
                // 数组为空的情况，抛出异常，阻止调用 sendMsg
                resultData?.length ? res(resultData) : rej(resultData);
            } else {
                ubcCommonViewSend({
                    value: 'getBosTokenApiErr',
                    ext: {
                        info: JSON.stringify(bosErr || '')
                    }
                });
                rej(bosErr);
            }
        } catch (error) {
            console.error('上传失败, 请检查相关配置', error);
            rej(error);
        }
    });
};

/**
 * @Description 预览和上传图片 因为
 * @param {Object} Conf 用户配置
 * @param {number} Conf.count 图片数量
 * @param {number} Conf.bucketConfName bucket 名称
 * @param {number} Conf.quality 压缩质量，范围0～100，数值越小，质量越低，压缩率越高（仅对jpg有效）
 * @returns {Promise}
 */
export const preAndUploadPic = (Conf: IPicConfProps): Promise<IPicProps[]> => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (res, rej) => {
        try {
            const preData = await previewPic(Conf);
            const picData = await uploadFileToBos(preData || [], Conf);
            res(picData || []);
        } catch (error) {
            rej(error);
        }
    });
};

/**
 * @Description 预览视频
 * @param {Object} Conf 用户配置
 * @returns {Promise}
 */
export const previewVideo = async (conf: IPicConfProps) => {
    try {
        const data = await chooseVideo({
            compressed: conf.compressed || true,
            maxDuration: conf.maxDuration || 1800,
            sourceType: ['album', 'camera']
        });
        if (!data.tempFilePath) {
            showToast({title: '上传失败，请重新上传', icon: 'none'});

            return [];
        }
        const type = data.tempFilePath.split('.')?.[1];
        const file = undefined;
        const resultData: IPicProps[] = [];
        const res = Object.assign(
            {},
            {...data},
            {
                filePath: data.tempFilePath,
                type,
                thumb: DEFAULT_VIDEO_THUMB,
                originalFileObj: file
            }
        );

        resultData.push(res);

        return resultData;
    } catch (error) {
        console.error(error);

        return [];
    }
};
