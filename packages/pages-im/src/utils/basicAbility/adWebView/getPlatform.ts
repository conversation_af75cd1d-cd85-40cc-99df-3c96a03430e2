/* eslint-disable */
class PlatformApi {
    constructor() {
        // system
        this.isIOS = false;
        this.isPad = false;
        this.isAndroid = false;
        this.isSmartProgram = false;
        // 是否为iphonex以上机型
        this.isIphonexPlus = false;
        // browser
        this.isWechatApp = false;
        this.isBaiduApp = false;
        this.isWeiboApp = false;
        this.isQQApp = false;
        this.isAlipayApp = false;
        this.isMiuiBrowser = false;
        this.isUc = false;
        this.isBaidu = false;
        this.isQQ = false;
        this.isAdr = false;
        this.isSafari = false;
        this.isChrome = false;
        this.isFirefox = false;
        this.isHaokanApp = false;
        this.isMobads = false; // 网盟
        this.isBaiduhi = false;
        this.isBaiduMap = false; // 地图
        this.isBDMiniVideo = false; // 全民
        this.isBaidulite = false;
        this.isUnknowUa = false;
        // engine
        this.isTrident = false;
        this.isGecko = false;
        this.isWebkit = false;
        // os version
        this.osVersion = false;
        // 手百APP版本
        this.bdAppVersion = '';
        this.haokanAppVersion = '';
        // app frame type
        this.isEasybrowse = false;
        this.isHuaweiNative = false;
        this.isBaiduhi = false;
        this.isTieba = false;
        // 是否来自爬虫
        this.isBaiduSpider = false;
        this.isBdnetdisk = false; // 百度网盘

        this._start();
    }

    /**
     * Judge system, iOS, android
     */
    _matchOs() {
        if (this._ua().indexOf('swan-baiduboxapp') !== -1) {
            this.isSmartProgram = true;
        }
        if (/iPhone|iPad|iPod/i.test(this._ua())) {
            this.isIOS = true;
            if (/iPad/i.test(this._ua())) {
                this.isPad = true;
            }
            if (/iphone/gi.test(this._ua()) && window.screen.height >= 812) {
                this.isIphonexPlus = true;
            }
        } else if (/Android/i.test(this._ua())) {
            this.isAndroid = true;
        }
    }

    /**
     * Judge browser type
     */
    _matchBrowser() {
        const ua = this._ua()
        let uaArray = typeof ua === 'string' ? ua.split('Mobile') : [];
        let apps = uaArray && uaArray.length > 1 ? uaArray[1] : null;

        if (/\bmicromessenger\/([\d.]+)/i.test(apps)) {
            this.isWechatApp = true;
        } else if (/baiduboxapp/i.test(apps)) {
            this.isBaiduApp = true;
            // 百度极速版
            this.isBaidulite = /lite baidubox/i.test(this._ua()) || /info baidubox/i.test(this._ua());
        } else if (/haokan/i.test(this._ua())) {
            this.isHaokanApp = true;
        } else if (/weibo/i.test(apps)) {
            this.isWeiboApp = true;
        } else if (/\sQQ/i.test(apps)) {
            this.isQQApp = true;
        } else if (/\sAlipay/i.test(apps)) {
            this.isAlipayApp = true;
        } else if (/UCBrowser/i.test(this._ua())) {
            this.isUc = true;
        } else if (/Mobads/i.test(this._ua())) {
            this.isMobads = true;
        } else if (/(huaweiclt|huaweivky)/i.test(this._ua()) && !/(browser|baidu)/i.test(this._ua())) {
            this.isHuaweiNative = true;
        } else if (/baiduhi/i.test(this._ua())) {
            this.isBaiduhi = true;
        } else if (/tieba/i.test(this._ua())) {
            this.isTieba = true;
        } else if (this._ua().match(/mi\s/i) === 'mi') {
            this.isMiuiBrowser = true;
        } else if (/baidubrowser/i.test(this._ua())) {
            this.isBaidu = true;
        } else if (/qqbrowser\/([0-9.]+)/i.test(this._ua())) {
            this.isQQ = true;
        } else if (
            !/android/i.test(this._ua()) &&
            /\bversion\/([0-9.]+(?: beta)?)(?: mobile(?:\/[a-z0-9]+)?)? safari\//i.test(this._ua())
        ) {
            this.isSafari = true;
        } else if (
            /(?:Chrome|CrMo|CriOS)\/([0-9]{1,2}\.[0-9]\.[0-9]{3,4}\.[0-9]+)/i.test(this._ua()) &&
            !/samsung/i.test(this._ua())
        ) {
            this.isChrome = true;
        } else if (/(firefox|FxiOS+)\/([0-9.ab]+)/i.test(this._ua())) {
            this.isFirefox = true;
        } else if (
            /android/i.test(this._ua()) &&
            /Android[\s_\-/i686]?[\s_\-/](\d+[.\-_]\d+[.\-_]?\d*)/i.test(this._ua())
        ) {
            this.isAdr = true;
        } else if (/Baiduspider-render\/2.0/i.test(this._ua())) {
            this.isBaiduSpider = true;
        } else if (/baidumap/i.test(this._ua())) {
            this.isBaiduMap = true;
        } else if (/bdminivideo/i.test(this._ua())) {
            this.isBDMiniVideo = true;
        } else if (/netdisk/i.test(this._ua())) {
            this.isBdnetdisk = true;
        } else if (!this._ua()) {
            this.isUnknowUa = true;
        }
    }

    /**
     * Judge browser engine type
     */
    _matchEngine() {
        if (/\b(?:msie |ie |trident\/[0-9].*rv[ :])([0-9.]+)/i.test(this._ua())) {
            this.isTrident = true;
        } else if (/\brv:([\d\w.]+).*\bgecko\/(\d+)/i.test(this._ua())) {
            this.isGecko = true;
        } else if (/\bapplewebkit[/]?([0-9.+]+)/i.test(this._ua())) {
            this.isWebkit = true;
        }
    }

    /**
     * get OS version
     */
    _getOsVersion() {
        let osVersion;
        let result;
        if (this.isAndroid) {
            result = /Android ([._\d]+)/.exec(this._ua()) || /Android\/([\d.]+)/.exec(this._ua());
            if (result && result.length > 1) {
                osVersion = result[1];
            }
        } else if (this.isIOS) {
            result = /OS (\d+)_(\d+)_?(\d+)?/.exec(this._appVersion());
            if (result && result.length > 3) {
                osVersion = `${result[1]}.${result[2]}.${result[3] | 0}`;
            }
        }
        this.osVersion = osVersion;
    }

    /**
     * Wrap engine, browser, engine varible to function
     */
    _wrapFun() {
        let self = this;
        for (let key in self) {
            if (self.hasOwnProperty(key) && typeof self[key] !== 'function') {
                let handle = function (key) {
                    return key;
                }.bind(null, self[key]);
                self[key] = handle;
            }
        }
    }

    /**
     * Get user agent
     *
     * @return {string} user agent
     */
    _ua() {
        return navigator.userAgent;
    }

    /**
     * Get app version
     *
     * @return {string} app version
     */
    _appVersion() {
        return navigator.appVersion;
    }

    _getBdAppVersion() {
        const ua = this._ua();
        const oldMatch = /([\d+.]+)_(?:diordna|enohpi)_/.exec(ua);
        const match = /baiduboxapp\/([\d+.]+)/.exec(ua);
        this.bdAppVersion = match ? match[1] : oldMatch ? oldMatch[1].split('.').reverse().join('.') : '';
    }

    _getHaokanAppVersion() {
        const ua = this._ua();
        const match = /haokan\/([\d+.]+)/.exec(ua);
        this.haokanAppVersion = match && match[1];
    }

    /**
     * Get app frame type
     */
    _getFrameType() {
        if (this._ua().indexOf('light') > -1) {
            this.isEasybrowse = true;
        }
    }

    /**
     * Start match user agent
     */
    _start() {
        this._matchOs();
        this._matchBrowser();
        this._matchEngine();
        this._getOsVersion();
        this._getBdAppVersion();
        this._getHaokanAppVersion();
        this._getFrameType();
        this._wrapFun();
    }
}

export default new PlatformApi();
