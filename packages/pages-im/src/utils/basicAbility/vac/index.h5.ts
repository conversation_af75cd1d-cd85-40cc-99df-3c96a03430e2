import type { HumanVerificationParams, HumanVerificationKey, VacInstance } from './index.d';

const mkdLink = 'https://ppui-static-wap.cdn.bcebos.com/static/sdk-machine/js/mkd_v2.js';

/**
 * 渲染人机验证组件
 *
 * @param args - 验证参数
 * @param args.nodeId - 验证组件挂载的 DOM 节点 ID
 * @param args.type - 验证类型
 * @param args.verifySuccessCallback - 验证成功后的回调函数
 * @returns 返回验证实例，如果发生错误，则返回空对象
 */
const renderHumanVerification = (args: HumanVerificationParams): VacInstance => {
    try {
        const PassMkd = window?.sdkMachine;

        const instance = new PassMkd({
            container: args.nodeId,
            type: args.type,
            ak: 'wB1tRgbE0DRylZf5CitZaRrBTM1FLoz3',
            deviceType: 'pc',
            hasClose: args?.hasClose,
            initApiSuccessFn() {
                instance?.init?.();
            },
            // 验证结果的回调函数
            verifySuccessFn: data => {
                args?.verifySuccessCallback?.(data as unknown as HumanVerificationKey);
            },
            errorFn: error => {
                console.error('renderHumanVerification 出错 errorFn:', error);
                args?.errorCallback?.();
            },
            verifyFailFn: args?.verifyFailCallback,
            closeEvent: args?.closeCallback
        });

        return instance as VacInstance;
    } catch (err) {
        console.error('renderHumanVerification 出错：', err);

        return {} as VacInstance;
    }
};

/**
 * 生成人机验证实例。
 *
 * 此方法用于在浏览器环境中生成一个人机验证的实例。首先检查当前环境是否支持（即是否在浏览器中），
 * 如果支持且已加载sdkMachine，则直接渲染人机验证；否则，动态加载所需的脚本文件，并在脚本加载完成后渲染人机验证。
 *
 * @param args - 渲染人机验证所需的参数对象。
 * @returns 返回一个Promise，解析为人机验证的实例（VacInstance）。
 * @throws 如果在非浏览器环境中调用，则抛出错误。
 */
export const generateHumanVerification = (args: HumanVerificationParams): Promise<VacInstance> => {
    return new Promise(async (resolve, reject) => {
        try {
            if (document) {
                if (window?.sdkMachine) {
                    const instance = await renderHumanVerification(args);
                    resolve(instance);
                } else {
                    const script = document.createElement('script');
                    script.async = true;
                    script.src = `${mkdLink}?_=${new Date().getTime()}`;

                    document.body.appendChild(script);
                    script.onload = async function () {
                        if (window?.sdkMachine) {
                            const instance = await renderHumanVerification(args);
                            resolve(instance);
                            script.onload = null;
                        }
                    };
                }
            } else {
                reject('请在浏览器环境使用');
            }
        } catch (err) {
            reject(err);
        }
    });
};
