import {showToast} from '@tarojs/taro';
/**
 * 复制文本到剪贴板，并根据配置显示提示信息。
 *
 * @param text - 必须提供的文本字符串，将被复制到剪贴板。
 * @param options - 可选的配置对象，包含配置项。
 * @returns void
 */
export function copyTextFn(text: string, toastText?: string) {
    // 判断浏览器是否支持navigator.clipboard
    if (!navigator.clipboard) {
        const ele = document.createElement('input');
        ele.value = text;
        document.body.appendChild(ele);
        ele.select();
        document.execCommand('copy');
        if (document.execCommand('copy')) {
            showToast({title: toastText || '复制成功', icon: 'none'});
        } else {
            showToast({title: '复制失败', icon: 'none'});
        }
        document.body.removeChild(ele);
    } else {
        navigator.clipboard
            .writeText(text)
            .then(() => {
                showToast({title: toastText || '已复制', icon: 'none'});
            })
            .catch(() => {
                showToast({title: '复制失败', icon: 'none'});
            });
    }
}
