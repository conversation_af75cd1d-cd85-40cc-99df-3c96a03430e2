import {showToast, setClipboardData} from '@tarojs/taro';
/**
 * 复制文本到剪贴板，并根据配置显示提示信息。
 *
 * @param text - 必须提供的文本字符串，将被复制到剪贴板。
 * @param options - 可选的配置对象，包含配置项。
 * @returns void
 */
export function copyTextFn(text: string, toastText?: string) {
    console.log('swan');
    setClipboardData({
        data: text,
        success: () => {
            showToast({title: toastText || '复制成功', icon: 'none'});
        },
        fail: err => {
            console.error(err);
            showToast({title: '复制失败', icon: 'none'});
        }
    });
}
