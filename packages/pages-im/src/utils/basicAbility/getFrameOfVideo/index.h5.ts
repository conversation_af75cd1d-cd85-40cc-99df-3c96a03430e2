import {
    getVideoSize,
    dataURItoBlob,
    base64toFile,
    urlToBase64Async
} from '@baidu/vita-utils-shared';

export const getFrameOfVideo = ({
    filePath
}: {
    filePath: string;
}): Promise<{
    type: string;
    size: number;
    blobPic: string;
    dataUri: string;
    transType: string;
    file: File;
}> => {
    return new Promise((resolve, rej) => {
        try {
            if (!filePath || !(process.env.TARO_ENV === 'h5')) {
                process.env.TARO_ENV === 'h5'
                    ? console.error('getFirstFrameOfVideo 出错：url 为必传参数')
                    : console.error(
                        'getFirstFrameOfVideo 出错：方法使用错误，该方法仅支持 h5 使用'
                    );
                rej();

                return;
            }

            const video = document.createElement('video');
            video.src = filePath;
            video.muted = true;
            video.autoplay = true;
            video.playsInline = true;
            video.setAttribute('webkit-playsinline', 'true');
            video.setAttribute('style', 'width:0;height:0;position:fixed;right:-100%;');

            document.body.appendChild(video);

            const loadedDataPromise = new Promise<string>(resolveLoadedData => {
                video.onloadeddata = () => {
                    resolveLoadedData('loadedDataPromise');
                };
            });

            const timeoutPromise = new Promise<string>(resolveTimeout => {
                // 1s后，若未触发video.onloadeddata，则走上传默认图片逻辑
                setTimeout(() => {
                    resolveTimeout('timeoutPromise');
                }, 1000);
            });

            Promise.race([loadedDataPromise, timeoutPromise])
                .then(async r => {
                    if (r === 'loadedDataPromise') {
                        const {width, height} = getVideoSize(
                            414,
                            video.videoWidth,
                            video.videoHeight
                        );
                        const canvas = document.createElement('canvas');

                        canvas.width = width;
                        canvas.height = height;

                        canvas.getContext('2d')?.drawImage(video, 0, 0, width, height);

                        // 视频转换为 base 64图片
                        const res = canvas.toDataURL('image/png');
                        const blobRes = dataURItoBlob(res);
                        // 清除element
                        if (document.body.contains(video)) {
                            document.body.removeChild(video);
                        }

                        // base 64图片转 blob 预览
                        const blobPic = window.URL.createObjectURL(blobRes);
                        resolve({
                            dataUri: res,
                            blobPic,
                            transType: 'base64',
                            size: blobRes?.size,
                            type: blobRes?.type || '',
                            file: base64toFile(res, 'blobRes?.type')
                        });
                    } else if (r === 'timeoutPromise') {
                        const defaultImageUrl =
                            'https://med-fe.cdn.bcebos.com/wz-mini/video_cover.png';

                        const res = await urlToBase64Async(defaultImageUrl);
                        const blobRes = dataURItoBlob(res);

                        // 清除element
                        if (document.body.contains(video)) {
                            document.body.removeChild(video);
                        }

                        // base 64图片转 blob 预览
                        const blobPic = window.URL.createObjectURL(blobRes);

                        resolve({
                            dataUri: res,
                            blobPic,
                            transType: 'base64',
                            size: blobRes?.size,
                            type: blobRes?.type || '',
                            file: base64toFile(res, 'blobRes?.type')
                        });
                    }
                })
                .catch(() => {
                    if (document.body.contains(video)) {
                        document.body.removeChild(video);
                    }
                    rej();
                });
            video.onabort = () => {
                if (document.body.contains(video)) {
                    document.body.removeChild(video);
                }
                rej();
            };
        } catch (err) {
            console.error('getFirstFrameOfVideo 出错：', err);
            rej(err);
        }
    });
};
