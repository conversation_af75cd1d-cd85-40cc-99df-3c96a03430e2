// eslint-disable-next-line
export function noop() {}

/**
 * 判断当前页面是否是在PC环境打开
 * @returns bolean
 */
export const isPcPage = () => {
    const {userAgent} = window.navigator;

    // 使用用户代理检测判断设备类型
    // eslint-disable-next-line max-len
    return !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|SymbianOS|Windows Phone|ArkWeb|UCBrowser/i.test(
        userAgent
    );
};

/**
 * 判断当前浏览器是否是safari
 * @returns bolean
 */
export const isSafari = () => {
    const ua = navigator.userAgent.toLowerCase();
    if (
        ua.indexOf('applewebkit') > -1 &&
        ua.indexOf('mobile') > -1 &&
        ua.indexOf('safari') > -1 &&
        ua.indexOf('linux') === -1 &&
        ua.indexOf('android') === -1 &&
        ua.indexOf('chrome') === -1 &&
        ua.indexOf('ios') === -1 &&
        ua.indexOf('browser') === -1
    ) {
        return true;
    }

    return false;
};

/**
 * 判断当前浏览器是否是Android
 * @returns bolean
 */
export const isAndroid = () => {
    const ua = navigator.userAgent.toLowerCase();
    if (ua.indexOf('android') > -1 || ua.indexOf('adr') > -1) {
        return true;
    }

    return false;
};

/**
 * 用于检测当前用户是否在使用微信浏览器（WeChat Browser）
 * @returns bolean
 */
export const isWeixnH5 = () => {
    const ua = navigator?.userAgent?.toLowerCase();
    const isWeixin = ua?.indexOf('micromessenger') !== -1;
    if (isWeixin) {
        return true;
    }

    return false;
};

export const inBrowser = typeof document !== 'undefined' && !!document.scripts;
export const inH5 = process.env.TARO_ENV === 'h5';
export const inWechat = process.env.TARO_ENV === 'weapp';
export const inSwan = process.env.TARO_ENV === 'swan';
export const inAlipay = process.env.TARO_ENV === 'alipay';
export const inQQ = process.env.TARO_ENV === 'qq';
export const inToutiao = process.env.TARO_ENV === 'tt';
export const isDev = process.env.NODE_ENV === 'development';
// export const inWxWenzhen = process.env.WX_APP_ENV === 'wenzhen';
// export const inWxJiayi = process.env.WX_APP_ENV === 'jiayi';
// // 海南年检
// export const inNianJianHn = process.env.WX_APP_ENV === 'njhn';
// // 银川年检
// export const inNianJianYc = process.env.WX_APP_ENV === 'njyc';
export const inPc = isPcPage;
export const inSafari = isSafari;
export const inWeixnH5 = isWeixnH5;
