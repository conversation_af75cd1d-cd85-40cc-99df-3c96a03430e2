/**
 * <AUTHOR> 张二义
 * @Date         : 2022-03-08 17:43:35
 * @Description  : taro h5存储的storage格式为 {data: val} 与mars无法打平 所以打平一下
 */

/**
 * 执行回调
 *
 * @param {Function} fn 回调函数
 * @param {Object} data 参数
 */
import Taro from '@tarojs/taro';

function callback(fn, ...args) {
    typeof fn === 'function' && fn(args);
}

const cookieCache: unknown[] = [];

function setCookie(cname: string, cvalue: string, exdays = 10) {
    const d = new Date();
    d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
    const expires = `expires=${d.toUTCString()}`;
    document.cookie = `${cname}=${cvalue}; ${expires}`;

    // 缓存cookie
    cookieCache.push(cname);
}

function getCookie(cname: string) {
    const name = `${cname}=`;
    const ca = document.cookie.split(';');

    for (let i = 0; i < ca.length; i++) {
        const c = ca[i].trim();

        if (c.indexOf(name) === 0) {
            return c.substring(name.length, c.length);
        }
    }

    return '';
}

function getCookieKeys() {
    const keys: string[] = [];
    const ca = document.cookie.split(';');

    for (let i = 0; i < ca.length; i++) {
        const pair = ca[i].trim();

        keys.push(pair.split('=')[0]);
    }

    return keys;
}

function removeCookie(cname) {
    setCookie(cname, '', -1);
}

function clearCookie() {
    for (const cname of cookieCache) {
        removeCookie(cname);
    }
}

export function setStorageSync(key, data) {
    const dataStr = JSON.stringify(data || '');

    if (typeof localStorage !== 'undefined') {
        localStorage.setItem(key, dataStr);
    } else {
        setCookie(key, dataStr);
    }
}

export function setStorage(options = {}) {
    const { key, data, success, fail, complete } = options as Taro.setStorage.Option;

    return new Promise((resolve, reject) => {
        let info = { status: 0, message: 'success' };

        try {
            setStorageSync(key, data);
        } catch (e) {
            info = { status: -1, message: e };
        }

        if (info.status === 0) {
            callback(success, info);
            callback(complete, info);
            resolve(info);
        } else {
            callback(fail, info);
            callback(complete, info);
            reject(info);
        }
    });
}

export function getStorageSync(key) {
    let info = '';

    if (typeof localStorage !== 'undefined') {
        info = localStorage.getItem(key) || '';
    } else {
        info = getCookie(key);
    }
    try {
        info && (info = JSON.parse(info));
    } catch (error) {
        console.error(error);
    }

    return info;
}

export function getStorage(options = {}) {
    const { key, success, fail, complete } = options as Taro.setStorage.Option;

    return new Promise((resolve, reject) => {
        let info = { status: 0, message: 'success', data: {} };

        try {
            info.data = getStorageSync(key);
        } catch (e) {
            info = { status: -1, message: e, data: {} };
        }

        if (info.status === 0) {
            callback(success, info);
            callback(complete, info);
            resolve(info);
        } else {
            callback(fail, info);
            callback(complete, info);
            reject(info);
        }
    });
}

export interface GetStorageInfoSync extends Taro.getStorageInfoSync.Option {
    currentSize: number;
    keys: string[];
    limitSize: number;
}

export function getStorageInfoSync() {
    const info = {
        currentSize: 0,
        keys: [],
        limitSize: NaN
    } as GetStorageInfoSync;

    if (typeof localStorage !== 'undefined') {
        for (const key in localStorage) {
            if (Object.prototype.hasOwnProperty.call(localStorage, key)) {
                info.keys.push(key);
                info.currentSize += (localStorage.getItem(key) || '').length * 2; // 一个字符占2个字节
            }
        }
    } else {
        info.currentSize = document.cookie.length * 2;
        info.keys = getCookieKeys();
        info.limitSize = 4 * 1024; // 4K
    }

    return info;
}

export interface GetStorageOptions extends GetStorageInfoSync {
    status?: number;
}

export function getStorageInfo(options = {}) {
    const { success, fail, complete } = options as Taro.getStorageInfo.Option;

    return new Promise((resolve, reject) => {
        let info = {} as GetStorageOptions;

        try {
            info = getStorageInfoSync();
        } catch (e) {
            info = e;
            info.status = -1;
        }

        if (info.status === -1) {
            callback(fail, info);
            callback(complete, info);
            reject(info);
        } else {
            callback(success, info);
            callback(complete, info);
            resolve(info);
        }
    });
}

export function removeStorageSync(key) {
    if (typeof localStorage !== 'undefined') {
        localStorage.removeItem(key);
    } else {
        removeCookie(key);
    }

    return true;
}

export function removeStorage(options = {}) {
    const { key, success, complete } = options as Taro.removeStorage.Option;

    removeStorageSync(key);

    callback(success);
    callback(complete);

    return Promise.resolve();
}

export function clearStorageSync() {
    if (typeof localStorage !== 'undefined') {
        localStorage.clear();
    } else {
        clearCookie();
    }

    return true;
}

export function clearStorage(options = {}) {
    const { success, complete } = options as Taro.clearStorage.Option;
    clearStorageSync();
    callback(success);
    callback(complete);

    return Promise.resolve();
}

export default {
    set: setStorageSync,
    get: getStorageSync,
    remove: removeStorageSync,
    setStorage,
    setStorageSync,
    getStorage,
    getStorageSync,
    getStorageInfo,
    getStorageInfoSync,
    removeStorageSync,
    removeStorage
};
