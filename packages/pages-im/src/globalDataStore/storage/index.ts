/**
 * <AUTHOR> 张二义
 * @Date         : 2022-03-08 17:43:35
 * @Description  : taro h5存储的storage格式为 {data: val} 与mars无法打平 所以打平一下
 */

/**
 * <AUTHOR> 张二义
 * @Date         : 2022-03-08 17:43:35
 * @Description  : taro h5存储的storage格式为 {data: val} 与mars无法打平 所以打平一下
 */

import Taro from '@tarojs/taro';

export default {
    set: Taro.setStorageSync,
    get: Taro.getStorageSync,
    remove: Taro.removeStorageSync,
    setStorage: Taro.setStorage,
    setStorageSync: Taro.setStorageSync,
    getStorage: Taro.getStorage,
    getStorageSync: Taro.getStorageSync,
    getStorageInfo: Taro.getStorageInfo,
    getStorageInfoSync: Taro.getStorageInfoSync,
    removeStorageSync: Taro.removeStorageSync,
    removeStorage: Taro.removeStorage
};
