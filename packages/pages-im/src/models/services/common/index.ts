import {uploadFile} from '@tarojs/taro';
import httpRequest from '../../../utils/basicAbility/comonRequest/common';
import {getWxLoginToken} from '../../../utils/generalFunction/wxLoginToken';
import type {
    IReqConfProps,
    GetCouponsByCodeParams,
    GetCouponsByCodeResponse,
    GetPlanedCouponCodeParams,
    GetPlanedCouponCodeResponse,
    GetPatientBlockParams,
    GetPatientBlockResponse,
    IRequestDataProps,
    GetCalcCouponsParams
} from '../../../typings/service';
import {getBucketToken, uploadImageApi} from '../../apis/vtui';
import {
    calcFormCoupons as calcFormCouponsApi,
    getCouponsByCode as getCouponsByCodeApi,
    getComment as getCommentApi,
    getPlanedCouponCode as getPlanedCouponCode<PERSON>pi,
    getPatient<PERSON>lock as getPatient<PERSON>lock<PERSON><PERSON>
} from '../../../models/apis/uncategorized';
import {API_HOST} from '../../../models/apis/host';

export interface IBucketDataProps {
    accessKeyID?: string;
    secretAccessKey?: string;
    sessionToken?: string;
    bucket?: string;
    endPoint?: string;
    fileNames?: string[];
    snapshotURLs?: string[];
    mapFileUrls?: Record<string, Record<'icon' | 'origin' | 'small', string>>;
}

export interface IUploadDataProps {
    picID?: string;
    origin?: string;
    small?: string;
    icon?: string;
    width?: number;
    height?: number;
}

export interface IHeadProps {
    avatarUrl?: string;
    uname?: string;
}

export interface WxAccountBindResp {
    isBind: boolean;
}

export interface WxAccountBindReq {
    wxAppKey: 'netHospitalService' | 'netHospitalSwan';
}

export interface BindWxAccountReq {
    appId: string;
    code: string;
}

export interface UseprotocolListResp {
    /**
     * 顶部
     */
    head?: {
        title?: string;
    };

    /**
     * 提示
     */
    tip?: string;

    /**
     * 协议列表
     */
    agreeList?: AgreementGroupItem[];
}

export interface CreateWxCodeRes {
    expireTime: string;
    imageStr: string;
    url: string;
}

export interface AgreementGroupItem {
    title: string;
    item: AgreementEntryItem[];
}

export interface AgreementEntryItem {
    title: string;
    unifiUrl: string;
    urlType: string;
}

// https://yapi.baidu-int.com/project/12941/interface/api/680528
export interface IPatientInfoProps {
    age: string;
    ageMonth?: string;
    birthday: string;
    improved?: number;
    improvedDesc?: string;
    isCertified?: number;
    isCertifiedDesc?: string;
    mobile?: string;
    name: string;
    patientId: string;
    relationship?: string;
    sex: string;
}

export interface IPatientListProps {
    patientList: IPatientInfoProps[];
}

export interface GetShortSwapLongParams {
    scene: string;
}

export const getUseprotocolDetail = async (conf: IReqConfProps) => {
    return await httpRequest<{verified: number}>(conf);
};

export const calcFormCoupons = async (params: GetCalcCouponsParams) => {
    return await httpRequest<IRequestDataProps>({
        url: `${API_HOST}${calcFormCouponsApi}`,
        method: 'POST',
        data: params
    });
};

/**
 *
 * @description 通过领取 code 领取优惠劵
 * @param params {GetCouponsByCodeParams}
 * @returns
 */
export const getCouponsByCode = async (params: GetCouponsByCodeParams) => {
    return await httpRequest<GetCouponsByCodeResponse>({
        url: `${API_HOST}${getCouponsByCodeApi}`,
        data: params,
        isNeedLogin: true,
        isFirstScreen: false
    });
};

/**
 *
 * @description 获取上传图片token
 * @param params
 * @returns
 */
export const getBosToken = async (params: {bucketConfName?: string; fileNum?: number}) => {
    return await httpRequest<IBucketDataProps>({
        url: `${API_HOST}${getBucketToken}`,
        method: 'POST',
        data: params,
        isNeedLogin: false,
        isFirstScreen: false
    });
};

export const uploadPicToApiService = async (filePath: string, bucketConfName = 'default') => {
    return new Promise((resolve, reject) => {
        uploadFile({
            url: `${API_HOST}${uploadImageApi}?bucketConfName=${bucketConfName}`,
            filePath,
            name: 'file',
            success: res => {
                resolve(res?.data);
            },
            fail: err => {
                reject(err);
            }
        });
    });
};

// 获取评价信息
export const getComment = async (params: unknown) => {
    return await httpRequest({
        url: `${API_HOST}${getCommentApi}`,
        method: 'POST',
        data: params,
        isNeedLogin: true,
        isFirstScreen: false
    });
};

// 根据触发点请求发券码
export const getPlanedCouponCode = async (params: GetPlanedCouponCodeParams) => {
    return await httpRequest<GetPlanedCouponCodeResponse>({
        url: `${API_HOST}${getPlanedCouponCodeApi}`,
        method: 'GET',
        data: params
    });
};

// 获取医生拉黑患者信息
export const getPatientBlock = async (params: GetPatientBlockParams) => {
    return await httpRequest<GetPatientBlockResponse>({
        url: `${API_HOST}${getPatientBlockApi}`,
        // url: `https://yapi.baidu-int.com/mock/19267/wzcui/uiservice/common/user/blacklist`,
        method: 'POST',
        data: params
    });
};

/**
 * @description 获取用户微信绑定状态
 * @returns {Promise<GetServiceInfoRes>}
 */
export const checkWxAccountBind = async (params: WxAccountBindReq) => {
    let header = {};
    if (process.env.TARO_ENV === 'weapp') {
        const data = await getWxLoginToken();

        header =
            data?.tokenName && data?.token
                ? {
                    Cookie: `${data.tokenName}=${data.token};`
                }
                : {};
    }

    return httpRequest<WxAccountBindResp>({
        // url: 'https://jiankang.baidu.com/wx-user/bind/check',
        url: `${API_HOST}/wx-user/bind/check`,
        method: 'GET',
        data: params,
        isNeedLogin: false,
        isFirstScreen: false,
        header
    });
};
