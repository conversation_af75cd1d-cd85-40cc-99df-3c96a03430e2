import { API_HOST } from '../../../models/apis/host';
import httpRequest from '../../../utils/basicAbility/comonRequest/common';

interface AIVoiceToken {
    expiresIn: number;
    tmpSecretId: string;
    tmpSecretKey: string;
    token: string;
    ttl: number;
}

interface postAudioTokenParams {
    data: {
        appid: string;
    };
}

/**
 *
 * @description 获取腾讯云语音token
 * @returns {Promise<GetRecomListResponse>}
 */
export const postAudioToken = (params: postAudioTokenParams) => {
    return httpRequest<AIVoiceToken>({
        url: `${API_HOST}/avatar-sdk/asr/token4wx`,
        method: 'POST',
        data: params,
        isNeedLogin: false,
        isFirstScreen: true
    });
};

/**
 *
 * @description 获取腾讯云语音token
 * @returns {Promise<GetRecomListResponse>}
 */
export const postNeedLoginAudioToken = (params: postAudioTokenParams) => {
    return httpRequest<AIVoiceToken>({
        url: `${API_HOST}/avatar-sdk/asr/token4wx`,
        method: 'POST',
        data: params,
        isNeedLogin: true,
        isFirstScreen: true
    });
};
