import type {MsgId} from '@/typings';
import type {SessionId} from '../../../../store/triageStreamAtom/index.type.ts';

export type SceneTypeOfParams =
    | 'wzBotConversation'
    | `wzBotCapsule_${string}`
    | 'wzBotFreeService'
    | `imWelcomeModule_${string}`
    | 'inputAudio'
    | 'inputImg'
    | 'imFlowOptions'
    | 'wzBotFreeService'
    | 'queryFirstCall'
    | 'defaultFirstCall'
    | 'unknown'
    | 'uploadImg'
    | 'submitAiForm';

export type InputImgMap = {
    text: string;
    images: [
        {
            value: string;
            path?: string;
            width?: number;
            height?: number;
        }
    ];
};

export interface ConversationSSEParams {
    params: {
        chatData: {
            sessionId: SessionId;
            sceneType: SceneTypeOfParams;
        };
        msg: {
            payload: {
                msgKey: string;
                content: string | undefined | InputImgMap;
                contentType?: number;
                origin?: string; // 原尺寸图片链接
            }[];
        };
        ctrlData?: {
            firstCall?: boolean;
            tmpBotVersion2?: 1 | 0; // 是否为新版本，用于 cbot2.0 上线过程兼容；
            pageFrom?: 'sessionList' | 'pageRefresh';
            ctrlType?: string;
        };
        formSubmitData?: {
            msgId: MsgId;
            fields: {
                key: string;
                value: string;
            }[];
        };
    };
    ops: {
        onComplete: (res: unknown) => void;
        onError: (error: Error) => void;
        onHeadersReceived: (headers: Headers) => void;
    };
}
