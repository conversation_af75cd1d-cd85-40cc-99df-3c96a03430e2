import {Base64} from 'js-base64';
import {SSEProcessor} from 'sse-kit/lib/bundle.swan.esm';

import {API_HOST} from '../../../../models/apis/host';
import {createTriageStreamConversation} from '../../../apis/vtui';

import editConf from '../../../../utils/basicAbility/comonRequest/utils/editConf';
import type {SSEResponseType} from '../../../../store/triageStreamAtom/index.type.ts';

import type {ConversationSSEParams} from './index.d';

export type SSEProcessorInstance<T extends object> = InstanceType<typeof SSEProcessor<T>>;

export const conversationSSE = async (
    args: ConversationSSEParams
): Promise<SSEProcessorInstance<SSEResponseType>> => {
    const {params, ops} = args;

    const conf = {
        url: `${API_HOST}${createTriageStreamConversation}?_format=base64`,
        method: 'POST' as const,
        data: {...params},
        needTransparentWhiteListParams: true
    };
    const decoratedConf = await editConf(conf);

    return new SSEProcessor<SSEResponseType>({
        url: decoratedConf.url as `https://${string}`,
        method: 'POST',
        enableConsole: false,
        headers: decoratedConf.header as Headers,
        reqParams: decoratedConf.data,
        onComplete: ops.onComplete,
        onError: ops.onError,
        onHeadersReceived: ops.onHeadersReceived,
        preprocessDataCallback
    });
};

function decodeBase64Segments(input: string): string {
    try {
        return Base64.decode(input);
    } catch (error) {
        console.error('解码出错，返回原始匹配内容:', input, error);

        return input;
    }
}

function preprocessDataCallback(input: string): string {
    if (input.startsWith('data:')) {
        const line = decodeBase64Segments(input.replace(/^data:/, '').trim());

        return `data: ${line}`;
    }

    return input;
}
