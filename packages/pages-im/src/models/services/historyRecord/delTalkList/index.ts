import httpRequest from '../../../../utils/basicAbility/comonRequest/cui';
import {API_HOST} from '../../../../models/apis/host';

import type {DelTalkParams, DelTalkResponse} from './index.d';

/**
 * 删除对话
 * @param talkGroupId - 对话组ID
 * @returns Promise<void>
 */
export const deleteTalk = (data: DelTalkParams) => {
    return httpRequest<DelTalkResponse>({
        url: `${API_HOST}/vtui/session/delete`,
        method: 'POST',
        data,
        isNeedLogin: false
    });
};
