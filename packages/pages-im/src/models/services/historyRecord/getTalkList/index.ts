import httpRequest from '../../../../utils/basicAbility/comonRequest/cui';
import {API_HOST} from '../../../../models/apis/host';

import type {TalkListParams, TalkListResponse} from './index.d';

/**
 * 获取对话列表
 * @param params - 请求参数
 * @returns Promise<TalkListResponse>
 */
export const getTalkList = (params: TalkListParams) => {
    return httpRequest<TalkListResponse>({
        url: `${API_HOST}/vtui/session/list`,
        method: 'POST',
        data: params,
        isNeedLogin: true,
        isFirstScreen: false
    });
};
