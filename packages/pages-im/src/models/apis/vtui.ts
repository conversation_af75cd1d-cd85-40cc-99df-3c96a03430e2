/**
 *
 * @link https://yapi.baidu-int.com/project/33685/interface/api/1908809
 * @description 服务列表算价接口，返回整张卡片数据
 */
export const calcCoupons = '/wzcui/uiservice/zhenqian/zhusu/calcCoupons';

/**
 *
 * @link https://yapi.baidu-int.com/project/32495/interface/api/1840666
 * @description 获取Bos Bucket的STS
 */
export const getBucketToken = '/wzcui/uiservice/common/file/getBucketToken';

/**
 *
 * @link https://yapi.baidu-int.com/project/32495/interface/api/1840671
 * @description 上传图片接口
 */
export const uploadImageApi = '/wzcui/uiservice/common/file/uploadImage';

/**
 *
 * <AUTHOR>
 * @link
 * @description 获取全局通知
 */
export const getGlobalNotice = '/wzcui/uiservice/common/user/globalnotice';

/**
 *
 * <AUTHOR>
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4590249
 * @description 获取诊前 Stream 页面核心数据V2
 */
export const getTriageStreamCore = '/vtui/conversation/coredata';

/**
 *
 * <AUTHOR>
 * @link 待补充
 * @description 获取诊前 Stream 页面非核心数据
 */
export const getOtherData = '/vtui/conversation/otherdata';

/**
 *
 * <AUTHOR>
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4590249
 * @description 诊前 Stream 页面创建会话接口，SSE 流式传输
 */
export const createTriageStreamConversation = '/vtui/conversation/msg';

/**
 *
 * <AUTHOR>
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4590249
 * @description 用户通用行为
 */

export const getUseraction = '/vtui/useraction/commonaction';

/**
 *
 * <AUTHOR>
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4590249
 * @description 用户业务行为
 */
export const getUserBizAction = '/vtui/useraction/bizaction';

/**
 *
 * <AUTHOR>
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4655784
 * @description 诊前 Stream 页面获取会话消息
 */
export const getSessionMsgList = '/vtui/message/list';
