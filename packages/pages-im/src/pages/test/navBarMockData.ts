export const navBarData = {
    topTips: {
        renderType: 'orderGuideTip',
        progress: [
            {
                text: '描述问题',
                status: 1
            },
            {
                text: '购买服务',
                status: 0
            },
            {
                text: '等待回复',
                status: 0
            },
            {
                text: '进入咨询',
                status: 0
            }
        ],
        orderGuideTip: {
            titleInfo: {
                avatar: 'https://med-fe.cdn.bcebos.com/wz%2FauthoriseTitleIcon.png',
                title: '',
                actionInfo: {
                    interaction: 'openLink',
                    interactionInfo: {
                        url: '/pages/act/specialCenter/index?appid=10077'
                    }
                }
            },
            viewOrderInfo: {
                avatar: 'https://med-fe.cdn.bcebos.com/wz%2FconsultHistory.png',
                title: '我的订单',
                actionInfo: {
                    value: '',
                    disabled: false,
                    interaction: 'openLink',
                    interactionInfo: {
                        url: '/wenzhen/pages/order/list/index'
                    }
                }
            }
        }
    }
};
