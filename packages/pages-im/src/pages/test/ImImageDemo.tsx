import {ImImage} from '@baidu/vita-ui-cards-common';
import {View} from '@tarojs/components';

const img1 = 'https://muzhi-fe.cdn.bcebos.com/hospital-mp/tool-image2.png';
const img2 = 'https://muzhi-fe.cdn.bcebos.com/hospital-mp/tool-image6.png';
const img3 = 'https://muzhi-fe.cdn.bcebos.com/hospital-mp/tool-image4.png';
const img4 = 'https://muzhi-fe.bj.bcebos.com/hospital-mp/history-jc-wyf.png';
// const img5 = 'https://muzhi-fe.cdn.bcebos.com/hospital-mp/tool-image5.png';
const img6 = 'https://muzhi-fe.cdn.bcebos.com/hospital-mp/107564b4c69deb5c68e0fa24f8c24c85.jpg';
const img7 =
    'https://appcms.cdn.bcebos.com/appcms/2022-10/921630fda449cae62411925b5022137f.png?x-bce-process=image/auto-orient,o_1/resize,w_1242,limit_1/quality,Q_85/format,f_auto';
export default function ImImageDemo() {
    const data1 = {
        cardStyle: {
            width: 465,
            height: 465
        },
        content: {
            value: img1,
            origin: img1,
            small: img1
        }
    };
    const data2 = {
        cardStyle: {
            width: 360,
            height: 360
        },
        content: {
            icon: img2,
            origin: img2,
            small: img2
        }
    };
    const data3 = {
        content: {
            icon: img3,
            origin: img3,
            small: img3
        }
    };
    const data4 = {
        content: {
            icon: img4,
            origin: img4,
            small: img4
        }
    };
    const data5 = {
        content: {
            icon: img6,
            origin: img6,
            small: img6
        }
    };
    const data7 = {
        content: {
            icon: img7,
            origin: img7,
            small: img7
        }
    };

    return (
        <View>
            <View className='wz-fs-48 wz-fw-500'>ImImage</View>
            <View className='wz-mt-30'>
                <View>IM流图片</View>
                <ImImage data={data1} />
            </View>
            <View className='wz-mt-30'>
                <View>最小比例 360/360</View>
                <ImImage data={data2} />
            </View>
            <View className='wz-mt-30'>
                <View>图片竖版长图</View>
                <ImImage data={data3} />
            </View>
            <View className='wz-mt-30'>
                <View>图片横版长图 & 背景色</View>
                <ImImage data={data7} bgColor='#00c8c8' borderRadius={27} />
            </View>
            <View className='wz-mt-30'>
                <View>图片隐私</View>
                <ImImage data={data5} isPrivate />
            </View>
            <View className='wz-mt-30'>
                <View>图片隐私长图</View>
                <ImImage data={data4} isPrivate />
            </View>
            <View className='wz-mt-30'>
                <View>图片上传中</View>
                <ImImage data={data5} showloading />
            </View>
        </View>
    );
}
