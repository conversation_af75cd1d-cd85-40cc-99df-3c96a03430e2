import cx from 'classnames';
import {ScrollView, View} from '@tarojs/components';
import {throttle} from 'lodash';
import {memo, type FC, useRef, useState, useEffect, useMemo, useCallback} from 'react';
import {nextTick, eventCenter} from '@tarojs/taro';
import {
    useGetSessionMsgIds,
    useGetTextareaFocusType
} from '../../../../hooks/triageStream/dataController';
import {useScrollControl} from '../../../../hooks/triageStream/useScrollControl';

import type {MsgId} from '../../../../typings';
import HistoryBtn from '../HistoryBtn';
import SessionMsgItem from '../MsgItem';
import {ScrollAnchor} from '../ScrollToBottomBtn';
import {SCROLL_ANIMATION_DISABLE_ONCE} from '../../../../constants/common';

import styles from './index.module.less';

const MSG_CONTAINER_ID = 'msgContainer';
const SCROLL_VIEW_ID = 'sessionContentWrapper';
const BOTTOM_EMPTY_DOM_ID = 'bottomEmptyDom';
const SCROLL_ANIMATION_DURATION = '300';
// 触摸移动的偏移量阈值，超过此值才认为是用户手动滚动
const TOUCH_MOVE_THRESHOLD = 10;

/**
 * 会话内容组件
 *
 * @returns 返回会话内容的 JSX 元素
 */
const SessionContent: FC = () => {
    const [firstMsgId, setFirstMsgId] = useState<MsgId | undefined>(undefined);
    const [latestMsgId, setLatestMsgId] = useState<MsgId | undefined>(undefined);
    const [scrollWithAnimation, setScrollWithAnimation] = useState<boolean>(true);

    const {msgIds} = useGetSessionMsgIds();
    const {updateTriageTextareaNotFocus} = useGetTextareaFocusType();
    const {onScroll} = useScrollControl();

    // 是否用户手动滚动, 设置为 滚动优先级最高的行为，此时不执行代码里边触发的滚动
    const isTouching = useRef(false);
    // 记录触摸起始的 Y 坐标
    const touchStartY = useRef(0);

    // 生成会话消息组件
    const genMsg = useMemo(() => {
        if (!msgIds.length) return null;

        setFirstMsgId(msgIds[0]);
        const lastId = [...msgIds].pop();

        return msgIds.map(msgId => {
            return <SessionMsgItem key={msgId} msgId={msgId} isLatest={lastId === msgId} />;
        });
    }, [msgIds]);

    const setUserScroll = throttle(type => {
        isTouching.current = type;
    }, 200);

    useEffect(() => {
        // msgIds 空数组可能是切换session 或是初始化，基于此重置用户触摸状态
        if (msgIds?.length === 0) {
            setUserScroll(false);
        }
    }, [msgIds, setUserScroll]);

    useEffect(() => {
        // 需要跳过自动滚动的符号列表
        const SKIP_SCROLL_SYMBOLS = ['dom_height_observer', 'msg-item-scrollToBottom'];
        onScroll(event => {
            setLatestMsgId(undefined);

            // eslint-disable-next-line no-console
            process.env.NODE_ENV === 'development' &&
                console.info(
                    'Event: 滚动事件触发，source:',
                    event,
                    'isTouching:',
                    isTouching.current
                );

            if (isTouching.current && event.symbol && SKIP_SCROLL_SYMBOLS.includes(event.symbol)) {
                return;
            }

            if (!event.enabled) {
                // 禁止滚动
                setUserScroll(true);
                return;
            }

            if (event.msgId) {
                nextTick(() => {
                    setLatestMsgId(event.msgId);
                    // scrollIntoView，需要延迟一下重新置成undefined，否则dom变化会重新回到底部
                    nextTick(() => {
                        setLatestMsgId(undefined);
                        setScrollWithAnimation(true);
                    });
                });
            } else {
                nextTick(() => {
                    setLatestMsgId('bottomEmptyDom');
                    // scrollIntoView，需要延迟一下重新置成undefined，否则dom变化会重新回到底部
                    nextTick(() => {
                        setLatestMsgId(undefined);
                        setScrollWithAnimation(true);
                    });
                });
            }

            // 发送消息触发滚动到底部重置触摸态
            if (event.symbol === 'user_send_msg_to_wall') {
                setUserScroll(false);
            }
        });

        // 用于支持部分场景下禁止滚动动画，如：历史消息加载后恢复位置场景
        const handleScrollAnimationDisableOnce = (disable: boolean) => {
            setScrollWithAnimation(!disable);
        };
        eventCenter.on(SCROLL_ANIMATION_DISABLE_ONCE, handleScrollAnimationDisableOnce);

        return () => {
            eventCenter.off(SCROLL_ANIMATION_DISABLE_ONCE, handleScrollAnimationDisableOnce);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [onScroll]);

    // 判断是否禁止自动滚动 ios 手百得用start move有延迟
    const onTouchStart = useCallback(e => {
        const touches = e.touches[0];
        touchStartY.current = touches.clientY;
    }, []);

    const onTouchEnd = useCallback(
        e => {
            e.stopPropagation();
            // 有一种场景第一次进来，搓一搓屏幕，会触发不自动滚动，这里需要特殊处理
            if (msgIds.length < 3) {
                return;
            }
            const endY = e.changedTouches[0].clientY;
            // 当手指在屏幕上的位置和开始的位置不一致时，说明此时是用户手动滚动
            // 添加偏移量阈值，避免点击事件被误判为滚动
            if (
                touchStartY.current &&
                endY &&
                Math.abs(+touchStartY.current - +endY) > TOUCH_MOVE_THRESHOLD
            ) {
                setUserScroll(true);
            }
        },
        [setUserScroll, msgIds]
    );

    const onTouchMove = useCallback(() => {
        // 有一种场景第一次进来，搓一搓屏幕，会触发不自动滚动，这里需要特殊处理
        if (msgIds.length < 3) {
            return;
        }
        setUserScroll(true);
    }, [setUserScroll, msgIds]);

    const onScrollToLower = useCallback(() => {
        setUserScroll(false);
    }, [setUserScroll]);

    return (
        <View
            style={{
                flex: 1,
                height: 1
            }}
        >
            <ScrollView
                id={SCROLL_VIEW_ID}
                scrollY
                enhanced
                enableFlex
                scrollAnchoring
                fastDeceleration
                scrollWithAnimation={scrollWithAnimation}
                scrollAnimationDuration={SCROLL_ANIMATION_DURATION}
                enablePassive
                upperThreshold={70}
                lowerThreshold={0}
                showScrollbar={false}
                enableBackToTop={false}
                onTouchStart={onTouchStart}
                onTouchEnd={onTouchEnd}
                onTouchMove={() => {
                    updateTriageTextareaNotFocus();
                    onTouchMove();
                }}
                onScrollToLower={onScrollToLower}
                scrollIntoView={latestMsgId}
                className={cx(
                    styles.sessionContentWrapper,
                    process.env.TARO_ENV === 'h5' ? styles.sessionContentWrapperOfH5 : ''
                )}
            >
                <View id={MSG_CONTAINER_ID} className='wz-plr-36'>
                    <HistoryBtn msgId={firstMsgId} />
                    {/* 会话内容 */}
                    {genMsg}
                    <View className={styles.scrollAnchorContainer}>
                        <ScrollAnchor
                            swanRelativeSelector={`#${SCROLL_VIEW_ID}`}
                            className={styles.scrollAnchor}
                        />
                    </View>
                    <View id={BOTTOM_EMPTY_DOM_ID} className={styles.bottomEmptyDom} />
                </View>
            </ScrollView>
        </View>
    );
};

export default memo(SessionContent);
