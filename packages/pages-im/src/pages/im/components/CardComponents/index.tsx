import cx from 'classnames';
import {View} from '@tarojs/components';
import {memo, useMemo, type FC, useCallback} from 'react';
import {ImImage, ImFlow, ImSystem, ImFormCard} from '@baidu/vita-ui-cards-common';
import {Im<PERSON><PERSON><PERSON>, <PERSON>m<PERSON>om<PERSON>, ImFocusDoctor} from '@baidu/wz-taro-tools-core';

import {
    MSG_CARDID_TYPE,
    MSG_CARDID_ENUM,
    MSG_CARDID_ENUM_STREAM,
    MSG_CARDID_ENUM_STREAM_TYPE,
    NEW_SERVICECARD_RENDER_TYPE
} from '../../../../constants/msg';

import ImRichMsg from '../../../../components/ImRichMsg';
import ImThinking from '../../../../components/ImThinking';
import ImWelcomeModule from '../../../../components/ImWelcomeModule';
import StreamFocusService from '../../../../components/StreamFocusService';

import ImCollectedAndReExpert from '../../../../components/ImCollectedAndReExpert';
import ImCollectedGuahaoRecExpert from '../../../../components/ImCollectedGuahaoRecExpert';
import ImCollectedAndNoNDirectSku from '../../../../components/ImCollectedAndNoNDirectSku';
import ImCollectedAndReExpertTypewriter from '../../../../components/ImCollectedAndReExpertTypewriter';
import ImCollectedAndNoNDirectSkuTypewriter from '../../../../components/ImCollectedAndNoNDirectSkuTypewriter';

import {useModalUpdate} from '../../../../hooks/modal/useGenGlobalModalWithAtom';
import {useGetCardEventCallback} from '../../../../hooks/useGetCardEventCallback';
import {useUpdateImWelcomeMouleDisplayStatus} from '../../../../hooks/triageStream/useImWelcomeMoule';

import {triageStreamAtomStore} from '../../../../store/triageStreamAtom';
import {getBdFileMapAtom} from '../../../../store/triageStreamAtom/bdFileMapAtom';

import {OwnerTypeEnum} from '../../../../typings/msg.d';
import type {ViewRenderModalStore} from '../../../../store/viewRenderAtom';

import ConditionalRender from '../ConditionalRender';
import type {IProps} from './index.d';
import styles from './index.module.less';

const ImFormCardComponentsMap = {
    [MSG_CARDID_ENUM_STREAM.ImAIFormMenstrualPeriod]: ImFormCard,
    [MSG_CARDID_ENUM_STREAM.ImAIFormOvulationPeriod]: ImFormCard
};

const componentsMap = {
    // stream 新增消息卡片；
    [MSG_CARDID_ENUM_STREAM.ImFlow]: ImFlow,
    [MSG_CARDID_ENUM_STREAM.ImThinking]: ImThinking,
    [MSG_CARDID_ENUM_STREAM.ImCollectedInfoAndSku]: StreamFocusService,
    [MSG_CARDID_ENUM_STREAM.ImAIRecommendUnDirect]: props => {
        // eslint-disable-next-line react/prop-types
        const condition = NEW_SERVICECARD_RENDER_TYPE.includes(props?.data?.cardStyle?.renderType);
        return (
            <ConditionalRender
                {...props}
                condition={condition}
                baseComponent={ImCollectedAndNoNDirectSku}
                experimentComponent={ImCollectedAndNoNDirectSkuTypewriter}
            />
        );
    },
    [MSG_CARDID_ENUM_STREAM.ImAIRecommendExpert]: props => {
        // eslint-disable-next-line react/prop-types
        const condition = NEW_SERVICECARD_RENDER_TYPE.includes(props?.data?.cardStyle?.renderType);
        return (
            <ConditionalRender
                {...props}
                condition={condition}
                baseComponent={ImCollectedAndReExpert}
                experimentComponent={ImCollectedAndReExpertTypewriter}
            />
        );
    },
    [MSG_CARDID_ENUM_STREAM.ImAIGuahaoRecExpert]: ImCollectedGuahaoRecExpert,
    [MSG_CARDID_ENUM_STREAM.ImWelcomeModule]: ImWelcomeModule,
    ...ImFormCardComponentsMap,

    // 原消息卡片；
    [MSG_CARDID_ENUM.ImRichMsg]: ImRichMsg,
    [MSG_CARDID_ENUM.ImText]: ImText,
    [MSG_CARDID_ENUM.ImImage]: ImImage,
    [MSG_CARDID_ENUM.ImCommon]: ImCommon,
    [MSG_CARDID_ENUM.ImSystemMsg]: ImSystem,
    [MSG_CARDID_ENUM.ImFocusDoctor]: ImFocusDoctor
};

const needBubbleWrapMsg: number[] = [MSG_CARDID_ENUM.ImText];

/**
 * 动态渲染卡片组件
 *
 * @param props 组件属性
 * @returns 渲染后的卡片组件
 */
const CardComponents: FC<IProps> = props => {
    const {data, msgId, isLatest} = props;
    const {content} = data?.data || {};
    const {meta} = data || {};

    const {onOpenLink, onSendUbc} = useGetCardEventCallback();
    const {updateModalStore} = useModalUpdate(triageStreamAtomStore);
    const {updateImWelcomeMoule} = useUpdateImWelcomeMouleDisplayStatus();

    const dispatchEventCallback = useCallback(
        args => {
            const cbMap = {
                openLink: onOpenLink,
                modal: arg => {
                    const {info, cardData} = arg;
                    cardData &&
                        updateModalStore({
                            modalState: 1,
                            interaction: 'modal',
                            interactionInfo: info,
                            cardData,
                            msgData: data.data
                        } as unknown as ViewRenderModalStore);
                },
                sendUbc: arg => {
                    const mergeArg = {
                        ...arg,
                        info: {
                            ...arg.info,
                            params: {
                                ...(arg.info.params || {}),
                                ext: {
                                    ...(arg.info?.params?.ext || {}),
                                    product_info: {
                                        ...(arg.info?.params?.ext?.product_info || {})
                                    }
                                }
                            }
                        }
                    };
                    onSendUbc?.(mergeArg);
                }
            };
            cbMap[args?.interaction] &&
                cbMap[args?.interaction]?.({
                    info: args?.interactionInfo,
                    cardData: content
                });
        },
        [content, data.data, onOpenLink, onSendUbc, updateModalStore]
    );

    const extProp = useMemo(() => {
        const imFormCardExtProps = Object.keys(ImFormCardComponentsMap).reduce((acc, item) => {
            acc[item] = {
                msgId: msgId,
                cardId: content?.cardId,
                cardName: content?.cardName,
                ext: content?.data?.ext
            };
            return acc;
        }, {});

        return {
            [MSG_CARDID_ENUM_STREAM.ImAIRecommendUnDirect]: {
                localExt: data?.meta?.localExt
            },
            [MSG_CARDID_ENUM_STREAM.ImAIRecommendExpert]: {
                localExt: data?.meta?.localExt
            },
            [MSG_CARDID_ENUM.ImText]: {
                lineHeight: '87',
                color: '#000311'
            },
            [MSG_CARDID_ENUM.ImCommon]: {
                onThrowEvent: dispatchEventCallback
            },
            [MSG_CARDID_ENUM.ImFocusDoctor]: {
                onThrowEvent: dispatchEventCallback
            },
            [MSG_CARDID_ENUM.ImSystemMsg]: {
                onThrowEvent: dispatchEventCallback
            },
            [MSG_CARDID_ENUM.ImImage]: {
                data: {
                    ...(content?.data || {}),
                    cardStyle:
                        meta?.ownerType !== OwnerTypeEnum['系统']
                            ? {...(content?.data?.cardStyle || {}), width: 360, height: 360}
                            : content?.data?.cardStyle
                },
                mode: 'aspectFill',
                sendStatus: meta?.localMsgStatus,
                showloading: meta?.localMsgStatus === 'pending',
                isIm: meta?.ownerType !== OwnerTypeEnum['系统'],
                isPrivate: meta?.ownerType === OwnerTypeEnum['需求方'],
                bdFileMap: process.env.TARO_ENV === 'swan' ? getBdFileMapAtom() : undefined
            },
            [MSG_CARDID_ENUM.ImRichMsg]: {
                textExt: {
                    lineHeight: '87',
                    color: '#000311',
                    isPrivate: meta?.ownerType === OwnerTypeEnum['需求方']
                },
                imageExt: {
                    mode: 'aspectFill',
                    sendStatus: meta?.localMsgStatus,
                    showloading: meta?.localMsgStatus === 'pending',
                    isIm: meta?.ownerType !== OwnerTypeEnum['系统'],
                    isPrivate: meta?.ownerType === OwnerTypeEnum['需求方'],
                    bdFileMap: process.env.TARO_ENV === 'swan' ? getBdFileMapAtom() : undefined
                }
            },
            [MSG_CARDID_ENUM_STREAM.ImFlow]: {
                isLocalInterrupted: meta?.localMsgStatus === 'aborted',
                localExt: meta?.localExt,
                isLatest
            },
            [MSG_CARDID_ENUM_STREAM.ImWelcomeModule]: {
                curMsgId: msgId,
                onScrollIntoView: () => {
                    updateImWelcomeMoule(true);
                },
                onScrollOutOfView: () => {
                    updateImWelcomeMoule(false);
                }
            },
            ...imFormCardExtProps
        };
    }, [
        isLatest,
        dispatchEventCallback,
        content?.data,
        content?.cardId,
        content?.cardName,
        meta?.ownerType,
        meta?.localMsgStatus,
        meta?.localExt,
        data?.meta?.localExt,
        msgId,
        updateImWelcomeMoule
    ]);

    const dynamicRender = useMemo(() => {
        const ComponentToRender =
            componentsMap[content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE | MSG_CARDID_TYPE];
        const extProps = extProp[content?.cardId as keyof typeof extProp] || {};

        if (!ComponentToRender) {
            return (
                <View className={cx('wz-ptb-36 wz-plr-39 wz-flex-col wz-col-bottom wz-fs-51')}>
                    当前版本暂不支持该消息渲染
                </View>
            );
        }

        return data?.type === 'dynamic' ? (
            <ComponentToRender data={data.data} msgId={msgId} {...extProps} />
        ) : (
            <ComponentToRender data={content.data} msgId={msgId} {...extProps} />
        );
    }, [content, data.data, data?.type, extProp, msgId]);

    return needBubbleWrapMsg.includes(
        content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE | MSG_CARDID_TYPE
    ) ? (
            <View
                className={cx(
                    meta?.ownerType === OwnerTypeEnum['需求方']
                        ? styles.bubbleWrapper
                        : styles.bubbleWrapperServicer,
                    'wz-plr-45 wz-ptb-42 wz-flex-col wz-col-top wz-fs-51'
                )}
            >
                {dynamicRender}
            </View>
        ) : (
            dynamicRender
        );
};

export default memo(CardComponents);
