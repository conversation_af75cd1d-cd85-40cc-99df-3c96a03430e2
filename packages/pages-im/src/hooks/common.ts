import { useMemo, useRef, useEffect, useCallback } from 'react';
import { useRouter } from '@tarojs/taro';

import type { UrlParamsType } from '../typings';

/**
 *
 * @description 获取链接上的参数
 * @returns 链接上的参数
 */
export const useGetUrlParams = (): UrlParamsType => {
    const router = useRouter();

    return useMemo(() => {
        if (router && router?.params) {
            return router.params || ({} as unknown as UrlParamsType);
        }

        return {} as unknown as UrlParamsType;
    }, [router]);
};

/**
 *
 * @description 获取链接上的参数
 */
export const useGetPagePath = () => {
    try {
        const router = useRouter();

        if (router && router?.path) {
            return router?.path;
        }

        return '';
    } catch (error) {
        return '';
    }
};

// 节流方法
/* eslint-disable */
export const useDebounce = (fn, delay = 500, dep: any = []) => {
    const { current } = useRef({ fn, timer: null });
    useEffect(
        function () {
            current.fn = fn;

            return () => {
                if (current.timer) {
                    clearTimeout(current.timer);
                }
            };
            // eslint-disable-next-line react-hooks/exhaustive-deps
        },
        [fn]
    );

    return useCallback(function f(...args) {
        if (current.timer) {
            clearTimeout(current.timer);
        }
        // @ts-ignore
        current.timer = setTimeout(() => {
            current.fn.call(this, ...args);
        }, delay);
        // @ts-ignore
    }, dep);
};
