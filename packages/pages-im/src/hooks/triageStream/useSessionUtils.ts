import {useCallback} from 'react';
import {useAtom, useSetAtom} from 'jotai';
import {sessionCapsulesToolsAtom, sessionCapsulesToolsMd5Atom} from '../../store/triageStreamAtom';
import type {CapsulesToolsType} from '../../store/triageStreamAtom/index.type.ts';

/**
 * @module useSessionUtils
 * @description
 * 该 Hook 提供了会话级别的一些辅助功能和状态管理。
 * 目前主要用于管理和更新会话中的 "Capsules Tools" (快捷工具/短语)。
 *
 * @returns {object} 返回一个包含以下方法的对象：
 * - `updateSessionCapsulesTools`: (Function) 更新会话的 Capsules Tools 数据。
 *   它会检查传入数据的MD5值，仅当数据有变化时才更新相关的Jotai atom状态。
 *   接受一个包含md5和列表数据的对象作为参数。
 */
export const useSessionUtils = () => {
    const setSessionCapsulesTools = useSetAtom(sessionCapsulesToolsAtom);
    const [sessionCapsulesToolsMd5, setSessionCapsulesToolsMd5] = useAtom(
        sessionCapsulesToolsMd5Atom
    );

    const updateSessionCapsulesTools = useCallback(
        (arg: {md5: string; list: CapsulesToolsType[]}) => {
            if (sessionCapsulesToolsMd5 !== arg.md5) {
                setSessionCapsulesToolsMd5(arg.md5);
                setSessionCapsulesTools(arg.list);
            }
        },
        [sessionCapsulesToolsMd5, setSessionCapsulesTools, setSessionCapsulesToolsMd5]
    );

    return {
        updateSessionCapsulesTools
    };
};
