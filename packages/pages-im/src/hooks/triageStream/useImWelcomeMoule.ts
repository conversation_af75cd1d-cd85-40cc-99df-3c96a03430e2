import {useCallback} from 'react';
import {useAtomValue} from 'jotai';

import {
    imWelcomeMouleMsgIdsAtom,
    imWelcomeMouleDisplayStatusAtom,
    updateImWelcomeMouleDisplayStatus
} from '../../store/triageStreamAtom/speCardAtom';

import type {MsgId} from '../../typings';

/**
 * 更新欢迎模块展示状态
 *
 * @returns 更新欢迎模块展示状态
 */
export const useUpdateImWelcomeMouleDisplayStatus = () => {
    const updateImWelcomeMoule = useCallback((status: boolean) => {
        updateImWelcomeMouleDisplayStatus(status);
    }, []);

    return {updateImWelcomeMoule};
};

/**
 * 获取欢迎模块展示状态
 *
 * @returns 获取欢迎模块展示状态
 */
export const useGetImWelcomeMouleDisplayStatus = () => {
    const status = useAtomValue(imWelcomeMouleDisplayStatusAtom);

    return {status};
};

/**
 * 获取有效欢迎模块消息 ID
 *
 * @returns 有效欢迎模块消息 ID
 */
export const useGetImWelcomeMouleEffectiveMsgId = (): {msgIds: MsgId} => {
    const msgIds = useAtomValue(imWelcomeMouleMsgIdsAtom);

    return {
        msgIds: [...msgIds].pop() || ''
    };
};
