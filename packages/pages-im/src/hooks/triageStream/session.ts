import {useCallback, useRef} from 'react';
import {useAtomValue, useSetAtom} from 'jotai';

import {curSessionIdAtom, sessionAreaLoadingAtom} from '../../store/triageStreamAtom';

import type {SessionId} from '../../store/triageStreamAtom/index.type';
import type {GetTriageStreamCoreParamsType} from '../../models/services/triageStream/index.d';

import {useInitDataController} from './useInitDataController';

/**
 * 创建会话
 * @returns
 */
export const useCreateSessionhook = () => {
    const {getData} = useInitDataController();
    const curSessionId = useAtomValue(curSessionIdAtom);
    const setSessionAreaLoading = useSetAtom(sessionAreaLoadingAtom);

    const createSession = useCallback(async () => {
        try {
            if (!curSessionId) return;

            setSessionAreaLoading(true);
            await getData('create');
            setSessionAreaLoading(false);
        } catch (err) {
            setSessionAreaLoading(false);
            console.error('createSession error', err);
        }
    }, [curSessionId, getData, setSessionAreaLoading]);

    return {
        createSession
    };
};

/**
 * 切换会话
 * @returns
 */
export const useSwitchSessionhook = () => {
    const initSessionId = useRef<SessionId | undefined>(undefined);

    const {getData} = useInitDataController();
    const curSessionId = useAtomValue(curSessionIdAtom);
    const setSessionAreaLoading = useSetAtom(sessionAreaLoadingAtom);

    const switchSession = useCallback(
        async (
            newSessionId: SessionId,
            switchTirgger: NonNullable<GetTriageStreamCoreParamsType['ctrlData']>['pageFrom']
        ) => {
            if (!curSessionId) return;
            initSessionId.current = curSessionId;

            if (curSessionId === newSessionId) return;

            setSessionAreaLoading(true);
            await getData('init', {
                initType: 'switch',
                switchTirgger,
                switchSessionId: newSessionId
            });
            setSessionAreaLoading(false);

            // 在 H5 环境下，将 sessionId 更新到 URL 参数中
            if (process.env.TARO_ENV === 'h5') {
                const url = new URL(window.location.href);
                url.searchParams.set('sessionId', newSessionId);
                window.history.replaceState({}, '', url.toString());
            }
        },
        [curSessionId, getData, setSessionAreaLoading]
    );

    return {
        switchSession
    };
};
