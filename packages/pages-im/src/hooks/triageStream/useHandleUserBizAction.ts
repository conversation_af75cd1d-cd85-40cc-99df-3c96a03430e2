import {useSet<PERSON>tom} from 'jotai';
import {useCallback} from 'react';

import {ongoingToastAtom} from '../../store/triageStreamAtom/otherData';
import {updateTriageStreamMsgAtom} from '../../store/triageStreamAtom/msg';

import {getUserBizActionReq} from '../../models/services/triageStream';
import type {
    userInfoItem,
    GetUserBizActionRespType
} from '../../models/services/triageStream/index.d';

import {
    useGetSessionId,
    useUpdateUserData,
    useGetAdjectiveRecommendExpertMsgId,
    useGetAdjectiveRecommendUnDirectMsgId
} from './pageDataController';
import {useSessionUtils} from './useSessionUtils';
import {useMsgDataGetController} from './dataController';

const ServiceTypeMap = {
    11602: 'undirectService',
    11601: 'docService'
};

export const useHandleUserLoginBizAction = () => {
    const sessionId = useGetSessionId();
    const {adjectiveRecommendExpertMsgId} = useGetAdjectiveRecommendExpertMsgId();
    const {adjectiveRecommendUnDirectMsgId} = useGetAdjectiveRecommendUnDirectMsgId();

    const {updateUserData} = useUpdateUserData();
    const setOngoingToast = useSetAtom(ongoingToastAtom);
    const {updateSessionCapsulesTools} = useSessionUtils();

    const {data: docServiceData} = useMsgDataGetController({
        msgId: adjectiveRecommendExpertMsgId
    });
    const {data: undirectServiceData} = useMsgDataGetController({
        msgId: adjectiveRecommendUnDirectMsgId
    });

    const getNeedUpdateData = useCallback(() => {
        const userLoginInfoListData: userInfoItem[] = [];

        // 合并两个消息的数据
        [undirectServiceData, docServiceData].forEach(item => {
            if (item && item?.data && item?.data?.content) {
                const {cardId} = item?.data?.content || '';
                const {msgId} = item?.meta || '';
                const {collectedInfo} = item?.data?.content?.data?.content || {};

                const compParam = {
                    msgId,
                    patientInfo: collectedInfo?.curPatient,
                    serviceType: ServiceTypeMap[cardId as keyof typeof ServiceTypeMap],
                    zhusu: collectedInfo?.clinicalDesc || '',
                    qid: collectedInfo?.qid
                };
                userLoginInfoListData.push(compParam);
            }
        });

        return userLoginInfoListData;
    }, [docServiceData, undirectServiceData]);

    const handleMessageUpdate = useCallback(
        messageList => {
            messageList.forEach(messageItem => {
                updateTriageStreamMsgAtom(`${sessionId}_${messageItem?.meta?.msgId}`, messageItem, {
                    _debugSymbol: 'loginUpdate'
                });
            });
        },
        [sessionId]
    );

    const handleDataUpdating = useCallback(
        (args: {
            userData?: GetUserBizActionRespType<'userLogin'>['userData'];
            capsulesData?: NonNullable<
                GetUserBizActionRespType<'userLogin'>['toolData']
            >['capsules'];
            ongoingToast?: NonNullable<
                NonNullable<GetUserBizActionRespType<'userLogin'>['statusData']>['topTips']
            >['orderGuideTip']['ongoingToast'];
            messageData?: GetUserBizActionRespType<'userLogin'>['message'];
        }) => {
            const {userData, capsulesData, ongoingToast, messageData} = args;

            userData && updateUserData(userData);
            ongoingToast && setOngoingToast(ongoingToast);
            messageData && handleMessageUpdate(messageData);
            capsulesData && updateSessionCapsulesTools(capsulesData);
        },
        [handleMessageUpdate, setOngoingToast, updateSessionCapsulesTools, updateUserData]
    );

    const handleLoginBizAction = useCallback(async () => {
        const msgList = getNeedUpdateData();

        const params = {
            bizActionType: 'userLogin' as const,
            chatData: {
                sessionId: sessionId || ''
            },
            bizActionData: {
                userLoginInfoList: {
                    msgList: msgList
                }
            }
        };

        const [err, data] = await getUserBizActionReq<'userLogin'>(params);
        if (!err) {
            handleDataUpdating({
                userData: data?.data?.userData,
                capsulesData: data?.data?.toolData?.capsules,
                ongoingToast: data?.data?.statusData?.topTips?.orderGuideTip?.ongoingToast,
                messageData: data?.data?.message
            });

            return data;
        }

        return null;
    }, [getNeedUpdateData, handleDataUpdating, sessionId]);

    return {
        handleLoginBizAction
    };
};
