import {useCallback} from 'react';
import {useAtomValue} from 'jotai';
import {
    endTriageStreamMsgAtom,
    createResendMsgArgAtom,
    updateTriageStreamMsgAtom,
    updateCurSessionMsgIdsAtom,
    getTriageStreamMsgDataByKey
} from '../../../store/triageStreamAtom/msg';
import {curSessionIdAtom} from '../../../store/triageStreamAtom';

import type {MsgId} from '../../../typings';
import type {SSEProcessorInstance} from '../../../models/services/triageStream/sse';
import type {MsgItemType, SSEResponseType} from '../../../store/triageStreamAtom/index.type.ts';

import type {CreateConversationArgs} from '../index.d';

// Keep curSSEInstance managed here
const curSSEInstance: {
    id: symbol;
    thinkingMsgKey: MsgId;
    relatedMsgIds: MsgId[];
    instance: SSEProcessorInstance<SSEResponseType>;
}[] = [];

let isActivelyAborted: {
    [key: symbol]: boolean;
} = {};

/**
 * @module useSSEManager
 * @description
 * 该 Hook 专门用于管理 Server-Sent Events (SSE) 的连接实例和其生命周期。
 * 它处理SSE实例的创建、追踪活动状态、添加关联消息ID、处理完成/错误回调以及清理工作。
 *
 * @returns {object} 返回一个包含以下属性和方法的对象：
 * - `curSSEInstance`: (Array) 当前活动的SSE实例队列。主要供内部管理，但可用于如 `pop` 等操作。
 * - `addSSEInstance`: (Function) 向活动队列中添加一个新的SSE实例及其关联的思考中消息键。
 * - `getSSEInstance`: (Function) 根据ID获取一个特定的SSE实例。
 * - `addRelatedMsgIdToInstance`: (Function) 为指定的SSE实例添加关联的消息ID，用于后续清理。
 * - `removeSSEInstance`: (Function) 根据ID从活动队列中移除一个SSE实例。
 * - `updateRelatedMsgData`: (Function) 清理指定SSE实例关联的所有消息数据（标记为结束）。
 * - `onSSEComplete`: (Function) SSE实例成功完成时的回调处理函数。
 *   负责清理思考中消息、更新关联消息数据及移除实例。
 * - `onSSEError`: (Function) SSE实例发生错误时的回调处理函数。
 *   负责创建重发参数、更新消息状态为失败或中止，并移除实例。
 * - `isActivelyAborted`: (React.MutableRefObject<boolean>) 一个 ref 对象，标识SSE流是否被用户或程序主动中止。
 * - `setActivelyAborted`: (Function) 设置 `isActivelyAborted` ref 的值。
 */
export const useSSEManager = () => {
    const sessionId = useAtomValue(curSessionIdAtom);

    const addSSEInstance = useCallback(
        (instance: SSEProcessorInstance<SSEResponseType>, thinkingMsgKey: MsgId) => {
            curSSEInstance.push({
                id: instance.id,
                instance,
                relatedMsgIds: [],
                thinkingMsgKey
            });
        },
        []
    );

    const getSSEInstance = useCallback((id: symbol) => {
        return curSSEInstance.find(i => i.id === id);
    }, []);

    const addRelatedMsgIdToInstance = useCallback((sseId: symbol, msgId: MsgId) => {
        const instance = curSSEInstance.find(i => i.id === sseId);
        if (instance && !instance.relatedMsgIds.includes(msgId)) {
            instance.relatedMsgIds.push(msgId);
        }
    }, []);

    const updateRelatedMsgData = useCallback(
        (sseId: symbol) => {
            if (!sessionId) return;
            const instance = curSSEInstance.find(i => i.id === sseId);
            instance?.relatedMsgIds.forEach(msgId => {
                endTriageStreamMsgAtom(`${sessionId}_${msgId}`);
            });
        },
        [sessionId]
    );

    const removeSSEInstance = useCallback((sseId: symbol) => {
        const index = curSSEInstance.findIndex(item => item.id === sseId);

        if (index !== -1) {
            curSSEInstance.splice(index, 1);
        }
        delete isActivelyAborted[sseId];
    }, []);

    const onSSEComplete = useCallback(
        (arg: {
            thinkingMsgMsgId: MsgId;
            withOutThinkingMsg: boolean;
            sseId: SSEProcessorInstance<SSEResponseType>['id'];
        }) => {
            try {
                const {sseId, thinkingMsgMsgId, withOutThinkingMsg} = arg;

                if (!withOutThinkingMsg) {
                    updateCurSessionMsgIdsAtom([thinkingMsgMsgId], {
                        type: 'delete'
                    });
                }
                updateRelatedMsgData(sseId);
                removeSSEInstance(sseId);
            } catch (err) {
                console.error('SSEManager onComplete error:', err);
            }
        },
        [updateRelatedMsgData, removeSSEInstance]
    );

    const onSSEError = useCallback(
        (arg: {
            msgContent?: MsgItemType<unknown>;
            msgKey: string;
            resendArg: CreateConversationArgs;
            sseId: SSEProcessorInstance<SSEResponseType>['id'];
            errorContent: {status?: string} | unknown;
            lastReplyId: MsgId;
        }) => {
            const {msgContent, msgKey, sseId, resendArg, lastReplyId} = arg;
            if (!sessionId) return;

            const isAborted =
                process.env.TARO_ENV === 'h5'
                    ? (arg.errorContent as {status?: string})?.status === 'aborted'
                    : isActivelyAborted[sseId];

            if (!isAborted) {
                createResendMsgArgAtom(msgKey, resendArg);
            }

            const lastReplyMsgData = getTriageStreamMsgDataByKey(`${sessionId}_${lastReplyId}`);

            if (lastReplyId !== '' && lastReplyMsgData) {
                const updatedMsg = {
                    ...lastReplyMsgData,
                    meta: {
                        ...lastReplyMsgData?.meta,
                        localMsgStatus: 'aborted' as const
                    }
                };

                updateTriageStreamMsgAtom(`${sessionId}_${lastReplyId}`, updatedMsg, {
                    actionType: 'update'
                });
            }

            if (msgContent) {
                const updatedMsg = {
                    ...msgContent,
                    meta: {
                        ...msgContent?.meta,
                        localMsgStatus:
                            lastReplyId !== '' && lastReplyMsgData
                                ? ('aborted' as const)
                                : ('rejected' as const)
                    }
                };
                updateTriageStreamMsgAtom(`${sessionId}_${msgKey}`, updatedMsg, {
                    _debugSymbol: 'onSEEError'
                });
            }

            removeSSEInstance(sseId);
        },
        [sessionId, removeSSEInstance]
    );

    const setActivelyAborted = useCallback(
        (args: {id: symbol; status: 'aborted'; _debugSymbol?: string}) => {
            isActivelyAborted = {
                ...isActivelyAborted,
                [args.id]: args.status === 'aborted'
            };
        },
        []
    );

    return {
        curSSEInstance,
        addSSEInstance,
        getSSEInstance,
        addRelatedMsgIdToInstance,
        removeSSEInstance,
        updateRelatedMsgData,
        onSSEComplete,
        onSSEError,
        isActivelyAborted,
        setActivelyAborted
    };
};
