import {useCallback, useRef, useMemo} from 'react';
import {useAtom, useAtomValue, useSetAtom} from 'jotai';

import {
    updateCurSessionMsgIdsAtom,
    updateTriageStreamMsgAtom,
    endTriageStreamMsgAtom
} from '../../../store/triageStreamAtom/msg';
import {
    lastMsgIdAtom,
    curSessionIdAtom,
    updateDataForUbcAtom,
    lastConversionMsgIdAtom
} from '../../../store/triageStreamAtom';

import {useGetUrlParams} from '../../../hooks/common';

import type {MsgId} from '../../../typings';
import type {CreateConversationArgs} from '../index.d';
import type {MsgItemType} from '../../../store/triageStreamAtom/index.type.ts';

import {useSessionUtils} from '../useSessionUtils';
import {useScrollControl} from '../useScrollControl';
import {useHandleUserLoginBizAction} from '../useHandleUserBizAction';
import {useGetSwanMsgListSceneStatus} from '../useGetSwanMsgListSceneStatus';

import {useSSEManager} from './useSSEManager';
import {updateMsgData} from './conversationDataUtils';
import {useMessageManager} from './useMessageManager';
import {cancelPreSSEConnection} from './sseControlUtils';
import {executeConversationCreation} from './conversationCreatorUtils';

import type {
    SSEStatusRefs,
    CancelPreSSEParams,
    UpdateMsgDataParams,
    ConversationCreatorDeps
} from './index.d';

/**
 * @module useConversationDataController
 * @description
 * 该 Hook 作为会话页面数据交互的总控制器。
 * 它整合了消息管理、SSE连接管理和会话工具（如Capsules）的功能，
 * 负责创建新的会话交互、处理用户输入、管理SSE流的生命周期，以及更新UI所需的相关状态。
 *
 * @returns {object} 返回一个包含以下方法的对象：
 * - `mockUserMsg`: (Function) 模拟用户消息发送并将其展示在UI上。
 *   接受消息内容和消息键作为参数。
 * - `cancelPreSSE`: (Function) 取消当前正在进行的SSE实例。
 *   例如，在用户发起新一轮对话或离开页面时调用。接受一个中断原因（字符串）和可选的消息键作为参数。
 * - `createConversation`: (Function) 创建一个新的会话交互流程。
 *   包括模拟用户消息、准备SSE请求参数、发起SSE连接、处理SSE消息流以及更新状态。
 *   接受一个包含消息详情、是否显示用户消息、是否显示思考中消息等选项的参数对象。
 * - `updateSessionCapsulesTools`: (Function) 更新会话中快捷短语/工具（Capsules）的数据。
 *   通常由SSE消息流中的特定事件触发。接受一个包含md5和列表数据的对象。
 */
export const useConversationDataController = () => {
    const lastReply = useRef<MsgId>('');
    const aiTextCount = useRef<number>(0);
    const sseHasSuccessed = useRef(false);
    const sseHasConnected = useRef(false);
    const lastMsgIdRef = useRef<MsgId>('');
    const sseDataHasDealed = useRef(false);
    const llmTokenHasStarted = useRef(false);
    const lastMsgExt = useRef<Record<string, unknown>>({});

    const getLastReply = () => lastReply.current;

    const sessionId = useAtomValue(curSessionIdAtom);
    const updateLastMsgIdAtomSetter = useSetAtom(lastMsgIdAtom);
    const [currentLastConversionMsgId, setLastConversionMsgId] = useAtom(lastConversionMsgIdAtom);

    // URL 参数和其他
    const {scrollToBottom} = useScrollControl();
    // eslint-disable-next-line camelcase
    const {expertId, expert_id} = useGetUrlParams();
    const {handleLoginBizAction} = useHandleUserLoginBizAction();
    const {status: isSwanMsgListScene} = useGetSwanMsgListSceneStatus();

    // SSE Manager 相关
    const {
        addSSEInstance,
        setActivelyAborted,
        addRelatedMsgIdToInstance,
        onSSEError: sseManagerOnSSEError,
        onSSEComplete: sseManagerOnComplete,
        curSSEInstance: sseManagerCurSSEInstance
    } = useSSEManager();

    // 消息处理 相关
    const {
        updateUserMsgStatusOnCancel,
        mockUserMsg: managerMockUserMsg,
        addThinkMsg: managerAddThinkMsg,
        onSEEConnect: messageManagerOnSEEConnect,
        convertMsgToSSE: managerConvertMsgToSSE
    } = useMessageManager();

    // 会话工具 相关
    const {updateSessionCapsulesTools: managerUpdateSessionCapsulesTools} = useSessionUtils();

    // 构建 SSE 状态引用对象
    const sseStatusRefs: SSEStatusRefs = useMemo(
        () => ({
            sseHasSuccessed,
            sseHasConnected,
            sseDataHasDealed,
            llmTokenHasStarted,
            lastMsgIdRef,
            aiTextCount,
            lastMsgExt
        }),
        []
    );

    // 消息数据更新函数
    const updateMsgDataWrapper = useCallback(
        (msgItemList: MsgItemType<unknown>[], sseInstanceId: symbol, thinkingMsgMsgId: MsgId) => {
            const params: UpdateMsgDataParams = {
                msgItemList,
                sseInstanceId,
                thinkingMsgMsgId,
                sessionId: sessionId!,
                onUpdateDataForUbc: updateDataForUbcAtom,
                onUpdateLastMsgId: updateLastMsgIdAtomSetter,
                onSetLastConversionMsgId: setLastConversionMsgId,
                onUpdateSessionMsgIds: updateCurSessionMsgIdsAtom,
                onUpdateTriageStreamMsg: updateTriageStreamMsgAtom,
                onAddRelatedMsgIdToInstance: addRelatedMsgIdToInstance,
                onSetLastReply: (msgId: MsgId) => {
                    lastReply.current = msgId;
                },
                onSetLastMsgIdRef: (msgId: MsgId) => {
                    lastMsgIdRef.current = msgId;
                },
                onAddAiTextCount: count => {
                    aiTextCount.current += count;
                },
                onUpdateLastMsgExt: ext => {
                    lastMsgExt.current = ext;
                }
            };
            updateMsgData(params);
        },
        [sessionId, updateLastMsgIdAtomSetter, setLastConversionMsgId, addRelatedMsgIdToInstance]
    );

    // 取消前置 SSE 连接
    const cancelPreSSE = useCallback(
        (reason: string, msgKeyForStatusUpdate?: string) => {
            if (!sessionId) return;

            const preSEEInstance = sseManagerCurSSEInstance.pop();
            const params: CancelPreSSEParams = {
                sessionId,
                expertId: Number(expertId || expert_id),
                currentLastConversionMsgId,
                preSEEInstance,
                reason,
                msgKeyForStatusUpdate,
                onSetActivelyAborted: setActivelyAborted,
                onUpdateSessionMsgIds: updateCurSessionMsgIdsAtom,
                onEndTriageStreamMsg: endTriageStreamMsgAtom,
                onUpdateUserMsgStatusOnCancel: updateUserMsgStatusOnCancel
            };

            cancelPreSSEConnection(params);
        },
        [
            sessionId,
            expertId,
            expert_id,
            currentLastConversionMsgId,
            setActivelyAborted,
            updateUserMsgStatusOnCancel,
            sseManagerCurSSEInstance
        ]
    );

    // 创建会话
    const createConversation = useCallback(
        async (arg: CreateConversationArgs) => {
            if (!sessionId) return;

            // 构建依赖对象
            const deps: ConversationCreatorDeps = {
                sessionId,
                sseStatusRefs,
                getLastReply,
                cancelPreSSE,
                addSSEInstance,
                scrollToBottom,
                managerMockUserMsg,
                managerAddThinkMsg,
                managerConvertMsgToSSE,
                sseManagerOnComplete: arg => {
                    sseManagerOnComplete?.(arg);
                },
                sseManagerOnSSEError,
                handleLoginBizAction,
                updateCurSessionMsgIdsAtom,
                messageManagerOnSEEConnect,
                managerUpdateSessionCapsulesTools,
                updateMsgData: updateMsgDataWrapper
            };

            // 执行会话创建逻辑
            await executeConversationCreation(arg, deps, {
                isBdMessageCenterScene: isSwanMsgListScene
            });
        },
        [
            sessionId,
            sseStatusRefs,
            isSwanMsgListScene,
            cancelPreSSE,
            addSSEInstance,
            scrollToBottom,
            managerMockUserMsg,
            managerAddThinkMsg,
            sseManagerOnSSEError,
            sseManagerOnComplete,
            updateMsgDataWrapper,
            handleLoginBizAction,
            managerConvertMsgToSSE,
            messageManagerOnSEEConnect,
            managerUpdateSessionCapsulesTools
        ]
    );

    // 暴露的模拟用户消息函数
    const exposedMockUserMsg = useCallback(
        (msg: CreateConversationArgs['msg'], msgKey: string) => {
            return managerMockUserMsg(msg, msgKey, lastMsgIdRef);
        },
        [managerMockUserMsg]
    );

    return {
        cancelPreSSE,
        createConversation,
        mockUserMsg: exposedMockUserMsg,
        updateSessionCapsulesTools: managerUpdateSessionCapsulesTools
    };
};
