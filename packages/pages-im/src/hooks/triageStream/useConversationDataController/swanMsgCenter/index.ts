import type {DispatchSwanimActionArgs, SyncSwanimActionArgs, SendSwanMsgOps} from './index.d';

export const failedMsgPrefix = '[发送失败]';

export const dispatchSwanimAction = (arg: DispatchSwanimActionArgs) => {
    return new Promise(resolve => {
        // eslint-disable-next-line no-console
        console.warn('当前场景不支持 swanMsgCenter 消息发送，dispatchSwanimAction arg', arg);
        resolve(null);
    });
};

export const syncSwanMsgStatus = (
    arg: SyncSwanimActionArgs
): Promise<{status: 'success' | 'rejected'}> => {
    return new Promise(resolve => {
        // eslint-disable-next-line no-console
        console.warn('当前场景不支持 syncSwanMsgStatus', arg);
        resolve({
            status: 'success'
        });
    });
};

export const sendSwanMsg = (text: string, ops?: SendSwanMsgOps) => {
    return new Promise(resolve => {
        // eslint-disable-next-line no-console
        console.warn('当前场景不支持 sendSwanMsg', text, ops);
        resolve(null);
    });
};

export const transformMsg = ({msg, type}: {msg: string; type: number}): string => {
    // eslint-disable-next-line no-console
    console.warn('当前场景不支持 sendSwanMsg', msg, type);

    return '';
};
