import type {CreateConversationArgs} from '../..';

export interface DispatchSwanimActionArgs {
    msgData: CreateConversationArgs['msg'];
}

export interface SyncSwanimActionArgs {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    sseRes: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    swanImRes: any;
}

export interface SendSwanMsgOps {
    onResolve?: (res: unknown) => void;
    onReject?: (err: unknown) => void;
}
