import type {MutableRefObject} from 'react';

import type {MsgId} from '../../../typings';
import type {
    MsgItemType,
    SSEResponseType,
    CapsulesToolsType
} from '../../../store/triageStreamAtom/index.type.ts';

import type {InputImgMap} from '../../../models/services/triageStream/sse/index.d';
import type {SSEProcessorInstance} from '../../../models/services/triageStream/sse';
import type {AgreementDataType} from '../../../models/services/triageStream/index.d';

import type {CreateConversationArgs} from '../index.d';

/**
 * SSE 状态引用接口
 */
export interface SSEStatusRefs {
    sseHasSuccessed: MutableRefObject<boolean>;
    sseHasConnected: MutableRefObject<boolean>;
    sseDataHasDealed: MutableRefObject<boolean>;
    llmTokenHasStarted: MutableRefObject<boolean>;
    lastMsgIdRef: MutableRefObject<MsgId>;
    aiTextCount: MutableRefObject<number>;
    lastMsgExt: MutableRefObject<Record<string, unknown>>;
}

/**
 * SSE Manager 错误处理参数接口
 */
export interface SSEManagerErrorParams {
    hasReturnedData?: boolean;
    msgKey: string;
    sseId: symbol;
    resendArg: CreateConversationArgs;
    msgContent: MsgItemType<unknown> | undefined;
    errorContent: Error;
    lastReplyId: MsgId;
}

/**
 * 用户消息模拟选项接口
 */
export interface MockUserMsgOptions {
    agreementInfo?: AgreementDataType;
    onAgreementInfoCallback?: () => void;
}

/**
 * SSE 转换返回类型接口
 */
export interface SSEConvertResult {
    content: string | InputImgMap;
    contentType?: number;
}

/**
 * 胶囊工具数据类型
 */
export interface CapsulesData {
    md5: string;
    list: CapsulesToolsType[];
}

/**
 * 会话创建器依赖接口
 */
export interface ConversationCreatorDeps {
    sessionId: string;
    sseStatusRefs: SSEStatusRefs;
    getLastReply: () => MsgId;
    scrollToBottom: (reason: string) => void;
    cancelPreSSE: (reason: string, msgKey?: string) => void;
    managerMockUserMsg: (
        msg: CreateConversationArgs['msg'],
        msgKey: string,
        lastMsgIdRef: MutableRefObject<MsgId>,
        options?: MockUserMsgOptions
    ) => MsgItemType<unknown> | undefined;
    managerConvertMsgToSSE: (msg: CreateConversationArgs['msg']) => Promise<SSEConvertResult>;
    managerAddThinkMsg: (thinkingMsgMsgId: MsgId, lastMsgIdRef: MutableRefObject<MsgId>) => void;
    messageManagerOnSEEConnect: (params: {
        msgKey: string;
        msgContent: MsgItemType<unknown> | undefined;
    }) => void;
    sseManagerOnComplete: (params: {
        sseId: symbol;
        thinkingMsgMsgId: MsgId;
        withOutThinkingMsg: boolean;
    }) => void;
    sseManagerOnSSEError: (params: SSEManagerErrorParams) => void;
    addSSEInstance: (
        instance: SSEProcessorInstance<SSEResponseType>,
        thinkingMsgMsgId: MsgId
    ) => void;
    managerUpdateSessionCapsulesTools: (capsules: CapsulesData) => void;
    updateMsgData: (
        msgItemList: MsgItemType<unknown>[],
        sseInstanceId: symbol,
        thinkingMsgMsgId: MsgId
    ) => void;
    handleLoginBizAction: () => void;
    updateCurSessionMsgIdsAtom: (msgIds: MsgId[], options: {type: 'delete'}) => void;
}

/**
 * SSE 实例信息接口
 */
export interface SSEInstanceInfo {
    instance: {
        close?: () => void;
        getCurrentEventId: () => string | number;
    };
    id: symbol;
    thinkingMsgKey: string;
    relatedMsgIds: MsgId[];
}

/**
 * 取消前置 SSE 的参数接口
 */
export interface CancelPreSSEParams {
    reason: string;
    sessionId: string;
    expertId: number | string;
    currentLastConversionMsgId: MsgId;
    msgKeyForStatusUpdate?: string;
    preSEEInstance?: SSEInstanceInfo;
    onEndTriageStreamMsg: (key: string) => void;
    onSetActivelyAborted: (options: {id: symbol; status: 'aborted'}) => void;
    onUpdateUserMsgStatusOnCancel: (msgKey: string, status: 'success') => void;
    onUpdateSessionMsgIds: (msgIds: MsgId[], options: {type: 'delete'}) => void;
}

/**
 * 更新消息数据的工具函数
 */
export interface UpdateMsgDataParams {
    sessionId: string;
    sseInstanceId: symbol;
    thinkingMsgMsgId: MsgId;
    msgItemList: MsgItemType<unknown>[];
    onSetLastReply: (msgId: MsgId) => void;
    onUpdateLastMsgId: (msgId: MsgId) => void;
    onSetLastMsgIdRef: (msgId: MsgId) => void;
    onSetLastConversionMsgId: (msgId: MsgId) => void;
    onUpdateDataForUbc: (data: {lastMsgId: MsgId; rounds: number}) => void;
    onAddRelatedMsgIdToInstance: (sseInstanceId: symbol, msgId: MsgId) => void;
    onUpdateSessionMsgIds: (
        msgIds: MsgId[],
        options: {type: 'insertBefore'; targetId: MsgId}
    ) => void;
    onUpdateTriageStreamMsg: (
        key: string,
        msg: MsgItemType<unknown>,
        debugInfo: {_debugSymbol: string}
    ) => void;
    onAddAiTextCount: (count: number) => void;
    onUpdateLastMsgExt: (ext: Record<string, unknown>) => void;
}
