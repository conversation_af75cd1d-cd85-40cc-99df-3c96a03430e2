import {getUseractionReq} from '../../../models/services/triageStream';

import type {CancelPreSSEParams} from './index.d';

/**
 * 取消前置 SSE 连接的工具函数
 */
export const cancelPreSSEConnection = (params: CancelPreSSEParams) => {
    const {
        sessionId,
        expertId,
        currentLastConversionMsgId,
        preSEEInstance,
        reason,
        msgKeyForStatusUpdate,
        onSetActivelyAborted,
        onUpdateSessionMsgIds,
        onEndTriageStreamMsg,
        onUpdateUserMsgStatusOnCancel
    } = params;

    if (!sessionId || !preSEEInstance?.instance) return;

    preSEEInstance.instance.close?.();

    // Tips：小程序无法分辨是否为主动打断，所以需要手动设置；@wanghaoyu08
    if (process.env.TARO_ENV !== 'h5') {
        onSetActivelyAborted({
            id: preSEEInstance.id,
            status: 'aborted'
        });
    }

    onUpdateSessionMsgIds([preSEEInstance.thinkingMsgKey], {type: 'delete'});

    preSEEInstance.relatedMsgIds.forEach(id => {
        onEndTriageStreamMsg(`${sessionId}_${id}`);
    });

    if (msgKeyForStatusUpdate) {
        onUpdateUserMsgStatusOnCancel(msgKeyForStatusUpdate, 'success');
    }

    const eventId = preSEEInstance.instance.getCurrentEventId();
    getUseractionReq<'stopConv'>({
        bizActionType: 'stopConv',
        // eslint-disable-next-line camelcase
        chatData: {sessionId, expertId: Number(expertId)},
        bizActionData: {
            stopConvInfo: {
                reason,
                eventId: String(eventId),
                msgId: currentLastConversionMsgId
            }
        }
    });
};
