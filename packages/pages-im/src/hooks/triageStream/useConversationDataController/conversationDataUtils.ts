import {setIsGeneratingAtom} from '../../../store/triageStreamAtom';
import type {MSG_CARDID_ENUM_STREAM_TYPE} from '../../../constants/msg';

import {updateSpecialCardAdjectiveMsgId} from '../msgUtils';
import type {UpdateMsgDataParams} from './index.d';

export const updateMsgData = (params: UpdateMsgDataParams) => {
    const {
        sessionId,
        msgItemList,
        sseInstanceId,
        thinkingMsgMsgId,
        onSetLastReply,
        onAddAiTextCount,
        onSetLastMsgIdRef,
        onUpdateLastMsgId,
        onUpdateLastMsgExt,
        onUpdateDataForUbc,
        onUpdateSessionMsgIds,
        onUpdateTriageStreamMsg,
        onSetLastConversionMsgId,
        onAddRelatedMsgIdToInstance
    } = params;

    if (!sessionId || !msgItemList || msgItemList.length === 0) return;

    setIsGeneratingAtom(true);

    msgItemList.forEach(msg => {
        const {meta, data} = msg;

        onSetLastReply(meta?.msgId);

        msg.meta.localExt = {
            dataSource: 'conversation',
            insertType: 'push',
            needScrollToBottom: true
        };

        // 默认获取回来的消息插入在『思考中』消息之前，如果有『思考中消息』，updateCurSessionMsgIdsAtom 兜底插入；@wanghaoyu08
        onUpdateSessionMsgIds([meta.msgId], {
            type: 'insertBefore',
            targetId: thinkingMsgMsgId
        });
        onUpdateTriageStreamMsg(`${sessionId}_${meta.msgId}`, msg, {
            _debugSymbol: 'updateMsgData'
        });

        updateSpecialCardAdjectiveMsgId(
            data?.content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE,
            meta.msgId
        );
        onAddRelatedMsgIdToInstance(sseInstanceId, meta.msgId);

        // 统计打断时回复字数
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const dataContent = data?.content?.data?.content as any;
        if (dataContent && dataContent.list && Array.isArray(dataContent.list)) {
            // 假设列表中的每项文本长度需要统计
            dataContent.list.forEach(item => {
                if (item && typeof item.content === 'string') {
                    onAddAiTextCount(item.content.length);
                }
            });
        }
        onUpdateLastMsgExt(data?.content?.data?.ext || {});
    });

    const lastMeta = msgItemList[msgItemList.length - 1].meta;

    onUpdateLastMsgId(lastMeta.msgId);
    onSetLastConversionMsgId(lastMeta.msgId);
    onSetLastMsgIdRef(lastMeta.msgId);
    onUpdateDataForUbc({lastMsgId: lastMeta.msgId, rounds: lastMeta.rounds || 0});
};
