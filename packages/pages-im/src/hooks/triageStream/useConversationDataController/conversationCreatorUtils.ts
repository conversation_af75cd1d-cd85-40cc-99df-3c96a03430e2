import type {MutableRefObject} from 'react';
import {genImMsgKey} from '@baidu/vita-utils-shared';

import {showToast} from '../../../utils/customShowToast';
import {allSettled} from '../../../utils/generalFunction/allSettled';
import {ubcCommonViewSend} from '../../../utils/generalFunction/ubc';

import {MSG_THINKING_MSG_KEY_PREFIX} from '../../../constants/msg';
import {
    conversationSSE,
    type SSEProcessorInstance
} from '../../../models/services/triageStream/sse';

import {
    getUserDataAtom,
    getUserInterruptedAtom,
    setIsGeneratingAtom
} from '../../../store/triageStreamAtom';
import {updateMsgRiskControlAtom} from '../../../store/triageStreamAtom/msg';
import {
    getAgreementInfo<PERSON>tom,
    resetAgreementInfoAtom
} from '../../../store/triageStreamAtom/otherData';

import type {MsgId} from '../../../typings';
import type {CreateConversationArgs} from '../index.d';
import type {SSEResponseType} from '../../../store/triageStreamAtom/index.type.ts';

import {
    transformMsg,
    sendSwanMsg,
    failedMsgPrefix,
    syncSwanMsgStatus,
    dispatchSwanimAction
} from './swanMsgCenter';

import type {SSEConvertResult, ConversationCreatorDeps, SSEStatusRefs} from './index.d';

/**
 * 准备会话参数
 */
export const prepareConversationParams = async (
    arg: CreateConversationArgs,
    sessionId: string,
    managerConvertMsgToSSE: (msg: CreateConversationArgs['msg']) => Promise<SSEConvertResult>
) => {
    const {msg, ctrlData, formSubmitData, intent} = arg;
    const msgKey = genImMsgKey(10);
    const thinkingMsgMsgId = `${MSG_THINKING_MSG_KEY_PREFIX}${msgKey}`;

    const ssePayloadMsg = await managerConvertMsgToSSE(msg);
    const params = {
        chatData: {
            sessionId,
            sceneType: msg.sceneType || 'unknown'
        },
        msg: {payload: [{msgKey, ...ssePayloadMsg}]},
        ...(ctrlData?.firstCall ? {ctrlData} : {ctrlData: {tmpBotVersion2: 1 as const}}),
        ...(formSubmitData?.msgId ? {formSubmitData} : {}),
        ...(intent ? {intent} : {})
    };

    return {msgKey, thinkingMsgMsgId, params};
};

/**
 * 处理用户消息上墙
 */
export const handleUserMsgDisplay = (
    arg: CreateConversationArgs,
    msgKey: string,
    lastMsgExt: MutableRefObject<Record<string, unknown>>,
    lastMsgIdRef: MutableRefObject<MsgId>,
    managerMockUserMsg: ConversationCreatorDeps['managerMockUserMsg'],
    scrollToBottom: ConversationCreatorDeps['scrollToBottom']
) => {
    const {msg, withOutMsg = false} = arg;

    if (withOutMsg) return undefined;

    const agreementInfo = getAgreementInfoAtom();
    const mockUserMsgContent = managerMockUserMsg(msg, msgKey, lastMsgIdRef, {
        agreementInfo: agreementInfo,
        onAgreementInfoCallback: () => {
            resetAgreementInfoAtom();
        }
    });

    // 用户发送消息上墙，需滚动到底部
    scrollToBottom('user_send_msg_to_wall');

    // 有用户消息发送统计
    ubcCommonViewSend({
        value: 'tokenAllCountApi',
        ext: {
            product_info: {
                msgId: lastMsgIdRef.current,
                ...(lastMsgExt.current ?? {})
            }
        }
    });

    return mockUserMsgContent;
};

/**
 * 处理 SSE 接口返回的数据
 */
export const handleSSEDataStream = async (
    sseInstance: SSEProcessorInstance<SSEResponseType>,
    params: {
        thinkingMsgMsgId: MsgId;
        sseStatusRefs: SSEStatusRefs;
        startSSETime: number;
        deps: ConversationCreatorDeps;
    }
) => {
    const {thinkingMsgMsgId, sseStatusRefs, startSSETime, deps} = params;
    const {
        getLastReply,
        managerUpdateSessionCapsulesTools,
        updateMsgData,
        handleLoginBizAction,
        updateCurSessionMsgIdsAtom,
        sseManagerOnComplete
    } = deps;

    let firstResponseTime: number;

    for await (const chunk of sseInstance.message()) {
        if (chunk?.status === 0 && chunk?.data) {
            sseStatusRefs.sseHasConnected.current = true;

            // 更新胶囊工具
            if (chunk.data.toolData?.capsules) {
                managerUpdateSessionCapsulesTools(chunk.data.toolData.capsules);
            }

            if (chunk.data.message) {
                updateMsgData(chunk.data.message, sseInstance.id, thinkingMsgMsgId);
            }

            // 展示 toast 提示
            if (chunk.data.ctrlData?.toast) {
                showToast({
                    title: chunk.data.ctrlData.toast,
                    icon: 'none',
                    duration: 3000
                });
            }

            if (chunk.data.ctrlData?.riskControl) {
                updateMsgRiskControlAtom(chunk.data.ctrlData.riskControl);
            }

            if (
                !sseStatusRefs.llmTokenHasStarted.current &&
                chunk.data.ctrlData?.convStatus === 'start'
            ) {
                sseStatusRefs.llmTokenHasStarted.current = true;
                // 记录并上报首次 token 返回时间
                firstResponseTime = new Date().getTime();
                if (startSSETime) {
                    const tokenTime = firstResponseTime - startSSETime;
                    ubcCommonViewSend({
                        value: 'tokenFirstTimeApi',
                        ext: {
                            product_info: {
                                costTime: tokenTime,
                                msgId: getLastReply(),
                                ...(sseStatusRefs?.lastMsgExt.current ?? {})
                            }
                        }
                    });
                }
                updateCurSessionMsgIdsAtom([thinkingMsgMsgId], {type: 'delete'});
            }

            // 校验请求发送时和结束时用户是否登录状态是否一致；
            if (chunk.data.userData?.isLogin) {
                const localUserData = getUserDataAtom();
                if (localUserData && !localUserData.isLogin) {
                    handleLoginBizAction();
                }
            }
        }
    }

    sseStatusRefs.sseDataHasDealed.current = true;
    if (sseStatusRefs.sseHasSuccessed.current) {
        sseManagerOnComplete({
            sseId: sseInstance.id,
            thinkingMsgMsgId,
            withOutThinkingMsg: false
        });
    }
};

/**
 * 创建会话的核心实现
 * @description
 * 用于构建请求 sse 接口参数，实例化请求并调用 handleSSEDataStream 处理数据流；
 * 针对特殊的场景，如手百消息中心，需要额外处理 SSE 连接成功后的逻辑；
 */
export const executeConversationCreation = async (
    arg: CreateConversationArgs,
    deps: ConversationCreatorDeps,
    speExt?: {
        isBdMessageCenterScene?: boolean;
    }
) => {
    const {
        sessionId,
        sseStatusRefs,
        getLastReply,
        cancelPreSSE,
        addSSEInstance,
        scrollToBottom,
        managerMockUserMsg,
        managerAddThinkMsg,
        sseManagerOnComplete,
        sseManagerOnSSEError,
        managerConvertMsgToSSE,
        updateCurSessionMsgIdsAtom,
        messageManagerOnSEEConnect
    } = deps;
    const {withOutThinkingMsg = false, sseOnErrorCallback, withOutMsg = false} = arg;

    // 准备参数
    const {msgKey, thinkingMsgMsgId, params} = await prepareConversationParams(
        arg,
        sessionId,
        managerConvertMsgToSSE
    );

    // 取消前置 SSE
    cancelPreSSE('用户新建会话主动停止', msgKey);

    // 处理用户消息显示
    const mockUserMsgContent = handleUserMsgDisplay(
        arg,
        msgKey,
        sseStatusRefs.lastMsgExt,
        sseStatusRefs.lastMsgIdRef,
        managerMockUserMsg,
        scrollToBottom
    );

    // 重置状态
    sseStatusRefs.sseHasSuccessed.current = false;
    sseStatusRefs.sseDataHasDealed.current = false;
    sseStatusRefs.llmTokenHasStarted.current = false;
    sseStatusRefs.sseHasConnected.current = false;
    // todo 这俩之前是在updateMsg 里面修改的
    sseStatusRefs.aiTextCount.current = 0; // 开始新会话时重置字数统计
    sseStatusRefs.lastMsgExt.current = {};

    const startSSETime = new Date().getTime();

    // const failedToRunQueue = [];

    try {
        let sseInstance: SSEProcessorInstance<SSEResponseType>;

        const sseTask = conversationSSE({
            params,
            ops: {
                // Tips：各个平台返回数据格式不一致，此处暂用 any；@wanghaoyu08
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                onComplete: (res: any) => {
                    setIsGeneratingAtom(false);
                    if (
                        (process.env.TARO_ENV === 'swan' &&
                            (res?.status === 200 || res?.statusCode === 200)) ||
                        (process.env.TARO_ENV === 'h5' && res?.status === 'success')
                    ) {
                        // 请求建立成功
                        sseStatusRefs.sseHasSuccessed.current = true;
                        if (sseStatusRefs.sseDataHasDealed.current) {
                            sseManagerOnComplete({
                                sseId: sseInstance.id,
                                thinkingMsgMsgId,
                                withOutThinkingMsg
                            });
                        }

                        // 用户发送消息的场景计算完成
                        if (!withOutMsg) {
                            const responseTime = new Date().getTime();
                            const tokenTime = responseTime - startSSETime;
                            ubcCommonViewSend({
                                value: 'tokenEndTimeApi',
                                ext: {
                                    product_info: {
                                        costTime: tokenTime,
                                        msgId: getLastReply(),
                                        ...(sseStatusRefs?.lastMsgExt.current ?? {})
                                    }
                                }
                            });
                        }
                    } else {
                        // 请求建立失败
                        sseManagerOnSSEError({
                            msgKey,
                            sseId: sseInstance.id,
                            resendArg: arg,
                            msgContent: mockUserMsgContent,
                            errorContent: new Error(`接口请求失败，statusCode：${res?.status}`),
                            lastReplyId: getLastReply(),
                            hasReturnedData: sseStatusRefs.llmTokenHasStarted.current
                        });
                        console.error('conversationSSE onError 出错：', res);
                        updateCurSessionMsgIdsAtom([thinkingMsgMsgId], {type: 'delete'});

                        sseOnErrorCallback &&
                            sseOnErrorCallback?.(
                                new Error(`接口请求失败，statusCode：${res?.status}`)
                            );

                        // Tips: 手百消息中心场景，消息发送失败需要额外双写到消息中心；@wanghaoyu08
                        if (speExt?.isBdMessageCenterScene) {
                            const [msgParams] = params?.msg?.payload || [];
                            if (msgParams?.content && msgParams?.contentType) {
                                const text = transformMsg({
                                    msg:
                                        typeof msgParams.content === 'string'
                                            ? msgParams?.content
                                            : msgParams?.content?.text,
                                    type: msgParams?.contentType
                                });
                                sendSwanMsg(`${failedMsgPrefix}${text}`);
                            }
                        }
                    }
                },
                onHeadersReceived: () => {
                    setIsGeneratingAtom(true);
                    messageManagerOnSEEConnect({msgKey, msgContent: mockUserMsgContent});
                    if (!withOutThinkingMsg) {
                        managerAddThinkMsg(thinkingMsgMsgId, sseStatusRefs.lastMsgIdRef);
                    }
                    sseStatusRefs.llmTokenHasStarted.current = false;
                },
                onError: error => {
                    setIsGeneratingAtom(false);
                    const isUserInterrupted = getUserInterruptedAtom();
                    (!sseStatusRefs.sseHasConnected.current || isUserInterrupted) &&
                        sseManagerOnSSEError({
                            msgKey,
                            sseId: sseInstance.id,
                            resendArg: arg,
                            msgContent: mockUserMsgContent,
                            errorContent: error,
                            lastReplyId: getLastReply(),
                            hasReturnedData: sseStatusRefs.llmTokenHasStarted.current
                        });
                    console.error('conversationSSE onError 出错：', error);
                    updateCurSessionMsgIdsAtom([thinkingMsgMsgId], {type: 'delete'});

                    sseOnErrorCallback && sseOnErrorCallback?.(error);
                    // 执行打断ubc上报逻辑
                    ubcCommonViewSend({
                        value: 'tokenInterruptChatApi',
                        ext: {
                            product_info: {
                                costTextNum: sseStatusRefs.aiTextCount.current || 0, // 使用实际统计的字数
                                msgId: getLastReply(),
                                message: error?.message || '',
                                ...(sseStatusRefs?.lastMsgExt.current ?? {})
                            }
                        }
                    });
                    sseStatusRefs.aiTextCount.current = 0; // 重置统计
                }
            }
        });

        // Tips：明确当前场景第一个 task 为 SSE 消息发送，请勿修改；@wanghaoyu08
        const promiseTasks: Promise<SSEProcessorInstance<SSEResponseType> | unknown>[] = [sseTask];

        // Tips：手百消息中心场景，需要额外双写到消息中心；@wanghaoyu08
        if (!params.ctrlData.firstCall && speExt?.isBdMessageCenterScene) {
            const [dispatchMsg] = params?.msg?.payload || [];
            dispatchMsg.content &&
                promiseTasks.push(
                    dispatchSwanimAction({
                        msgData: dispatchMsg as unknown as CreateConversationArgs['msg']
                    })
                );
        }
        const [sseTaskVal, ..._restTasksResults] = await allSettled(promiseTasks);

        // 重置缓存消息风控命中信息；
        updateMsgRiskControlAtom(undefined);

        // 处理正常 SSE 数据流；
        if (sseTaskVal.status === 'fulfilled') {
            sseInstance = sseTaskVal.value as SSEProcessorInstance<SSEResponseType>;
            addSSEInstance(sseInstance, thinkingMsgMsgId);

            await handleSSEDataStream(sseInstance, {
                thinkingMsgMsgId,
                sseStatusRefs,
                startSSETime,
                deps
            });
        }

        // 处理其余并行 task 结果；
        if (_restTasksResults.length) {
            // eslint-disable-next-line no-console
            console.info('其余 task 结果', _restTasksResults);

            _restTasksResults.forEach(item => {
                const taskResult = item.status === 'fulfilled' ? item.value : item.reason;

                if (
                    taskResult &&
                    typeof taskResult === 'object' &&
                    'taskType' in taskResult &&
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    (taskResult as any).taskType === 'swanMsgCenter'
                ) {
                    syncSwanMsgStatus({
                        sseRes: sseTaskVal,
                        swanImRes: item
                    });
                }
            });
        }
    } catch (err: unknown) {
        setIsGeneratingAtom(false);
        ubcCommonViewSend({
            value: 'createConversationError',
            ext: {
                product_info: {
                    message: err
                }
            }
        });
        if (err instanceof Error && err.name !== 'AbortError') {
            console.error('Error in createConversation:', err);
        }
    }
};
