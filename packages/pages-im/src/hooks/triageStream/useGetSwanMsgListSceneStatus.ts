import {useMemo} from 'react';
import {useGetUrlParams} from '../common';

export const useGetSwanMsgListSceneStatus = () => {
    const {sf_ref} = useGetUrlParams();

    const curStatus = useMemo(() => {
        if (process.env.TARO_ENV === 'swan') {
            return sf_ref === 'bdapp_msg_jkgj' ? 1 : 0;
        }
        return 0;
    }, [sf_ref]);

    return {
        status: !!curStatus
    };
};
