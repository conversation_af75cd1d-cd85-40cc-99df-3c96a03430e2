import {useState, useCallback} from 'react';
import {getStorageSync, setStorageSync} from '@tarojs/taro';
import {useAtomValue} from 'jotai';

import {showBotEntranceCardAtom} from '../../store/triageStreamAtom/otherData';

export const useRetentionDialog = (storageKey: string = 'aiHealthManagerPop-realtime') => {
    // 是否展示挽留弹窗
    const [showDialog, setShowDialog] = useState(false);
    // 是否阻止返回
    const [backDetain, setBackDetain] = useState(false);
    // 返回状态和导航函数
    const [backState, setBackState] = useState('');
    const [naviFunc, setNaviFunc] = useState<{
        navigateBack?: () => void;
        navigateHome?: () => void;
            }>({});

    const isShowBotEntranceCard = useAtomValue(showBotEntranceCardAtom);

    /**
     * 初始化挽留弹窗逻辑（检查是否需要展示）
     */
    const initRetentionCheck = useCallback(() => {
        try {
            const lastTimeStamp = getStorageSync(storageKey);
            const currentDate = new Date(new Date().toLocaleDateString()).getTime();

            if (!lastTimeStamp || lastTimeStamp < currentDate) {
                if (isShowBotEntranceCard === '1') {
                    setBackDetain(true);
                    setStorageSync(storageKey, new Date().getTime());
                }
            }
        } catch (err) {
            console.error('存储操作失败:', err);
        }
    }, [storageKey, isShowBotEntranceCard]);

    /**
     * 处理返回时的挽留逻辑
     */
    const onBackDetain = useCallback(
        (eventName: string, navigateBack: () => void, navigateHome: () => void) => {
            setShowDialog(true);
            setBackState(eventName);
            setNaviFunc({navigateBack, navigateHome});
        },
        []
    );

    /**
     * 关闭挽留弹窗
     * @param isStay 是否停留在当前页面
     */
    const closeDialog = useCallback(
        (isStay: boolean) => {
            setShowDialog(false);
            setBackDetain(false);

            if (!isStay && typeof naviFunc[backState] === 'function') {
                setTimeout(() => {
                    naviFunc[backState]?.();
                }, 300);
            }
        },
        [backState, naviFunc]
    );

    return {
        showDialog,
        backDetain,
        onBackDetain,
        closeDialog,
        initRetentionCheck
    };
};
