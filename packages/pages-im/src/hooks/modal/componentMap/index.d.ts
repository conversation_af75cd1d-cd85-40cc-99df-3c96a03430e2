import type { InteractionType, InteractionInfo } from '../../../typings';

export type MultitalkModalSceneType = 'IM' | 'SERVEICE_RECORD';

export interface IProps {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
    // 枚举该组件的使用场景
    scene: MultitalkModalSceneType;
    onClosePopup?: () => void;
    // 外部控制弹窗状态
    externalOpen?: boolean;
    // 外部控制隐藏弹窗方法
    hideModal?: () => void;
    setRemindStatus?: (status: number) => void;
}