import { useAtom, useSet<PERSON>tom, type Atom } from 'jotai';
import { useCallback, useMemo, useEffect } from 'react';

import { modalAtom, initializeModalAtom, resetModalAtom, type ViewRenderModalStore } from '../../store/viewRenderAtom';

import { MODAL_COMPONENTS_MAP } from './componentMap/triageStreamMap';

type JotaiStore = {
    get: <Value>(atom: Atom<Value>) => Value;
    set: <Value>(atom: Atom<Value>, value: Value) => void;
};

// 使用 WeakMap 来跟踪每个 store 的初始化状态
const initializedStores = new WeakMap<JotaiStore, boolean>();

/**
 * @description 用于展示 Modal 组件的 hook
 */
export const useGenGlobalModalWithAtom = (store: JotaiStore) => {
    // 在 hook 初始化时绑定 atom 到指定的 store
    useEffect(() => {
        if (!initializedStores.get(store)) {
            initializeModalAtom(store);
            initializedStores.set(store, true);
        }
    }, [store]);

    const [modalStore, setModalStore] = useAtom(modalAtom);

    const resetModal = useCallback(() => {
        resetModalAtom(store);
    }, [store]);

    const { interaction, interactionInfo, modalState, cardData, msgData } = modalStore;

    /**
     * @description 通用 popup 关闭回调
     */
    const onClosePopup = useCallback(() => {
        resetModal();
    }, [resetModal]);

    /**
     * @description 返回 popup node
     */
    const modalComponent = useMemo(() => {
        if (!modalState) return null;

        const { cardId } = cardData;
        const type = interactionInfo?.sceneType;

        const modalConfig = MODAL_COMPONENTS_MAP[cardId];
        if (!modalConfig) return null;

        const props = {
            interaction,
            interactionInfo,
            onClosePopup,
            msgData
        };

        if (typeof modalConfig === 'function') {
            const ModalComponent = modalConfig;

            return <ModalComponent {...props} />;
        }

        if (type && modalConfig[type]) {
            const ModalComponent = modalConfig[type];

            return <ModalComponent {...props} />;
        }

        return null;
    }, [cardData, interaction, interactionInfo, modalState, msgData, onClosePopup]);

    return {
        modalComponent,
        onClosePopup,
        modalStore,
        setModalStore
    };
};

/**
 * @description 用于更新 Modal 状态的 hook
 */
export const useModalUpdate = (store: JotaiStore) => {
    const setModalStore = useSetAtom(modalAtom);

    const updateModalStore = useCallback(
        (newState: ViewRenderModalStore) => {
            setModalStore(newState);
        },
        [setModalStore]
    );

    const resetModalStore = useCallback(() => {
        resetModalAtom(store);
    }, [store]);

    return {
        resetModalStore,
        updateModalStore
    };
};
