import {useEffect, useRef, useCallback} from 'react';
import {eventCenter, nextTick, NodesRef, createSelectorQuery} from '@tarojs/taro';

/**
 * 跨平台点击区域判断 Hook（支持 H5 + 小程序）
 *
 * @param targetIds - 目标区域的 DOM ID 数组（每个必须唯一）
 * @param onClickOutside - 点击所有目标区域外触发的回调
 */
export function useClickOutside(targetIds: string[], onClickOutside: () => void) {
    const rectsRef = useRef<NodesRef.BoundingClientRectCallbackResult[]>([]);

    const updateRects = useCallback(() => {
        const query = createSelectorQuery();

        targetIds.forEach(id => {
            query.select(`#${id}`).boundingClientRect();
        });

        query.exec((res: any[]) => {
            rectsRef.current = res.filter(Boolean);
        });
    }, [targetIds]);

    useEffect(() => {
        updateRects(); // 初始执行一次

        const handleTouch = e => {
            const touch = e.touches?.[0] || e.changedTouches?.[0];
            if (!touch || rectsRef.current.length === 0) return;

            const {clientX, clientY} = touch;

            const clickedInsideSomeTarget = rectsRef.current.some(rect => {
                const {left, right, top, bottom} = rect;
                return clientX >= left && clientX <= right && clientY >= top && clientY <= bottom;
            });

            if (!clickedInsideSomeTarget) {
                onClickOutside();
            }
        };

        if (process.env.TARO_ENV === 'h5') {
            document?.addEventListener('touchstart', handleTouch, true);
        } else {
            eventCenter?.on('im_page_touch', handleTouch);
        }

        // 监听尺寸变化
        nextTick(() => {
            setTimeout(updateRects, 300);
        });

        return () => {
            if (process.env.TARO_ENV === 'h5') {
                document?.removeEventListener('touchstart', handleTouch, true);
            } else {
                eventCenter?.off?.('im_page_touch', handleTouch);
            }
        };
    }, [onClickOutside, updateRects]);
}
