// Author: z<PERSON><PERSON>yu03
// Date: 2025-04-10 21:46:35
// Description: 胶囊发送消息的hook

import {useCallback} from 'react';

import {updateTriageStreamMsgAtom} from '@baidu/vita-pages-im/src/store/triageStreamAtom/msg';
import type {MsgItemType} from '@baidu/vita-pages-im/src/store/triageStreamAtom/index.type';

import type {InteractionInfo} from '../../typings';
import type {GetUseractionRespType} from '../../models/services/triageStream/index.d';
import {genImMsgKey} from '../../utils';
import {useConversationDataController} from '../../hooks/triageStream/useConversationDataController';
import httpRequest from '../../utils/basicAbility/comonRequest/cui';
import {useGetSessionId, useInsertMsg} from '../../hooks/triageStream/pageDataController';

import type {SceneTypeOfParams} from '../../models/services/triageStream/sse/index.d';

export const useCapsuleToolsController = () => {
    const {mockUserMsg, cancelPreSSE, createConversation} = useConversationDataController();
    const sessionId = useGetSessionId();
    const {insertMsg} = useInsertMsg();

    /**
     * @description 更新mock数据的状体
     * @param msgContent mock数据结构
     */
    const updateMsgStatus = useCallback(
        (msgContent, msgKey) => {
            const updatedMsg: MsgItemType<unknown> = {
                ...msgContent,
                meta: {
                    ...msgContent?.meta,
                    localMsgStatus: 'success'
                }
            };

            // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
            updateTriageStreamMsgAtom(`${sessionId}_${msgKey}`, updatedMsg, {
                _debugSymbol: 'useCapsuleToolsController'
            });
        },
        [sessionId]
    );

    /**
     * @description 点击问诊tools调用接口，插入假消息
     */
    const updateWzMockMessage = async (interactionInfo: InteractionInfo) => {
        try {
            const msgKey = genImMsgKey(10);
            const m = mockUserMsg(
                {
                    type: 'text',
                    content: '我想购买在线咨询服务'
                },
                msgKey
            );

            const {url, method} = interactionInfo;
            const r = await httpRequest<GetUseractionRespType<'capsuleClick'>>({
                url: url as string,
                method,
                data: {
                    bizActionType: 'capsuleClick',
                    chatData: {
                        sessionId
                    },
                    bizActionData: {
                        clickCapsuleInfo: {
                            msgKey,
                            content: '我要直接问医生'
                        }
                    }
                }
            });
            updateMsgStatus(m, msgKey);
            cancelPreSSE('用户点击胶囊工具，主动停止SSE');
            const [err, res] = r;
            if (err) return;
            const data = res?.data;
            const {message} = data || {};

            message?.forEach(i => {
                insertMsg(i.meta.msgId, i);
            });
        } catch (error) {
            console.error(error, 'error');
        }
    };

    /**
     * @description 问诊消息发送
     */
    const updateWzMessage = useCallback(
        async (interactionInfo: InteractionInfo, ops: {sceneType: SceneTypeOfParams}) => {
            try {
                const {params = {}, sceneType = ''} = interactionInfo;
                const {payload = []} = params || {payload: []};
                let contentStr = '';
                (payload as Array<unknown>).forEach(item => {
                    if ((item as Record<string, string>)?.content) {
                        contentStr += (item as Record<string, string>)?.content;
                    }
                });
                if (contentStr) {
                    await createConversation({
                        msg: {
                            type: 'text',
                            content: contentStr,
                            sceneType: sceneType as SceneTypeOfParams || ops?.sceneType
                        }
                    });
                }
            } catch (error) {
                console.error(error);
            }
        },
        [createConversation]
    );

    return {
        updateWzMockMessage,
        updateWzMessage
    };
};
