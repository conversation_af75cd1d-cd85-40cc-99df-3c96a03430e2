@import url('@baidu/health-ui-icon/lib/styles/index.less');

// 全局点击态样式添加
.c-click-status {
    position: relative;
    display: inherit;
    transition: opacity ease 0.15s;
    overflow: hidden;
    user-select: none;
}

.c-click-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    --border-width: 1px;
    background-color: #000;
    opacity: 0;
    transform: translate(calc(var(--border-width) * -1), calc(var(--border-width) * -1));
    width: 100%;
    height: 100%;
    pointer-events: none;
    border-radius: inherit;
    box-sizing: content-box;
}

.c-click-status:active::before {
    opacity: 0.08;
}

// 解决百度小程序 flex 1 挤压不生效问题 如产生其他flex 奇怪问题到这看
swan-template {
    display: contents;
}
swan-swiper swan-template {
    display: block;
}

.taro-navigation-bar-no-icon {
    display: none !important;
}