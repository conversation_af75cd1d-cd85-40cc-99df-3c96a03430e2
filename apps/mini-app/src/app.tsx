import {PropsWithChildren} from 'react';
import {useLaunch, useError, useUnhandledRejection} from '@tarojs/taro';
import {weirwoodErrorReport, weirwoodInit} from '@baidu/vita-utils-shared'; // 封装的错误上报方法
import {utils} from '@baidu/vita-pages-im';
import {pageLeave, swanInitPage} from '@baidu/mika-ubc';

import {createErrorBoundary} from './components/ErrorBoundary';

import './app.less';

function App({children}: PropsWithChildren<any>) {
    useLaunch(info => {
        // TODO taro4 history路由版本问题，后续关注社区解决
        if (process.env.TARO_ENV === 'h5') {
            // 设置根元素 font-size
            const html = document.documentElement;
            const width = html.clientWidth;
            // 使用 Taro 的配置计算
            const designWidth = 375; // taro 默认宽度
            const baseFontSize = 20; // taro 默认字体
            const fontSize = (width * baseFontSize) / designWidth;
            html.style.fontSize = `${fontSize}px`;
        }

        if (process.env.TARO_ENV === 'swan') {
            // 修复在 手百环境下 引入lodash throttle方法报错问题
            Object.assign(global, {
                Date
            });
        }

        // mika ubc 时长埋点初始化注册
        pageLeave.init(info);
        process.env.TARO_ENV === 'swan' && swanInitPage();

        // ubc 埋点初始化注册
        utils.ubcFn.init(info);

        // 初始化错误上报
        weirwoodInit();

        // 增加版本管理
        utils.versionControl.init();
    });

    useError(err => {
        if (process.env.TARO_ENV !== 'h5') {
            // 非H5环境上报错误
            weirwoodErrorReport({error: err});
        }
    });

    useUnhandledRejection(err => {
        if (process.env.TARO_ENV !== 'h5') {
            // 非H5环境上报异常
            weirwoodErrorReport(err?.reason || {error: err, type: 'unhandledRejection'});
        }
    });

    // children 是将要会渲染的页面
    return children;
}

export default createErrorBoundary(App);
