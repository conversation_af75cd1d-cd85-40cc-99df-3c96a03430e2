{"name": "@baidu/vita-mini-app", "version": "0.0.3-dev.0", "private": false, "description": "vita-mini-app", "templateInfo": {"name": "default", "typescript": true, "css": "Less", "framework": "React"}, "files": ["/lib", "/path"], "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:swan:pkg": "rm -rf lib/ && taro build --type swan --blended", "build:h5": "taro build --type h5", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:h5": "npm run build:h5 -- --watch"}, "browserslist": {"development": ["defaults and fully supports es6-module", "maintained node versions"], "production": ["last 3 versions", "Android >= 4.1", "ios >= 8"]}, "author": "", "dependencies": {}}