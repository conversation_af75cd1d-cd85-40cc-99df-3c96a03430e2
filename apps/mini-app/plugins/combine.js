const fs = require('fs-extra');
const path = require('path');
let child = require('child_process');

const resolve = dir => path.resolve(__dirname, '../', dir);

/**
 * 编译taro配置文件
 */
const compileConfig = async () => {
    child.spawnSync('npx', ['tsc', resolve('src/app.config.ts'), '--outDir', resolve('lib')], {
        stdio: 'inherit'
    });
};

/**
 * 重命名配置文件
 */
const reNameConfig = async () => {
    const confPath = resolve('lib/app.config.js');
    if (fs.existsSync(confPath)) {
        fs.moveSync(confPath, resolve('lib/config.js'));
    }
};

export default ctx => {
    ctx.onBuildFinish(async () => {
        const blended = ctx.runOpts.blended || ctx.runOpts.options.blended;
        if (!blended) {
            return;
        }
        await compileConfig();
        await reNameConfig();
    });
};
