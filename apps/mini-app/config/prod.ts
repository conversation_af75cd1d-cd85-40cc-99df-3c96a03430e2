import path from 'path';

import type {UserConfigExport} from '@tarojs/cli';

const resolve = dir => path.resolve(__dirname, '../', dir);

const buildBaseChunk = (name, priority, modules, relativePath = 'node_modules') => {
    return {
        chunks: 'all',
        enforce: false,
        minSize: 15000,
        name,
        priority,
        test: md => {
            return modules.some(
                moduleName =>
                    md && md.resource && md.resource.includes(path.join(relativePath, moduleName))
            );
        }
    };
};

export default {
    plugins: ['@tarojs/plugin-indie', resolve('plugins/combine.js')],
    mini: {},
    h5: {
        publicPath: `${process.env.FCNAP_CDN_HOST}/${process.env.FCNAP_CDN_PATH}`,
        // publicPath: `/`,
        /**
         * WebpackChain 插件配置
         * @docs https://github.com/neutrinojs/webpack-chain
         */
        // webpackChain (chain) {
        //   /**
        //    * 如果 h5 端编译后体积过大，可以使用 webpack-bundle-analyzer 插件对打包体积进行分析。
        //    * @docs https://github.com/webpack-contrib/webpack-bundle-analyzer
        //    */
        //   chain.plugin('analyzer')
        //     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
        //   /**
        //    * 如果 h5 端首屏加载时间过长，可以使用 prerender-spa-plugin 插件预加载首页。
        //    * @docs https://github.com/chrisvfritz/prerender-spa-plugin
        //    */
        //   const path = require('path')
        //   const Prerender = require('prerender-spa-plugin')
        //   const staticDir = path.join(__dirname, '..', 'dist')
        //   chain
        //     .plugin('prerender')
        //     .use(new Prerender({
        //       staticDir,
        //       routes: [ '/pages/index/index' ],
        //       postProcess: (context) => ({ ...context, outputPath: path.join(staticDir, 'index.html') })
        //     }))
        // }

        webpackChain(chain) {
            chain.merge({
                devtool: 'source-map',
                output: {
                    devtoolModuleFilenameTemplate: info => {
                        // 只让 src 文件夹下的代码生成源码映射
                        if (info.resourcePath.includes('/apps/')) {
                            return `webpack:///${info.resourcePath}`;
                        }
                        return `webpack:///./${info.resourcePath}`;
                    }
                }
            });
            chain.merge({
                optimization: {
                    splitChunks: {
                        chunks: 'all',
                        // 大约1kb
                        minSize: 1000, // 生成 chunk 的最小体积（以 bytes 为单位）
                        maxAsyncRequests: 30, // 按需加载时的最大并行请求数
                        maxInitialRequests: 30, // 入口点的最大并行请求数
                        cacheGroups: {
                            // 拆 react-dom
                            reactDom: buildBaseChunk('reactDom', 70, ['react-dom']),
                            // 拆 其他大包
                            coreJs: buildBaseChunk('coreJs', 60, ['core-js']),
                            socketJs: buildBaseChunk('socketJs', 50, ['sockjs']),
                            // 拆react && tarojs
                            taroBase: buildBaseChunk('taroBase', 40, ['react', '@tarojs']),
                            common: {
                                test: /[\\/]node_modules[\\/]/,
                                name: 'common',
                                priority: 30,
                                reuseExistingChunk: true,
                                enforce: true
                            },
                            default: {
                                name: 'default',
                                minSize: 0,
                                minChunks: 2,
                                priority: -20,
                                reuseExistingChunk: true
                            }
                        }
                    }
                }
            });
            // chain
            //     .plugin('analyzer')
            //     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, []);
        }
    }
} satisfies UserConfigExport<'webpack5'>;
