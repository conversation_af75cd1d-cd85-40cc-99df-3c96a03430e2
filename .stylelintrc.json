{"extends": ["stylelint-config-standard"], "rules": {"at-rule-no-unknown": [true, {"ignoreAtRules": ["tailwind", "apply", "variants", "responsive", "screen", "layer"]}], "color-hex-length": "short", "color-no-invalid-hex": true, "font-family-name-quotes": "always-where-required", "font-weight-notation": "numeric", "function-url-quotes": "always", "no-duplicate-selectors": true, "no-empty-source": true, "property-no-vendor-prefix": true, "selector-no-qualifying-type": true, "selector-pseudo-element-colon-notation": "double", "selector-class-pattern": null, "font-family-no-missing-generic-family-keyword": null, "selector-type-no-unknown": [true, {"ignoreTypes": ["/^radio/"]}], "value-no-vendor-prefix": true, "no-invalid-double-slash-comments": null}}