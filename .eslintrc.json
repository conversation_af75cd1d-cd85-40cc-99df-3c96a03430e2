{
    "root": true,
    "env": {
        "browser": true,
        "es2021": true,
        "node": true
    },
    "globals": {
        "swan": true
    },
    "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:react/recommended",
        "plugin:react-hooks/recommended"
    ],
    "parser": "@typescript-eslint/parser",
    "parserOptions": {
        "ecmaVersion": "latest",
        "sourceType": "module",
        "ecmaFeatures": {
            "jsx": true
        }
    },
    "plugins": ["@typescript-eslint", "react", "react-hooks", "import"],
    "rules": {
        "@typescript-eslint/no-unused-vars": [
            "error",
            {
                "argsIgnorePattern": "^_"
            }
        ],
        "@typescript-eslint/no-explicit-any": "warn",
        "@typescript-eslint/no-var-requires": "off",
        // React/Hooks规范
        // 检查 Hooks 的使用规则
        "react-hooks/rules-of-hooks": "error",
        "react/forbid-prop-types": 2,
        "react/jsx-uses-react": "off",
        "react/react-in-jsx-scope": "off",
        "react/jsx-no-bind": 0,
        "react/no-array-index-key": 0,
        // 代码格式规范
        "indent": [
            2,
            4,
            {
                "SwitchCase": 1
            }
        ],
        "import/order": [
            "error",
            {
                "groups": ["builtin", "external", "internal", "parent", "sibling", "index"],
                "pathGroups": [
                    {
                        "pattern": "@/**",
                        "group": "internal"
                    }
                ]
            }
        ],
        "quotes": ["error", "single"],
        "jsx-quotes": ["error", "prefer-single"],
        "object-curly-spacing": ["error", "never"],
        "space-before-function-paren": [
            "error",
            {
                "anonymous": "always",
                "named": "never",
                "asyncArrow": "always"
            }
        ],
        "arrow-parens": [2, "as-needed"],
        "max-len": [
            "error",
            120,
            {
                "ignoreUrls": true
            }
        ],
        "comma-dangle": 0,
        // 最佳实践与错误处理
        "no-unsafe-optional-chaining": "error",
        "no-cond-assign": "error",
        "no-param-reassign": 2,
        "eqeqeq": ["error", "always", {"null": "ignore"}],
        "no-console": ["warn", {"allow": ["error"]}], // 允许使用console.error()
        // 复杂度与代码质量
        "complexity": [1, {"max": 30}],
        "max-depth": [1, 4],
        "max-statements-per-line": 1,
        // 变量与作用域
        "no-underscore-dangle": 0,
        // 其他
        "radix": 1,
        "new-cap": 1,
        "@babel/object-curly-spacing": 0,
        "array-bracket-spacing": 1,
        "operator-linebreak": "off",
        "@reskript/hooks-deps-new-line": "off",
        "camelcase": "off",
        "no-redeclare": 2,
        "no-duplicate-imports": "error",
        "no-var": "error",
        "no-undef": "error",
        "no-debugger": "error",
        "no-unused-expressions": "off",
        "import/newline-after-import": "off"
    },
    "settings": {
        "react": {
            "version": "detect"
        },
        "import/resolver": {
            "typescript": {
                "alwaysTryTypes": true
            }
        }
    }
}
