#!/usr/bin/env bash

MOD_NAME="vita-mini-app"
OUTPUT='./output'

echo "node $(node -v)"
echo "pnpm $(pnpm -v)"

API_HOST=$apiHost
FCNAP_ENV=$FCNAP_ENV

echo "API_HOST: $API_HOST"
echo "FCNAP_ENV: $FCNAP_ENV"

before() {
    rm -rf $OUTPUT
    mkdir 'output'
    # 设置环境变量
    if [ -n "$API_HOST" ]; then
        # 追加换行
        echo >>./apps/mini-app/.env.production
        # 追加 变量
        echo "TARO_APP_API_HOST=$API_HOST" >>./apps/mini-app/.env.production
    elif [ -n "$FCNAP_ENV" ]; then
        echo >>.env.production
        echo "TARO_APP_HEADER_BAGGAGE=$FCNAP_ENV" >>./apps/mini-app/.env.production
    fi
}

# 前端代码编译
build_fe() {
    echo '*******static resource compile begin*******'
    pnpm install
    pnpm mini:build:h5
    echo '*******static resource compile end*******'
}

after() {
    mv ./apps/mini-app/dist/h5/* $OUTPUT
}

before
build_fe
after
