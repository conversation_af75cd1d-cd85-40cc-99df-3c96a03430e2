#!/usr/bin/env bash

MOD_NAME="vita-mini-app"
OUTPUT='./output'

readonly JOBDIR=$(pwd)
readonly OUTPUT_DIR=${JOBDIR}/sourcemap

echo "node $(node -v)"
echo "pnpm $(pnpm -v)"

before() {
    rm -rf $OUTPUT
    mkdir 'output'
}

# 前端代码编译
build_fe() {
    echo '*******static resource compile begin*******'
    pnpm install
    pnpm mini:build:h5
    echo '*******static resource compile end*******'
}

# 核心代码：获取提交 HASH，拼接【日期 + buildId 前 7 位】，生成示例: 2021-04-26-9472769
# 也可以自定义，符合 buildid 规范即可
revision=${AGILE_REVISION}
buildId=$(date '+%Y-%m-%d-%H-%M')
stage=${1:-offline}
weirwoodJsonPath=./packages/utils-shared/weirwood/weirwood.json

# 修改weirwood.json的版本
upateWeirwoodBuildVersion() {
    echo '*******update build vension begin********'
    # 核心代码：替换 <buildid> 为当前构建版本
    sed -i "s/<buildid>/${buildId}/g" $weirwoodJsonPath
    cat $weirwoodJsonPath
    echo '*******update build vension end********'
}
# 上传sourcemap
uploadSourcemap() {
    echo '*******sourcemap  begin ********'
    mkdir sourcemap

    # 将构建产出和 weirwood.json 都拷贝到输出目录
    cp -r ./apps/mini-app/dist/h5/js/* ./sourcemap
    cp -r $weirwoodJsonPath ./sourcemap 

    # 核心代码：压缩所有 sourcemap，注意不能包含文件夹，必须扁平压缩
    cd sourcemap
    tar -czf sourcemap.tar.gz *.js *.js.map
    # # 核心代码：使用 npx 下载 @baidu/weirwood-cli 进行上传，这里需要注意 sourcemap.tar.gz 和 weirwood.json 在同一个目录
    npx @baidu/weirwood-cli -c weirwood.json
    # 核心代码： 删除所有 map 文件和 map 压缩包
    rm sourcemap.tar.gz
    cd ..
    rm -rf sourcemap
    echo '*******sourcemap end ********'
}

after() {
    mv ./apps/mini-app/dist/h5/* $OUTPUT
}
# run
before

upateWeirwoodBuildVersion

build_fe

# 检测如果是上线环境，需要上传 sourcemap
# if [ "$stage" = "online" ]; then
    uploadSourcemap
# fi

# toTaz
after
