# 群通知
export npmName=$1
export packageVersion=$2
export ipipeLink=$3
curl -X POST 'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d49cdd872c814911758b5322f6e528aec' \
   -H "Content-Type:application/json" \
   -d '{
    "message": {
        "body": [
            {
                "type": "TEXT",
                "content": "【npm发版通知】\n'${AGILE_TRIGGER_USER}'发布'${npmName}'版本号为：'${packageVersion}'\n"
            },
            {
                "href":"'${ipipeLink}'",
                "type":"LINK"
            },
            {
               "atuserids":["'${AGILE_TRIGGER_USER}'"],
                "atall": false, 
                "type":"AT"
            }
        ]
     }
  }'

  