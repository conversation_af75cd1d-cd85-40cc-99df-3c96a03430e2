{"compilerOptions": {"target": "es2017", "module": "commonjs", "removeComments": false, "preserveConstEnums": true, "moduleResolution": "node", "experimentalDecorators": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "outDir": "lib", "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "sourceMap": true, "rootDir": ".", "jsx": "react-jsx", "allowJs": true, "resolveJsonModule": true, "typeRoots": ["node_modules/@types", "typings", "packages/*/src/typings"], "paths": {"@/*": ["./src/*"]}, "plugins": [{"name": "typescript-plugin-css-modules"}]}, "include": ["apps/**/*.ts", "apps/**/*.tsx", "packages/**/*.ts", "packages/**/*.tsx", "types/**/*.ts", "types/**/*.tsx", "config/**/*.ts", "config/**/*.tsx"], "compileOnSave": false}