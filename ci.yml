Global:
    version: 2.0
    group_email: yumi<PERSON><PERSON>@baidu.com

Default:
    profile: [buildDev]
Profiles:
    - profile:
      name: buildDev
      mode: AGENT
      artifacts:
          release: true
      environment:
          image: DECK_STD_CENTOS7
          tools:
              - nodejs: 20.latest
              - pnpm: 9.6.0
      build:
          command: sh ./scripts/mini-app-h5/build_dev_h5.sh
    - profile:
      name: buildProd
      mode: AGENT
      artifacts:
          release: true
      environment:
          image: DECK_STD_CENTOS7
          tools:
              - nodejs: 20.latest
              - pnpm: 9.6.0
      build:
          command: sh ./scripts/mini-app-h5/build_prod_h5.sh
    - profile:
      name: buildNpm
      mode: AGENT
      artifacts:
          release: true
      environment:
          image: DECK_STD_CENTOS7
          tools:
            - nodejs: 20.latest
            - yarn: 1.22.4
            - pnpm: 9.6.0
      build:
        command: sh scripts/mini-app-npm/build_npm.sh
